import{_ as R}from"./BWS-stcc.js";import{_ as A}from"./jBNyKXjo.js";import J from"./sXr78Ku-.js";import{_ as K}from"./_cPjsZyH.js";import{d as G,f as Q,u as W,a as X,i as Y,r as x,j as y,k as Z,c as m,o as p,l,w as v,e as n,v as ee,b as t,s as w,F as k,n as N,J as te,K as V,y as f,q as oe,H as S,t as d}from"./D_zVjEfm.js";import re from"./HHVucgb9.js";import{_ as ae}from"./CF83mHfB.js";import"./G4Mf9Qin.js";const ne={class:"row"},se={key:0,class:"col-3 text-start"},le={class:"table-responsive-md"},ie={class:"table table-striped table-hover table-bordered table-fixed"},de={style:{"text-align":"center"},width:"100"},ue={valign:"top",title:"Edit",style:{"text-align":"center"}},me=["href"],pe=["href","onClick"],ce=["href"],ke=G({__name:"index",props:{},emits:["closedDialog"],setup(z){const _=Q();W(),X();const h=Y(),b=x(),g=z;let s=y({data:[]});x("");const a=y({search:"",searchname:"",searchdatename:"",page:1,pagesize:10,sortname:"",sorttype:""});y({x:""});const I=async o=>{a.page=o,await u()},i=async o=>{a.page=1,a.sortname=o,a.sorttype=a.sorttype=="asc"?"desc":"asc",await u()},L=async o=>{a.page=1,a.sortname="",Object.assign(a,o),await u()},T=async(o=!1)=>{o&&await u()},j=async o=>{s.data.forEach(e=>{e.del=o?e.id:0})},B=async()=>{try{if(a.del=s.data.filter(e=>e.del!==0).map(e=>e.del).filter(e=>e!=null)||[],a.del.length===0)throw new Error("Please select an item to delete.");const o=await _.post("api/admin/ordergroup/destroy",{id:g.edit});if(o.resultcode=="0")u();else throw new Error(o.resultmessage)}catch(o){f.alert(o.message,"Error","error"),console.error(o)}},u=async()=>{try{const o=await _.post("api/admin/ordergroup",{id:g.edit});if(o.resultcode=="0")Object.assign(s,o.data);else throw new Error(o.resultmessage)}catch(o){f.alert(o.message,"Error","error"),console.error(o)}};return Z(async()=>{await u()}),(o,e)=>{var D,$;const O=R,P=A,U=J,q=K,C=te,F=re,H=ee;return p(),m(k,null,[l(O,{title:n(s).title,defaultTitle:""},null,8,["title"]),v((p(),m("div",null,[l(P,{id:"ordergroup",refs:"breadcrumb"}),l(U,{searchNames:{"":"全部","ordergroup.ordergroupnumber":"訂單編號","ordergroup.rname":"您的姓名","ordergroup.rcompany":"公司名稱","ordergroup.rline_id":"您的LINE ID","ordergroup.rmobile":"您的手機","ordergroup.remail":"您的信箱","ordergroup.rpurpose":"採購目的","ordergroup.requirements":"您的需求","ordergroup.clientip":"IP","ordergroup.rmemo":"備註","ordergroup.adminuser_account":"操作者帳號"},searchDateNames:{"ordergroup.created_at":"建立日期"},onOnSearch:L}),t("div",ne,[[999].includes((D=n(h).adminuser)==null?void 0:D.role)?(p(),m("div",se,[l(q,{onClick:B})])):w("",!0),e[9]||(e[9]=t("div",{class:"col-6"},null,-1)),e[10]||(e[10]=t("div",{class:"col-3 text-end noprint"},null,-1))]),n(s).data.length==0?(p(),m(k,{key:0},[N(" No Data ")],64)):w("",!0),t("div",le,[t("table",ie,[t("thead",null,[t("tr",null,[t("th",de,[v(l(C,{onChange:j},null,512),[[V,[999].includes(($=n(h).adminuser)==null?void 0:$.role)]])]),t("th",{width:"",onClick:e[0]||(e[0]=r=>i("ordergroupnumber")),class:"sortable"},"訂單編號"),t("th",{width:"",onClick:e[1]||(e[1]=r=>i("rname")),class:"sortable"},"您的姓名"),t("th",{width:"",onClick:e[2]||(e[2]=r=>i("rcompany")),class:"sortable"},"公司名稱"),t("th",{width:"",onClick:e[3]||(e[3]=r=>i("rline_id")),class:"sortable"},"您的LINE ID"),t("th",{width:"",onClick:e[4]||(e[4]=r=>i("rmobile")),class:"sortable"},"您的手機"),t("th",{width:"",onClick:e[5]||(e[5]=r=>i("remail")),class:"sortable"},"您的信箱"),t("th",{width:"",onClick:e[6]||(e[6]=r=>i("created_at")),class:"sortable"},"建立日期")])]),t("tbody",null,[("utils"in o?o.utils:n(f)).isEmpty(n(s).data)==!1?(p(!0),m(k,{key:0},oe(n(s).data,(r,M)=>{var E;return p(),m("tr",{key:M},[t("td",ue,[v(l(C,{modelValue:r.del,"onUpdate:modelValue":c=>r.del=c,"true-value":r.id,value:r.id},null,8,["modelValue","onUpdate:modelValue","true-value","value"]),[[V,[999].includes((E=n(h).adminuser)==null?void 0:E.role)]]),e[12]||(e[12]=N("   ")),t("a",{href:`/admin/ordergroup/edit?edit=${r.id}`,class:"btn btn-success btn-sm",onClick:e[7]||(e[7]=S(c=>b.value.open(c.currentTarget.href,{...g},{title:"詢價表單"}),["prevent"]))},e[11]||(e[11]=[t("i",{class:"fa fa-edit","aria-hidden":"true"},null,-1)]),8,me)]),t("td",null,[t("a",{href:`/admin/ordergroup/show?id=${r.id}`,onClick:S(c=>b.value.open(c.currentTarget.href,{...g},{title:"詢價表單"+r.ordergroupnumber}),["prevent"])},d(r.ordergroupnumber),9,pe)]),t("td",null,d(r.rname),1),t("td",null,d(r.rcompany),1),t("td",null,d(r.rline_id),1),t("td",null,d(r.rmobile),1),t("td",null,[t("a",{href:"mailto:"+r.remail},d(r.remail),9,ce)]),t("td",null,d(("utils"in o?o.utils:n(f)).formatDate(r.created_at)),1)])}),128)):w("",!0)])])]),l(F,{total:n(s).total,page:a.page,"onUpdate:page":e[8]||(e[8]=r=>a.page=r),pagesize:a.pagesize?a.pagesize:10,onCurrentChange:I},null,8,["total","page","pagesize"]),l(ae,{ref_key:"myDialog",ref:b,onClosedDialog:T},null,512)])),[[H,n(_).getLoading()]])],64)}}});export{ke as default};
