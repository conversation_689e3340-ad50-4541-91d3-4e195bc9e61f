import{d as y,ak as u,al as h,am as v,a7 as l,an as _,c as k,o as i,F as b,b as z,a9 as w,B as C,s as N,P as S,ao as q,_ as B}from"./D_zVjEfm.js";const P={class:"container-fluid p-1"},x=y({__name:"my-pagination",props:u({total:{type:Number,required:!0,default:0},pagercount:{type:Number,default:5},pagesize:{type:Number,required:!1,default:10}},{page:{required:!0,type:Number,default:1},pageModifiers:{}}),emits:u(["current-change"],["update:page"]),setup(a,{emit:p}){const{width:d}=h(),o=p,n=v(a,"page"),r=a,g=l(()=>d.value<768?"small":"default"),m=async e=>{o("current-change",e),window.scrollTo(0,0);try{document.querySelector(".el-main").scrollTop=0}catch{}try{const t=document.querySelector('a[name="top"]');t&&t.scrollIntoView({behavior:"smooth",block:"start"})}catch{}},s=l({get(){return r.total},set:e=>{o("current-change",e)}});return _((e,t,c)=>{throw new Error("my-pagination:"+e.message+`
Stack trace:`+e.stack)}),(e,t)=>{const c=q;return i(),k(b,null,[z("div",P,[s.value>0?(i(),C(c,S({key:0,"current-page":n.value,"onUpdate:currentPage":t[0]||(t[0]=f=>n.value=f)},r,{background:"","page-size":a.pagesize,size:g.value,style:{"justify-content":"right"},layout:"total,prev, pager, next",total:s.value,"pager-count":a.pagercount,onCurrentChange:m}),null,16,["current-page","page-size","size","total","pager-count"])):N("",!0)]),w(e.$slots,"default",{},void 0,!0)],64)}}}),M=B(x,[["__scopeId","data-v-ded8d01a"]]);export{M as default};
