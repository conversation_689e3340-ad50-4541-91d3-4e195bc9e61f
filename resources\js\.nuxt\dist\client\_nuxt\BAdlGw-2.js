import{d as Y,V as Z,f as H,i as ee,g as te,j as x,r as C,k as ae,a7 as se,Q as ne,M as oe,c as i,o as a,l as s,m as t,w as I,aE as le,B as u,aF as ce,aG as re,b as n,F as h,q as S,aH as ie,t as d,v as ue,e as g,K as de,aI as _e,s as me,aJ as pe,n as ve,T as fe,H as he,N as ge,O as we,y as w,aK as ye,a9 as ke,aL as Ee,U as be,z as xe,_ as Ce}from"./D_zVjEfm.js";const Se={class:"common-layout"},Me={class:"menu-title"},Fe={class:"menu-title"},Be={class:"menu-title"},Le={class:"menu-title"},Ne={class:"row"},Ve={class:"col-9 text-start"},qe={class:"col-3 ms-auto text-end"},De={class:"card card-success"},He={class:"card-body"},Ie=Y({__name:"admin",setup(Pe){Z({title:"管理介面",script:[{src:"/ckeditor/ckeditor.js",crossorigin:"anonymous",defer:!0,async:!0}]});const P=H(),M=H(),l=ee(),T=te(),z=x({bgcolor:"#304156"}),F=x({}),U=x({}),j=async()=>{try{let e=await M.post("api/admin/main",U);if(e.resultcode=="0")Object.assign(F,e.data),l.adminuser.navs=e.data.navs;else throw new Error(e.resultmessage)}catch(e){w.formElError(e),console.error(e)}finally{}};C(""),ae(()=>{j(),L(),window.addEventListener("resize",L)});const o=C(!1),_=C(!1),B=e=>{const c=O(e);be({path:"/admin/"+e,query:c},{}),N()},O=e=>{const c=e.split("?")[1],m=new URLSearchParams(c),p={};for(const[y,k]of m)p[y]=k;return p},$=se(()=>_.value&&!o.value),L=()=>{const e=_.value;_.value=window.innerWidth<768,!e&&_.value?o.value=!0:o.value=!1},K=()=>{o.value=!o.value},N=async()=>{_.value&&(await xe(),o.value=!0)},R=async()=>{try{let e=await M.post("api/admin/logout",{});if(e.resultcode=="0")l.adminuser.api_token="",l.adminuser.name="",l.adminuser.role="";else throw new Error(e.resultmessage)}catch(e){w.formElError(e),console.error(e)}T.push("/admin"),w.toast("登出成功")};return ne(()=>l.adminuser.api_token,async e=>{},{deep:!0}),(e,c)=>{const m=re,p=ie,y=ce,k=le,V=ge,q=fe,A=oe("SwitchButton"),G=pe,J=Ee,Q=ye,D=_e,W=ue;return a(),i("div",Se,[s(D,null,{default:t(()=>[I(s(k,{width:"200px"},{default:t(()=>[I((a(),u(y,{"background-color":z.bgcolor,"text-color":"#FFFFFF","active-text-color":"#409eff",class:"el-menu-vertical-demo","default-active":"2"},{default:t(()=>[s(m,{index:"1"},{title:t(()=>c[0]||(c[0]=[n("span",{class:"menu-title"}," 管理介面",-1)])),_:1}),(a(!0),i(h,null,S(F.data,(v,f)=>(a(),u(p,{index:String(f)},{title:t(()=>[n("span",Me,d(v.title),1)]),default:t(()=>[(a(!0),i(h,null,S(v.data,(r,E)=>(a(),i(h,null,[r.data&&r.data.length>0?(a(),u(p,{key:0,index:f+"-"+E},{title:t(()=>[n("span",Fe,d(r.title),1)]),default:t(()=>[(a(!0),i(h,null,S(r.data,(b,X)=>(a(),u(m,{index:f+"-"+E+"-"+X,onClick:Te=>B(b.url)},{default:t(()=>[n("span",Be,d(b.title),1)]),_:2},1032,["index","onClick"]))),256))]),_:2},1032,["index"])):(a(),u(m,{key:1,index:f+"-"+E,onClick:b=>B(r.url)},{default:t(()=>[n("span",Le,d(r.title),1)]),_:2},1032,["index","onClick"]))],64))),256))]),_:2},1032,["index"]))),256))]),_:1},8,["background-color"])),[[W,g(P).getLoading()]])]),_:1},512),[[de,!o.value]]),s(D,null,{default:t(()=>[$.value?(a(),i("div",{key:0,class:"overlay",onClick:N})):me("",!0),s(G,{class:"header-content"},{default:t(()=>{var v;return[n("div",Ne,[n("div",Ve,[s(q,{onClick:he(K,["prevent"]),class:"toggle-collapse-btn"},{default:t(()=>[s(V,{size:20},{default:t(()=>[(a(),u(we(o.value?"Expand":"Fold")))]),_:1})]),_:1}),ve(" "+d(g(l).adminuser.name)+"( "+d(("utils"in e?e.utils:g(w)).getXmlSearch("角色",(v=g(l).adminuser)==null?void 0:v.role))+" ) ",1)]),n("div",qe,[s(q,{onClick:R,class:"logout-btn"},{default:t(()=>[s(V,{size:20},{default:t(()=>[s(A)]),_:1})]),_:1})])])]}),_:1}),s(Q,{class:"content p-3"},{default:t(()=>[n("div",De,[n("div",He,[s(J),ke(e.$slots,"default",{},void 0,!0)])])]),_:3})]),_:3})]),_:3})])}}}),Ue=Ce(Ie,[["__scopeId","data-v-82d4bcd3"]]);export{Ue as default};
