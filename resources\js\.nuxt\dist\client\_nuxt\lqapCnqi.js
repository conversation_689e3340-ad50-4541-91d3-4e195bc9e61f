import{d as n,u as r,a as c,r as d,c as l,o as i,b as t,t as _,e as u,_ as p}from"./D_zVjEfm.js";const m={class:"container mt-5"},f={class:"row justify-content-center"},v={class:"col-sm-12 col-lg-6"},b={class:"card text-center shadow-lg"},x={class:"card-body"},g={class:"card-text text-muted"},h=n({__name:"[...slug]",setup(k){const e=r(),o=c(),a=d(o.fullPath);return(y,s)=>(i(),l("div",m,[t("div",f,[t("div",v,[t("div",b,[s[3]||(s[3]=t("div",{class:"card-header bg-warning text-dark"},[t("h1",null,"404 錯誤")],-1)),t("div",x,[s[1]||(s[1]=t("h5",{class:"card-title"},"找不到頁面",-1)),s[2]||(s[2]=t("p",{class:"card-text"},"您所尋找的頁面不存在或已被移除。",-1)),t("p",g,"頁面位置: "+_(a.value),1),t("button",{class:"btn btn-primary mt-3",onClick:s[0]||(s[0]=w=>u(e).back())},"返回")])])])])]))}}),C=p(h,[["__scopeId","data-v-0a19c4bd"]]);export{C as default};
