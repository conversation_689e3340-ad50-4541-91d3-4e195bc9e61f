<!DOCTYPE html><html  data-capo=""><head><meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="stylesheet" href="/_nuxt/entry.BGE7dmog.css" crossorigin>
<link rel="modulepreload" as="script" crossorigin href="/_nuxt/D_zVjEfm.js">
<link rel="prefetch" as="script" crossorigin href="/_nuxt/D89mYBQu.js">
<link rel="prefetch" as="script" crossorigin href="/_nuxt/D4E4NNmG.js">
<link rel="prefetch" as="script" crossorigin href="/_nuxt/BdSOIv-e.js">
<link rel="prefetch" as="script" crossorigin href="/_nuxt/DkONAkuC.js">
<link rel="prefetch" as="script" crossorigin href="/_nuxt/k0vJOLy_.js">
<link rel="prefetch" as="script" crossorigin href="/_nuxt/B4My3aCe.js">
<link rel="prefetch" as="script" crossorigin href="/_nuxt/DSoKeBb0.js">
<link rel="prefetch" as="style" crossorigin href="/_nuxt/admin.Df1HJPlM.css">
<link rel="prefetch" as="script" crossorigin href="/_nuxt/BAdlGw-2.js">
<link rel="prefetch" as="script" crossorigin href="/_nuxt/DWwBub4e.js">
<link rel="prefetch" as="script" crossorigin href="/_nuxt/4qmtJa-f.js">
<link rel="prefetch" as="script" crossorigin href="/_nuxt/D4zfHrzP.js">
<script type="module" src="/_nuxt/D_zVjEfm.js" crossorigin></script></head><body><div id="__nuxt"><div class="loader"></div>
<style>
    :root {
        --loading-color-0: #4285F4;    /* 0% 藍色 */
        --loading-color-25: #DB4437;   /* 25% 紅色 */
        --loading-color-50: #F4B400;   /* 50% 黃色 */
        --loading-color-100: #0F9D58;  /* 100% 綠色 */
        --loading-size: 80px;
        --loading-border-width: 6px;
        --loading-speed: 1.5s;
    }

    .loader {
        position: fixed;
        top: calc(50% - var(--loading-size) / 2);
        left: calc(50% - var(--loading-size) / 2);
        z-index: 1031;
        width: var(--loading-size);
        height: var(--loading-size);
        box-sizing: border-box;
        border: solid var(--loading-border-width) transparent;
        border-radius: 50%;
        -webkit-animation: loading var(--loading-speed) linear infinite;
        animation: loading var(--loading-speed) linear infinite;
    }

    @-webkit-keyframes loading {
        0% {
            -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
            border-top-color: var(--loading-color-0);
            border-left-color: var(--loading-color-0);
            border-bottom-color: var(--loading-color-0);
        }
        25% {
            border-top-color: var(--loading-color-25);
            border-left-color: var(--loading-color-25);
            border-bottom-color: var(--loading-color-25);
        }
        50% {
            border-top-color: var(--loading-color-50);
            border-left-color: var(--loading-color-50);
            border-bottom-color: var(--loading-color-50);
        }
        100% {
            -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
            border-top-color: var(--loading-color-100);
            border-left-color: var(--loading-color-100);
            border-bottom-color: var(--loading-color-100);
        }
    }

    @keyframes loading {
        0% {
            transform: rotate(0deg);
            border-top-color: var(--loading-color-0);
            border-left-color: var(--loading-color-0);
            border-bottom-color: var(--loading-color-0);
        }
        25% {
            border-top-color: var(--loading-color-25);
            border-left-color: var(--loading-color-25);
            border-bottom-color: var(--loading-color-25);
        }
        50% {
            border-top-color: var(--loading-color-50);
            border-left-color: var(--loading-color-50);
            border-bottom-color: var(--loading-color-50);
        }
        100% {
            transform: rotate(360deg);
            border-top-color: var(--loading-color-100);
            border-left-color: var(--loading-color-100);
            border-bottom-color: var(--loading-color-100);
        }
    }
</style></div><div id="teleports"></div><script type="application/json" data-nuxt-data="nuxt-app" data-ssr="false" id="__NUXT_DATA__">[{"prerenderedAt":1,"serverRendered":2},1750342500313,false]</script>
<script>window.__NUXT__={};window.__NUXT__.config={public:{API_BASE:"https://demo2.881.tw/",GA4_ID:"",GTM_ID:"",RECAPTCHA_ID:"6LeOvsYqAAAAAAhnUJwS2WYz_boMt0ovn9pOtlsQ"},app:{baseURL:"/",buildId:"a124ef09-c57f-4f66-9ab1-eac32c1b2ec4",buildAssetsDir:"/_nuxt/",cdnURL:""}}</script></body></html>