import{d as V,V as h,i as x,f as E,r as f,j as _,k as B,c as C,o as g,b as o,w as S,v as q,e as l,B as I,m as r,t as U,l as n,E as D,C as H,W as N,X as T,J as j,n as z,y as c,H as F,I as L,U as M,_ as R}from"./D_zVjEfm.js";import{u as J}from"./CKhpvNlx.js";const O={class:"login-container"},W={class:"login-box"},X={class:"title-container"},$={class:"title"},A={class:"form-content"},G={class:"login-options"},K={class:"d-grid"},P=V({__name:"index",setup(Q){h({title:"管理介面",link:[]});const s=x(),p=E();f([]);const d=f(null),t=_({account:s.adminuser.iskeep?s.adminuser.account:"",password:"",iskeep:!!s.adminuser.iskeep,google_recaptcha_token:""}),{getRecaptchaToken:k}=J();_({});const b=async()=>{if(d.value)try{if(await d.value.validate())try{s.adminuser.api_token="",t.google_recaptcha_token=await k("submit");let e=await p.post("api/adminlogin",t);if(e.resultcode=="0")Object.assign(s.adminuser,e.data),s.adminuser.iskeep=t.iskeep,t.iskeep?s.adminuser.account=t.account:s.adminuser.account="",document.querySelector(".grecaptcha-badge").style.visibility="hidden",c.toast(e.resultmessage),M({path:"/admin/adminuserloginlog",query:{}});else throw new Error(e.resultmessage)}catch(e){c.formElError(e),console.error(e)}}catch(i){console.error("Validation error:",i)}};return B(()=>{}),(i,e)=>{const m=H,u=D,v=j,w=L,y=q;return g(),C("div",O,[o("div",W,[S((g(),I(w,{ref_key:"formEl",ref:d,model:t,class:"login-form",autocomplete:"on","label-position":"left",onSubmit:F(b,["prevent"])},{default:r(()=>[o("div",X,[o("h1",$,U(l(s).config.name),1),e[4]||(e[4]=o("p",{class:"subtitle"},"歡迎回來，請登入您的帳號",-1))]),o("div",A,[n(u,{prop:"account",rules:[{required:!0,message:"請輸入帳號"}]},{default:r(()=>[n(m,{modelValue:t.account,"onUpdate:modelValue":e[0]||(e[0]=a=>t.account=a),placeholder:"請輸入帳號","prefix-icon":l(N),size:"large"},null,8,["modelValue","prefix-icon"])]),_:1}),n(u,{prop:"password",rules:[{required:!0,message:"請輸入密碼"}]},{default:r(()=>[n(m,{modelValue:t.password,"onUpdate:modelValue":e[1]||(e[1]=a=>t.password=a),type:"password",placeholder:"請輸入密碼","prefix-icon":l(T),"show-password":"",size:"large"},null,8,["modelValue","prefix-icon"])]),_:1}),o("div",G,[n(u,{prop:"iskeep"},{default:r(()=>[n(v,{modelValue:t.iskeep,"onUpdate:modelValue":e[2]||(e[2]=a=>t.iskeep=a)},{default:r(()=>e[5]||(e[5]=[z("記住帳號")])),_:1},8,["modelValue"])]),_:1})]),e[6]||(e[6]=o("div",{class:"d-grid"},[o("button",{type:"submit",class:"btn btn-primary btn-block"},"登入")],-1)),e[7]||(e[7]=o("p",null,null,-1)),o("div",K,[o("button",{type:"button",class:"btn btn-light btn-block",onClick:e[3]||(e[3]=a=>("utils"in i?i.utils:l(c)).location("../"))},"返回首頁")])])]),_:1},8,["model"])),[[y,l(p).getLoading()]])])])}}}),ee=R(P,[["__scopeId","data-v-a9872554"]]);export{ee as default};
