import{_ as F}from"./jBNyKXjo.js";import H from"./sXr78Ku-.js";import{_ as P}from"./_cPjsZyH.js";import{_ as X}from"./CQvHppDm.js";import{d as q,f as J,g as K,i as R,r as C,j as $,k as z,c as p,o as m,l as r,w as D,v as G,e as l,b as a,s as E,J as Q,K as W,F as N,q as Y,B as Z,n as ee,H as te,t as i,y as d}from"./D_zVjEfm.js";import ae from"./HHVucgb9.js";import{_ as ne}from"./CF83mHfB.js";import"./G4Mf9Qin.js";const se={class:"d-flex justify-content-between"},oe={key:0},le={class:"table-responsive-md"},ie={class:"table table-striped table-hover table-bordered table-fixed"},re={align:"center",width:"90"},de={valign:"top",title:"Edit",align:"center"},ue=["href"],me=["href"],ye=q({__name:"index",props:{},emits:["closedDialog"],setup(S){const g=J();K();const f=R(),h=C(),b=S,c=$({data:[]});C("");const s=$({search:"",searchname:"",searchdatename:"",page:1,sortname:"",sorttype:"",del:[]}),V=async t=>{s.page=t,await u()},o=async t=>{s.page=1,s.sortname=t,s.sorttype=s.sorttype=="asc"?"desc":"asc",await u()},B=async t=>{s.page=1,s.sortname="",Object.assign(s,t),await u()},L=async(t=!1)=>{t&&await u()},j=async t=>{c.data.forEach(e=>{e.del=t?e.id:0})},I=async()=>{try{if(s.del=c.data.filter(e=>e.del!==0).map(e=>e.del).filter(e=>e!==null),s.del.length===0)throw new Error("Please select an item to delete.");let t=await g.post("api/admin/adminuser/destroy",s);if(t.resultcode=="0")d.toast(t.resultmessage),await u();else throw new Error(t.resultmessage)}catch(t){d.formElError(t),console.error(t)}},u=async()=>{try{let t=await g.post("api/admin/adminuser",s);if(t.resultcode=="0")Object.assign(c,t.data);else throw new Error(t.resultmessage)}catch(t){d.message(t.message),console.error(t)}};return z(async()=>{await u()}),(t,e)=>{var w,y;const M=F,T=H,x=P,A=X,v=Q,O=ae,U=G;return m(),p(N,null,[r(M,{id:"adminuser",refs:"breadcrumb"}),D((m(),p("div",null,[r(T,{searchNames:{"":"全部","adminuser.account":"帳號","adminuser.name":"姓名","adminuser.email":"EMAIL","adminuser.role|INT":"角色","adminuser.online|INT":"是否核可"},searchDateNames:{"adminuser.lastlogin_dt":"最後登入時間","adminuser.created_at":"建立日期"},onOnSearch:B}),a("div",se,[[999].includes((w=l(f).adminuser)==null?void 0:w.role)?(m(),p("div",oe,[r(x,{onClick:I})])):E("",!0),e[11]||(e[11]=a("div",null,null,-1)),a("div",null,[r(A,{onClick:e[0]||(e[0]=n=>h.value.open("admin/adminuser/edit",{...b},0))})])]),a("div",le,[a("table",ie,[a("thead",null,[a("tr",null,[a("th",re,[D(r(v,{onChange:j},null,512),[[W,[999].includes((y=l(f).adminuser)==null?void 0:y.role)]])]),a("th",{width:"",onClick:e[1]||(e[1]=n=>o("account")),class:"sortable"},"帳號"),a("th",{width:"",onClick:e[2]||(e[2]=n=>o("name")),class:"sortable"},"姓名"),a("th",{width:"",onClick:e[3]||(e[3]=n=>o("email")),class:"sortable"},"EMAIL"),a("th",{width:"",onClick:e[4]||(e[4]=n=>o("role")),class:"sortable"},"角色"),a("th",{width:"",onClick:e[5]||(e[5]=n=>o("lastlogin_dt")),class:"sortable"},"最後登入時間"),a("th",{width:"",onClick:e[6]||(e[6]=n=>o("online")),class:"sortable"},"是否核可"),a("th",{width:"",onClick:e[7]||(e[7]=n=>o("failcount")),class:"sortable"},"登入錯誤次數"),a("th",{width:"",onClick:e[8]||(e[8]=n=>o("created_at")),class:"sortable"},"建立日期")])]),a("tbody",null,[(m(!0),p(N,null,Y(c.data,(n,ce)=>{var k;return m(),p("tr",null,[a("td",de,[[999].includes((k=l(f).adminuser)==null?void 0:k.role)?(m(),Z(v,{key:0,modelValue:n.del,"onUpdate:modelValue":_=>n.del=_,"true-value":n.id},null,8,["modelValue","onUpdate:modelValue","true-value"])):E("",!0),e[13]||(e[13]=ee("   ")),a("a",{href:`/admin/adminuser/edit?edit=${n.id}`,class:"btn btn-success btn-sm",onClick:e[9]||(e[9]=te(_=>h.value.open(_.currentTarget.href,{...b},{title:"管理人員"}),["prevent"]))},e[12]||(e[12]=[a("i",{class:"fa fa-edit","aria-hidden":"true"},null,-1)]),8,ue)]),a("td",null,i(n.account),1),a("td",null,i(n.name),1),a("td",null,[a("a",{href:"mailto:"+n.email},i(n.email),9,me)]),a("td",null,i(("utils"in t?t.utils:l(d)).getXmlSearch("角色",n.role)),1),a("td",null,i(("utils"in t?t.utils:l(d)).formatDate(n.lastlogin_dt)),1),a("td",null,i(("utils"in t?t.utils:l(d)).getXmlSearch("是否核可",n.online)),1),a("td",null,i(n.failcount),1),a("td",null,i(("utils"in t?t.utils:l(d)).formatDate(n.created_at)),1)])}),256))])])]),r(O,{total:c.total,page:s.page,"onUpdate:page":e[10]||(e[10]=n=>s.page=n),onCurrentChange:V},null,8,["total","page"]),r(ne,{ref_key:"myDialog",ref:h,onClosedDialog:L},null,512)])),[[U,l(g).getLoading()]])],64)}}});export{ye as default};
