<template>
    <span v-html="data.epostbody"></span>
</template>

<script setup lang="ts">
import { defineProps, onMounted, reactive } from 'vue'

const http = createHttp()

const props = defineProps({
    id: {
        type: String,
        default: '',
        required: true
    }
})

const data = reactive<epost>({
    epostid: 0,
    epostbody: ''
})
const getData = async (): Promise<void> => {
    try {
        const rep = (await http.post('api/epost/show', { id: props.id })) as ApiResponse

        if (rep.resultcode == '0') {
            Object.assign(data, rep.data)
            //console.log(["inputs", inputs]);
        } else {
            throw new Error(rep.resultmessage)
        }
    } catch (error: any) {
        utils.formElError(error)
        console.error(error)
    }
}

onMounted(async () => {
    getData()
})

const errorCaptured = async (err, vm, info) => {
    console.error(`my-epost Error: ${vm.$options.name};message: ${err.toString()};info: ${info}`)
    return false // 可以返回 true 或 false 來決定是否繼續往上傳遞錯誤
}
</script>

<style scoped></style>
