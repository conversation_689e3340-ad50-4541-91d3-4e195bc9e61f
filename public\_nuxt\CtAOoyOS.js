import{d as V,g as x,h as k,i as E,f as T,r as u,j as p,k as z,c as j,o as _,b as e,l,m as a,n as f,p as B,w as L,v as U,e as q,B as C,E as F,C as I,T as N,I as Y,F as D,y as b}from"./D_zVjEfm.js";import{u as M}from"./CKhpvNlx.js";const R={class:"breadcrumb-area"},X={class:"container"},A={class:"row"},J={class:"col-12"},S={"aria-label":"breadcrumb"},W={class:"breadcrumb"},H={class:"breadcrumb-item"},O={class:"contact-area"},Q={class:"container"},G={class:"row align-items-center justify-content-between"},K={class:"col-12 col-lg-5"},P={class:"contact-form-area mb-100"},Z={class:"row"},$={class:"col-12 col-md-6"},ee={class:"col-12 col-md-6"},te={class:"col-12 col-md-6"},oe={class:"col-12 col-md-6"},le={class:"col-12"},se={class:"col-12"},ae={class:"col-12"},ie=V({__name:"index",setup(re){x(),k(),E();const c=T(),{getRecaptchaToken:g}=M(),i=u();u([]);const o=p({google_recaptcha_token:""});p({});const v=async()=>{var m;if(i.value)try{if(await i.value.validate())try{o.google_recaptcha_token=await g("submit");let r=await c.post("api/feedback/store",o);if(r.resultcode=="0")b.toast(r.resultmessage),(m=i.value)==null||m.resetFields();else throw new Error(r.resultmessage)}catch(r){b.alert(r.message),console.error(r)}}catch(t){console.error("Validation error:",t)}};return z(async()=>{}),(m,t)=>{const r=B,n=I,d=F,y=N,h=Y,w=U;return _(),j(D,null,[e("div",R,[t[8]||(t[8]=e("div",{class:"top-breadcrumb-area bg-img bg-overlay d-flex align-items-center justify-content-center",style:{"background-image":"url(/img/bg-img/contact_bg.jpg)"}},[e("h2",null,"聯繫我們")],-1)),e("div",X,[e("div",A,[e("div",J,[e("nav",S,[e("ol",W,[e("li",H,[l(r,{to:{path:"/"}},{default:a(()=>t[6]||(t[6]=[f("首頁")])),_:1})]),t[7]||(t[7]=e("li",{class:"breadcrumb-item active"},"聯繫我們",-1))])])])])])]),e("section",O,[e("div",Q,[e("div",G,[e("div",K,[t[10]||(t[10]=e("div",{class:"section-heading"},[e("p",null,"感謝您的造訪，如有需要請不吝惜的聯絡我們，希望有機會能為您服務，謝謝。")],-1)),e("div",P,[L((_(),C(h,{ref_key:"formEl",ref:i,style:{width:"98%"},model:o},{default:a(()=>[e("div",Z,[e("div",$,[l(d,{prop:"name",rules:[{required:!0,message:"姓名 未填"}]},{default:a(()=>[l(n,{modelValue:o.name,"onUpdate:modelValue":t[0]||(t[0]=s=>o.name=s),type:"text",placeholder:"您的姓名"},null,8,["modelValue"])]),_:1})]),e("div",ee,[l(d,{prop:"lineid"},{default:a(()=>[l(n,{modelValue:o.lineid,"onUpdate:modelValue":t[1]||(t[1]=s=>o.lineid=s),type:"text",placeholder:"您的LINE ID"},null,8,["modelValue"])]),_:1})]),e("div",te,[l(d,{prop:"mobile"},{default:a(()=>[l(n,{modelValue:o.mobile,"onUpdate:modelValue":t[2]||(t[2]=s=>o.mobile=s),type:"text",placeholder:"您的手機"},null,8,["modelValue"])]),_:1})]),e("div",oe,[l(d,{prop:"email",rules:[{required:!0,message:"電子信箱 未填"},{type:"email",message:"請輸入正確的電子信箱格式"}]},{default:a(()=>[l(n,{modelValue:o.email,"onUpdate:modelValue":t[3]||(t[3]=s=>o.email=s),type:"email",placeholder:"您的信箱"},null,8,["modelValue"])]),_:1})]),e("div",le,[l(d,{prop:"title",rules:[{required:!0,message:"標題 未填"}]},{default:a(()=>[l(n,{modelValue:o.title,"onUpdate:modelValue":t[4]||(t[4]=s=>o.title=s),type:"text",placeholder:"主題"},null,8,["modelValue"])]),_:1})]),e("div",se,[l(d,{prop:"memo"},{default:a(()=>[l(n,{modelValue:o.memo,"onUpdate:modelValue":t[5]||(t[5]=s=>o.memo=s),type:"textarea",rows:10,placeholder:"您的留言"},null,8,["modelValue"])]),_:1})]),e("div",ae,[l(y,{type:"primary",class:"alazea-btn mt-15",onClick:v},{default:a(()=>t[9]||(t[9]=[f("送出")])),_:1})])])]),_:1},8,["model"])),[[w,q(c).getLoading()]])])]),t[11]||(t[11]=e("div",{class:"col-12 col-lg-5"},[e("div",{class:"map-area mb-100"},[e("iframe",{src:"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3613.194637860622!2d121.49530227521973!3d25.09527187777668!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3442aeda08faaaab%3A0xb44541635fb447a0!2zMTEx5Y-w5YyX5biC5aOr5p6X5Y2A5bu25bmz5YyX6Lev5LiD5q61MTjkuYsy6JmfQuajnzTmqJM!5e0!3m2!1szh-TW!2stw!4v1746018474165!5m2!1szh-TW!2stw",width:"600",height:"450",style:{border:"0"},allowfullscreen:!0,loading:"lazy",referrerpolicy:"no-referrer-when-downgrade"})])],-1))])])])],64)}}});export{ie as default};
