import{d as C,f as D,r as o,a7 as k,k as M,c as v,o as s,l as g,B as S,s as N,a9 as T,m as d,ai as F,n as x,t as E,e as p,F as j,q,ah as U}from"./D_zVjEfm.js";const H={class:"inline"},z=C({__name:"my-apiselectform",props:{name1:{type:String,required:!0},name2:{type:String,required:!0},api1:{type:String,required:!0},api2:{type:String,required:!0},field1:{type:String,required:!0},field2:{type:String,required:!0}},emits:["update:name1","update:name2"],setup(w,{emit:B}){const y=D(),b=B,n=w;o(""),o("");let h=o("Loading"),i=o("Select");const O=k({get(){return n.name1!=""&&n.name2!=""&&V(n.name1),String(n.name1)},set(t){b("update:name1",String(t)||""),t!=""?(i.value="Loading",m.value="",V(t)):c.value=[]}}),m=k({get(){return String(n.name2)},set(t){b("update:name2",String(t)||"")}});let _=o([]),c=o([]);o([]);const L=async()=>{try{let t=await y.post(n.api1,{},!1);if(t.resultcode=="0")t.data.forEach(function(l,r){let a="",e="";Object.keys(l).length==1?(a=String(Object.values(l)[0]),e=a):(a=String(Object.values(l)[0]),e=String(Object.values(l)[1])),_.value.push({value:""+a,label:e})});else throw new Error(t.resultmessage);h.value="Select"}catch(t){console.error("Error fetching options:",t)}},V=async t=>{try{c.value=[],i.value="Loading";let l={};l[n.field1]=t;let r=await y.post(n.api2,l,!1);if(r.resultcode=="0")r.data.forEach(function(a,e){let u="",f="";Object.keys(a).length==1?(u=String(Object.values(a)[0]),f=u):(u=String(Object.values(a)[0]),f=String(Object.values(a)[1])),c.value.push({value:""+u,label:f})});else throw new Error(r.resultmessage);i.value="Select"}catch(l){console.error("Error fetching options:",l)}};return M(async()=>{L()}),(t,l)=>{const r=F,a=U;return s(),v("div",H,[g(a,{modelValue:O.value,"onUpdate:modelValue":l[0]||(l[0]=e=>O.value=e)},{default:d(()=>[g(r,{value:""},{default:d(()=>[x(E(p(h)),1)]),_:1}),(s(!0),v(j,null,q(p(_),(e,u)=>(s(),S(r,{key:e.id,value:e.value,label:e.label},null,8,["value","label"]))),128))]),_:1},8,["modelValue"]),n.name2!=null?(s(),S(a,{key:0,modelValue:m.value,"onUpdate:modelValue":l[1]||(l[1]=e=>m.value=e)},{default:d(()=>[g(r,{value:""},{default:d(()=>[x(E(p(i)),1)]),_:1}),(s(!0),v(j,null,q(p(c),(e,u)=>(s(),S(r,{key:e.id,value:e.value,label:e.label},null,8,["value","label"]))),128))]),_:1},8,["modelValue"])):N("",!0),T(t.$slots,"default")])}}});export{z as default};
