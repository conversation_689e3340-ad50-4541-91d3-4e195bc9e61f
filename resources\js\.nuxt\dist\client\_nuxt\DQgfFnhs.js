import{d as S,f as F,g as _,i as m,r as A,j as D,w as N,v as U,e as o,B as V,o as u,m as c,l as f,b as t,R as $,y as n,T as j,n as p,c as b,F as q,q as I,t as g,I as P}from"./D_zVjEfm.js";const R={align:"center"},T={class:"table-responsive-md"},z={class:"table table-striped table-hover table-bordered table-fixed"},O=S({__name:"index",props:{inputs:Object,url:{type:String,default:"",required:!0}},emits:["closed-dialog"],setup(s,{emit:x}){const a=F();_(),m();const v=x;let r=A([]);const y=D({headers:{Authorization:`Bearer ${m().adminuser.api_token}`}}),E=async(l,e,i)=>{a.setLoading(!1),l.resultcode=="0"?r.value=l.data:n.alert(l.resultmessage||"Upload failed. Please try again.","error")},B=l=>(a.setLoading(!0),r.value=[],l.type==="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"||l.type==="application/vnd.ms-excel"?!0:(n.toast("只允許上傳Excel文件","error"),!1)),h=(l,e,i)=>{a.setLoading(!1),n.toast(l.message,"error")};return(l,e)=>{const i=j,k=$,L=P,w=U;return N((u(),V(L,{ref:"formEl",model:s.inputs},{default:c(()=>[f(k,{headers:y.headers,class:"upload-demo",name:"file1",accept:".xls,.xlsx","before-upload":B,action:("utils"in l?l.utils:o(n)).getConfig("API_BASE")+s.url,"on-error":h,"on-success":E,"show-file-list":!1,data:s.inputs},{default:c(()=>[f(i,{type:"primary"},{default:c(()=>e[2]||(e[2]=[p("Click to upload")])),_:1})]),_:1},8,["headers","action","data"]),t("div",R,[t("button",{type:"button",class:"btn btn-warning",onClick:e[0]||(e[0]=d=>o(a).download(s.url.replace("excelimportstore","excelsample"),s.inputs))}," 範本 "),e[3]||(e[3]=p("   ")),t("button",{type:"button",class:"btn btn-secondary",onClick:e[1]||(e[1]=d=>v("closed-dialog",!0))}," 返回 "),e[4]||(e[4]=p(" 只限 xls/xlsx 格式 "))]),t("div",T,[t("table",z,[e[5]||(e[5]=t("thead",null,[t("tr",null,[t("th",null,"序號"),t("th",null,"結果")])],-1)),t("tbody",null,[(u(!0),b(q,null,I(o(r),(d,C)=>(u(),b("tr",null,[t("td",null,g(C+1),1),t("td",null,g(d),1)]))),256))]),e[6]||(e[6]=t("tbody",null,null,-1))])])]),_:1},8,["model"])),[[w,o(a).getLoading()]])}}});export{O as default};
