const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DEPTJ9I4.js","./D_zVjEfm.js","./entry.BGE7dmog.css","./AII-GxSx.js","./ewbPcaBc.js","./DeQTbgbd.js","./MMxUCDix.js","./DIRD0ar_.js","./DZ_RHEX4.js","./B2ebk17F.js","./DbPQOmG-.js"])))=>i.map(i=>d[i]);
import{d as $,g as x,h as B,f as M,r as v,j as V,k as S,M as N,c as g,o as a,l as m,w as q,v as H,e as w,B as f,m as l,b as s,F as L,q as U,n as E,t as j,E as z,S as G,N as J,O as K,y as _,P as Q,H as W,I as X,L as r}from"./D_zVjEfm.js";import{_ as Y}from"./jBNyKXjo.js";const Z={class:"form-group row"},ee={class:"col-md-2"},te={class:"text-danger p-1"},oe={class:"col-md-10"},se={class:"dialog-content"},re={class:"dialog-loading"},ae={align:"center"},de=$({__name:"edit",props:{edit:{default:""},kind:{default:""}},emits:["closed-dialog"],setup(b,{emit:D}){x();const h=B(),c=M(),y=D,d=b,k=v([]),u=v(null);v([]);const n=V({title:"",memo:"",field1:"",begindate:"",closedate:"",kindsort:"",kind:d.kind});V({});const I=async()=>{try{let t=await c.post("api/admin/kind/show?id="+d.edit,{});if(t.resultcode=="0")Object.assign(n,t.data);else throw new Error(t.resultmessage)}catch(t){_.formElError(t),console.error(t)}finally{}},O=async()=>{if(u.value)try{if(await u.value.validate())try{let e=await c.post("api/admin/kind/store",n);if(e.resultcode=="0")_.toast(e.resultmessage),y("closed-dialog",!0);else throw new Error(e.resultmessage)}catch(e){_.message(e.message),console.error(e)}}catch(t){console.error("Validation error:",t)}};S(async()=>{P(),d.edit!=""&&I()});async function P(){const t=Object.assign({"/datas/aboutpage.js":()=>r(()=>import("./DEPTJ9I4.js"),__vite__mapDeps([0,1,2]),import.meta.url),"/datas/all.js":()=>r(()=>import("./AII-GxSx.js"),__vite__mapDeps([3,1,2]),import.meta.url),"/datas/banner.js":()=>r(()=>import("./ewbPcaBc.js"),__vite__mapDeps([4,1,2]),import.meta.url),"/datas/case.js":()=>r(()=>import("./DeQTbgbd.js"),__vite__mapDeps([5,1,2]),import.meta.url),"/datas/customer.js":()=>r(()=>import("./MMxUCDix.js"),__vite__mapDeps([6,1,2]),import.meta.url),"/datas/news.js":()=>r(()=>import("./DIRD0ar_.js"),__vite__mapDeps([7,1,2]),import.meta.url),"/datas/productkind.js":()=>r(()=>import("./DZ_RHEX4.js"),__vite__mapDeps([8,1,2]),import.meta.url),"/datas/service.js":()=>r(()=>import("./B2ebk17F.js"),__vite__mapDeps([9,1,2]),import.meta.url),"/datas/serviceitem.js":()=>r(()=>import("./DbPQOmG-.js"),__vite__mapDeps([10,1,2]),import.meta.url)});d.kind;const e=`/datas/${h.query.kind}.js`;t[e]?t[e]().then(i=>{k.value=i.fields._rawValue.filter(p=>p.isedit==!0)}).catch(i=>{console.error(`Failed to load module at ${e}`,i)}):console.error(`Module ${e} does not exist`)}return(t,e)=>{const i=Y,p=N("Loading"),R=J,T=z,A=X,C=H;return a(),g(L,null,[m(i,{id:b.kind},null,8,["id"]),q((a(),f(A,{ref_key:"formEl",ref:u,style:{width:"98%"},model:n,onSubmit:W(O,["prevent"])},{default:l(()=>[(a(!0),g(L,null,U(k.value,(o,ne)=>(a(),g("div",Z,[s("label",ee,[E(j(o.label),1),s("span",te,j(o.rules&&o.rules.length>0&&o.rules[0].required?"*":""),1)]),s("div",oe,[m(T,{prop:o.name,rules:o.rules},{default:l(()=>[(a(),f(G,null,{default:l(()=>[s("div",se,[(a(),f(K(("utils"in t?t.utils:w(_)).getComponent(o.kind)),Q({modelValue:n[o.name],"onUpdate:modelValue":F=>n[o.name]=F,ref_for:!0},o.props),null,16,["modelValue","onUpdate:modelValue"]))])]),fallback:l(()=>[s("div",re,[m(R,{class:"is-loading"},{default:l(()=>[m(p)]),_:1}),e[2]||(e[2]=s("span",null,"Loading...",-1))])]),_:2},1024))]),_:2},1032,["prop","rules"])])]))),256)),s("div",ae,[e[3]||(e[3]=s("button",{type:"submit",class:"btn btn-primary"},"確定",-1)),e[4]||(e[4]=E("   ")),s("button",{type:"reset",class:"btn btn-secondary",onClick:e[0]||(e[0]=o=>u.value.resetFields())},"取消"),e[5]||(e[5]=E("   ")),s("button",{type:"button",class:"btn btn-secondary",onClick:e[1]||(e[1]=o=>y("closed-dialog",!1))},"返回")])]),_:1},8,["model"])),[[C,w(c).getLoading()]])],64)}}});export{de as default};
