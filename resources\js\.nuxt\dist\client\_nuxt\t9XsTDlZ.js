import{_ as M}from"./jBNyKXjo.js";import{d as B,f as F,g as L,h as D,i as N,r as _,j as w,k as S,c as b,o as f,l,w as $,v as j,e as A,B as H,m as r,b as o,s as R,n,E as G,C as O,D as P,G as T,H as z,I as J,F as K,y as g}from"./D_zVjEfm.js";import Q from"./fnCu7pBG.js";const W={class:"form-group row"},X={class:"col-md-10"},Y={class:"form-group row"},Z={class:"col-md-10"},h={class:"form-group row"},ee={class:"col-md-10"},oe={class:"form-group row"},se={class:"col-md-10"},te={class:"form-group row"},le={class:"col-md-10"},ae={class:"form-group row"},re={class:"col-md-10"},ne={class:"form-group row"},de={class:"col-md-10"},ie={key:0,class:"form-group row"},me={class:"col-md-10"},ue={align:"center"},ge=B({__name:"edit",props:{edit:{default:""}},emits:["closed-dialog"],setup(u,{emit:V}){const p=F();L(),D(),N();const v=V,c=u,m=_(null);_([]);const s=w({account:"",name:"",password:"",email:"",role:"",online:"",failcount:""});w({});const y=async()=>{try{let a=await p.post("api/admin/adminuser/show",{id:c.edit});if(a.resultcode=="0")Object.assign(s,a.data);else throw new Error(a.resultmessage)}catch(a){g.message(a.message),console.error(a)}},x=async()=>{if(m.value)try{if(!await m.value.validate())return;let e=await p.post("api/admin/adminuser/store",s);if(e.resultcode=="0")g.toast(e.resultmessage),v("closed-dialog",!0);else throw new Error(e.resultmessage)}catch(a){console.error(a),g.formElError(a)}};return S(async()=>{typeof c.edit<"u"&&c.edit!=""&&await y()}),(a,e)=>{const E=M,i=O,d=G,k=P,U=Q,q=T,C=J,I=j;return f(),b(K,null,[l(E,{id:"adminuser"}),$((f(),H(C,{ref_key:"formEl",ref:m,style:{width:"98%"},model:s,onSubmit:z(x,["prevent"])},{default:r(()=>[o("div",W,[e[11]||(e[11]=o("label",{class:"col-md-2"},[n("帳號"),o("span",{class:"text-danger p-1"},"*")],-1)),o("div",X,[l(d,{prop:"account",rules:[{required:!0,message:"帳號 未填"}]},{default:r(()=>[l(i,{modelValue:s.account,"onUpdate:modelValue":e[0]||(e[0]=t=>s.account=t),type:"text"},null,8,["modelValue"])]),_:1})])]),o("div",Y,[e[12]||(e[12]=o("label",{class:"col-md-2"},[n("姓名"),o("span",{class:"text-danger p-1"},"*")],-1)),o("div",Z,[l(d,{prop:"name",rules:[{required:!0,message:"姓名 未填"}]},{default:r(()=>[l(i,{modelValue:s.name,"onUpdate:modelValue":e[1]||(e[1]=t=>s.name=t),type:"text"},null,8,["modelValue"])]),_:1})])]),o("div",h,[e[13]||(e[13]=o("label",{class:"col-md-2"},[n("密碼"),o("span",{class:"text-danger p-1"})],-1)),o("div",ee,[l(d,{prop:"password",rules:[{required:u.edit=="",message:"密碼 未填"},{min:8,max:20,message:"長度在 8 到 20 个字符",trigger:"blur"}]},{default:r(()=>[l(k,{placement:"top-start",title:"密碼規格",width:400,trigger:"hover",content:"密碼長度必須為8~20位, 其中必須包含至少一位數字、一位英文，若需有特殊符號僅限於 ! @ # $ % & *"},{reference:r(()=>[l(i,{modelValue:s.password,"onUpdate:modelValue":e[2]||(e[2]=t=>s.password=t),"show-password":"",type:"password",placeholder:"密碼"},null,8,["modelValue"])]),_:1})]),_:1},8,["rules"])])]),o("div",oe,[e[14]||(e[14]=o("label",{class:"col-md-2"},[n("确認密碼"),o("span",{class:"text-danger p-1"})],-1)),o("div",se,[l(d,{prop:"password_confirmation"},{default:r(()=>[l(i,{type:"password",modelValue:s.password_confirmation,"onUpdate:modelValue":e[3]||(e[3]=t=>s.password_confirmation=t),autocomplete:"off"},null,8,["modelValue"])]),_:1})])]),o("div",te,[e[15]||(e[15]=o("label",{class:"col-md-2"},[n("EMAIL"),o("span",{class:"text-danger p-1"})],-1)),o("div",le,[l(d,{prop:"email",rules:[{required:!1,message:"EMAIL 未填"},{type:"email",message:"EMAIL 格式錯誤"}]},{default:r(()=>[l(i,{modelValue:s.email,"onUpdate:modelValue":e[4]||(e[4]=t=>s.email=t),type:"email",placeholder:"ex <EMAIL>"},null,8,["modelValue"])]),_:1})])]),o("div",ae,[e[16]||(e[16]=o("label",{class:"col-md-2"},[n("角色"),o("span",{class:"text-danger p-1"},"*")],-1)),o("div",re,[l(d,{prop:"role",rules:[{required:!1,message:"角色 未選"}]},{default:r(()=>[l(U,{onChange:e[5]||(e[5]=t=>console.log(t)),modelValue:s.role,"onUpdate:modelValue":e[6]||(e[6]=t=>s.role=t),type:"radio",xpath:"角色"},null,8,["modelValue"])]),_:1})])]),o("div",ne,[e[17]||(e[17]=o("label",{class:"col-md-2"},[n("開啟"),o("span",{class:"text-danger p-1"})],-1)),o("div",de,[l(d,{prop:"online",rules:[{required:!1,message:"開啟 未填"}]},{default:r(()=>[l(q,{"active-value":1,"inactive-value":0,modelValue:s.online,"onUpdate:modelValue":e[7]||(e[7]=t=>s.online=t)},null,8,["modelValue"])]),_:1})])]),u.edit!=""?(f(),b("div",ie,[e[18]||(e[18]=o("label",{class:"col-md-2"},[n("登入錯誤次數"),o("span",{class:"text-danger p-1"})],-1)),o("div",me,[l(d,{prop:"failcount",rules:[{required:!0,message:"登入錯誤次數 未填"}]},{default:r(()=>[l(i,{modelValue:s.failcount,"onUpdate:modelValue":e[8]||(e[8]=t=>s.failcount=t),modelModifiers:{number:!0},type:"number"},null,8,["modelValue"])]),_:1})])])):R("",!0),o("div",ue,[e[19]||(e[19]=o("button",{type:"submit",class:"btn btn-primary"},"確定",-1)),e[20]||(e[20]=n("   ")),o("button",{type:"reset",class:"btn btn-secondary",onClick:e[9]||(e[9]=t=>m.value.resetFields())},"取消"),e[21]||(e[21]=n("   ")),o("button",{type:"button",class:"btn btn-secondary",onClick:e[10]||(e[10]=t=>v("closed-dialog",!1))},"返回")])]),_:1},8,["model"])),[[I,A(p).getLoading()]])],64)}}});export{ge as default};
