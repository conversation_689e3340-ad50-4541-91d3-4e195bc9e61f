const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DEPTJ9I4.js","./D_zVjEfm.js","./entry.BGE7dmog.css","./AII-GxSx.js","./ewbPcaBc.js","./DeQTbgbd.js","./MMxUCDix.js","./DIRD0ar_.js","./DZ_RHEX4.js","./B2ebk17F.js","./DbPQOmG-.js"])))=>i.map(i=>d[i]);
import{d as N,g as x,f as B,r as y,j as D,k as M,L as a,M as S,c,o as r,l as _,w as $,v as q,e as I,B as k,m as l,b as s,F as O,q as H,n as p,t as v,E as U,S as z,N as G,O as J,y as g,P as K,H as Q,I as W}from"./D_zVjEfm.js";import{_ as X}from"./jBNyKXjo.js";const Y={class:"col-md-2"},Z={class:"text-danger p-1"},tt={class:"col-md-10"},et={class:"dialog-content"},ot={key:1,class:"text-danger"},st={class:"dialog-loading"},at={align:"center"},it=N({__name:"edit",props:{edit:{default:""},kind:{default:""}},emits:["closed-dialog"],setup(V,{emit:P}){x();const f=B(),w=P,d=V,L=y([]),m=y();y([]);const n=D({title:"",memo:"",field1:"",begindate:"",closedate:"",boardsort:"",kind:d.kind});D({});const R=async()=>{try{let t=await f.post("api/admin/board/show?id="+d.edit,{});if(t.resultcode=="0")Object.assign(n,t.data);else throw new Error(t.resultmessage)}catch(t){g.formElError(t),console.error(t)}finally{}},j=async()=>{if(m.value)try{if(await m.value.validate()){let e=await f.post("api/admin/board/store",n);if(e.resultcode=="0")g.toast(e.resultmessage),w("closed-dialog",!0);else throw new Error(e.resultmessage)}}catch(t){console.error("Validation error:",t),g.formElError(t)}};M(async()=>{T(),d.edit!=""&&R()});async function T(){try{const t=Object.assign({"/datas/aboutpage.js":()=>a(()=>import("./DEPTJ9I4.js"),__vite__mapDeps([0,1,2]),import.meta.url),"/datas/all.js":()=>a(()=>import("./AII-GxSx.js"),__vite__mapDeps([3,1,2]),import.meta.url),"/datas/banner.js":()=>a(()=>import("./ewbPcaBc.js"),__vite__mapDeps([4,1,2]),import.meta.url),"/datas/case.js":()=>a(()=>import("./DeQTbgbd.js"),__vite__mapDeps([5,1,2]),import.meta.url),"/datas/customer.js":()=>a(()=>import("./MMxUCDix.js"),__vite__mapDeps([6,1,2]),import.meta.url),"/datas/news.js":()=>a(()=>import("./DIRD0ar_.js"),__vite__mapDeps([7,1,2]),import.meta.url),"/datas/productkind.js":()=>a(()=>import("./DZ_RHEX4.js"),__vite__mapDeps([8,1,2]),import.meta.url),"/datas/service.js":()=>a(()=>import("./B2ebk17F.js"),__vite__mapDeps([9,1,2]),import.meta.url),"/datas/serviceitem.js":()=>a(()=>import("./DbPQOmG-.js"),__vite__mapDeps([10,1,2]),import.meta.url)}),i=`/datas/${d.kind}.js`;if(t[i]){const E=await t[i]();L.value=E.fields._rawValue.filter(b=>b.isedit==!0)}else console.error(`Module ${i} does not exist`)}catch(t){console.error("Failed to load module",t)}}return(t,e)=>{const i=X,E=S("Loading"),b=G,h=U,A=W,C=q;return r(),c(O,null,[_(i,{id:V.kind},null,8,["id"]),$((r(),k(A,{ref_key:"formEl",ref:m,style:{width:"98%"},model:n,onSubmit:[j,Q(j,["prevent"])]},{default:l(()=>[(r(!0),c(O,null,H(L.value,(o,u)=>(r(),c("div",{class:"form-group row",key:u},[s("label",Y,[p(v(o.label),1),s("span",Z,v(o.rules&&o.rules.length>0&&o.rules[0].required?"*":""),1)]),s("div",tt,[_(h,{prop:o.name,rules:o.rules},{default:l(()=>[(r(),k(z,null,{default:l(()=>[s("div",et,[o.kind?(r(),k(J(("utils"in t?t.utils:I(g)).getComponent(o.kind)),K({key:0,modelValue:n[o.name],"onUpdate:modelValue":F=>n[o.name]=F,ref_for:!0},o.props),null,16,["modelValue","onUpdate:modelValue"])):(r(),c("div",ot,'組件 "'+v(o.kind)+'" 不存在，請檢查組件設定是否正確',1))])]),fallback:l(()=>[s("div",st,[_(b,{class:"is-loading"},{default:l(()=>[_(E)]),_:1}),e[2]||(e[2]=s("span",null,"Loading...",-1))])]),_:2},1024))]),_:2},1032,["prop","rules"]),p(" "+v(o.memo),1)])]))),128)),s("div",at,[e[3]||(e[3]=s("button",{type:"submit",class:"btn btn-primary"},"確定",-1)),e[4]||(e[4]=p("   ")),s("button",{type:"reset",class:"btn btn-secondary",onClick:e[0]||(e[0]=o=>{var u;return(u=m.value)==null?void 0:u.resetFields()})},"取消"),e[5]||(e[5]=p("   ")),s("button",{type:"button",class:"btn btn-secondary",onClick:e[1]||(e[1]=o=>w("closed-dialog",!1))},"返回")])]),_:1},8,["model"])),[[C,I(f).getLoading()]])],64)}}});export{it as default};
