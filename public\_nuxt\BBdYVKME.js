import{_ as F}from"./jBNyKXjo.js";import{d as $,f as D,g as I,h as M,i as N,r as b,j as p,k as S,c as j,o as y,l,w as H,v as L,e as P,B as R,m as d,b as o,n as r,E as O,C as T,H as z,I as A,F as G,y as c}from"./D_zVjEfm.js";import J from"./CKt8fKXZ.js";import K from"./fnCu7pBG.js";import{_ as Q}from"./2eTUCwpK.js";import{_ as W}from"./G4Mf9Qin.js";const X={class:"form-group row"},Y={class:"col-md-10"},Z={class:"form-group row"},h={class:"col-md-10"},ee={class:"form-group row"},oe={class:"col-md-10"},se={class:"form-group row"},te={class:"col-md-10"},le={class:"form-group row"},ae={class:"col-md-10"},re={class:"form-group row"},de={class:"col-md-10"},ne={class:"form-group row"},ie={class:"col-md-10"},me={align:"center"},be=$({__name:"edit",props:{edit:{default:""}},emits:["closed-dialog"],setup(v,{emit:V}){const m=D();I(),M(),N();const f=V,u=v,i=b();b([]);const s=p({});p({x:""}),p({});const w=async()=>{try{const a=await m.post("api/admin/product/show",{id:u.edit});if(a.resultcode=="0")Object.assign(s,a.data);else throw new Error(a.resultmessage)}catch(a){c.message(a.message),console.error(a)}},x=async()=>{if(i.value)try{if(!await i.value.validate())return;const e=await m.post("api/admin/product/store",s);if(e.resultcode=="0")c.toast(e.resultmessage),f("closed-dialog",!0);else throw new Error(e.resultmessage)}catch(a){console.error(a),c.formElError(a)}};return S(async()=>{typeof u.edit<"u"&&u.edit!=""&&await w()}),(a,e)=>{const k=F,g=T,n=O,E=J,q=K,U=Q,_=W,C=A,B=L;return y(),j(G,null,[l(k,{id:"product"}),H((y(),R(C,{ref_key:"formEl",ref:i,style:{width:"98%"},model:s,onSubmit:z(x,["prevent"])},{default:d(()=>[o("div",X,[e[10]||(e[10]=o("label",{class:"col-md-2"},[r("標題： "),o("span",{class:"text-danger p-1"}," * ")],-1)),o("div",Y,[l(n,{prop:"title",rules:[{required:!0,message:"標題 未填"}]},{default:d(()=>[l(g,{modelValue:s.title,"onUpdate:modelValue":e[0]||(e[0]=t=>s.title=t),type:"text"},null,8,["modelValue"])]),_:1})])]),o("div",Z,[e[11]||(e[11]=o("label",{class:"col-md-2"},[r("圖示： "),o("span",{class:"text-danger p-1"})],-1)),o("div",h,[l(n,{prop:"img",rules:[{required:!1,message:"圖示 未填"}]},{default:d(()=>[l(E,{modelValue:s.img,"onUpdate:modelValue":e[1]||(e[1]=t=>s.img=t),folder:"images/product",width:"1412",height:"1673",limit:1,accept:"image/*"},null,8,["modelValue"])]),_:1})])]),o("div",ee,[e[12]||(e[12]=o("label",{class:"col-md-2"},[r("放置位置： "),o("span",{class:"text-danger p-1"}," * ")],-1)),o("div",oe,[l(n,{prop:"location",rules:[{required:!0,message:"放置位置 未選"}]},{default:d(()=>[l(q,{onChange:e[2]||(e[2]=t=>console.log(t)),modelValue:s.location,"onUpdate:modelValue":e[3]||(e[3]=t=>s.location=t),type:"checkbox",xpath:"放置位置"},null,8,["modelValue"])]),_:1})])]),o("div",se,[e[14]||(e[14]=o("label",{class:"col-md-2"},[r("排序號碼： "),o("span",{class:"text-danger p-1"})],-1)),o("div",te,[l(n,{prop:"sortnum",rules:[{required:!1,message:"排序號碼 未填"}]},{default:d(()=>[l(g,{modelValue:s.sortnum,"onUpdate:modelValue":e[4]||(e[4]=t=>s.sortnum=t),modelModifiers:{number:!0},type:"number"},null,8,["modelValue"]),e[13]||(e[13]=r(" 小到大 "))]),_:1})])]),o("div",le,[e[15]||(e[15]=o("label",{class:"col-md-2"},[r("本文： "),o("span",{class:"text-danger p-1"})],-1)),o("div",ae,[l(n,{prop:"body",rules:[{required:!1,message:"本文 未填"}]},{default:d(()=>[l(U,{modelValue:s.body,"onUpdate:modelValue":e[5]||(e[5]=t=>s.body=t)},null,8,["modelValue"])]),_:1})])]),o("div",re,[e[16]||(e[16]=o("label",{class:"col-md-2"},[r("開始時間： "),o("span",{class:"text-danger p-1"})],-1)),o("div",de,[l(n,{prop:"begindate",rules:[{required:!1,message:"開始時間 未填"}]},{default:d(()=>[l(_,{modelValue:s.begindate,"onUpdate:modelValue":e[6]||(e[6]=t=>s.begindate=t),placeholder:"Pick a day",type:"date"},null,8,["modelValue"])]),_:1})])]),o("div",ne,[e[17]||(e[17]=o("label",{class:"col-md-2"},[r("結束時間： "),o("span",{class:"text-danger p-1"})],-1)),o("div",ie,[l(n,{prop:"closedate",rules:[{required:!1,message:"結束時間 未填"}]},{default:d(()=>[l(_,{modelValue:s.closedate,"onUpdate:modelValue":e[7]||(e[7]=t=>s.closedate=t),placeholder:"Pick a day",type:"date"},null,8,["modelValue"])]),_:1})])]),o("div",me,[e[18]||(e[18]=o("button",{type:"submit",class:"btn btn-primary"},"確定",-1)),e[19]||(e[19]=r("   ")),o("button",{type:"reset",class:"btn btn-secondary",onClick:e[8]||(e[8]=t=>i.value.resetFields())},"取消"),e[20]||(e[20]=r("   ")),o("button",{type:"button",class:"btn btn-secondary",onClick:e[9]||(e[9]=t=>f("closed-dialog",!1))},"返回")])]),_:1},8,["model"])),[[B,P(m).getLoading()]])],64)}}});export{be as default};
