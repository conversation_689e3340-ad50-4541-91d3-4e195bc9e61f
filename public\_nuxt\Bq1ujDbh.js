import{_ as k}from"./jBNyKXjo.js";import{d as q,f as x,u as B,a as F,r as m,j as S,Q as C,k as D,c as I,o as a,l as y,w as N,v as j,e as H,B as p,m as f,b as _,E as L,C as M,n as R,H as U,I as $,F as O,y as u}from"./D_zVjEfm.js";import{_ as P}from"./2eTUCwpK.js";const A=q({__name:"edit",props:{edit:{type:String,default:""},type:{type:String,default:"html"}},setup(b){const n=x();B();const r=F(),l=b;m([]);const t=S({epostbody:"",edit:r.query.edit||"",type:r.query.type||l.type}),c=async()=>{var o;try{t.epostbody="";const e=await n.post("api/admin/epost/show",{edit:(o=r.query)==null?void 0:o.edit});e.resultcode=="0"&&Object.assign(t,e.data)}catch(e){u.alert(e.message||e),console.error(e)}finally{}};C(()=>r.fullPath,()=>{var o,e;t.edit=((o=r.query)==null?void 0:o.edit)||"",t.type=((e=r.query)==null?void 0:e.type)||l.type,c()});const i=m(),g=async()=>{if(i.value&&t.edit!="")try{if(await i.value.validate())try{const e={edit:t.edit,epostbody:t.epostbody},s=await n.post("api/admin/epost/store",e);if(s.resultcode=="0")u.toast(s.resultmessage);else throw new Error(s.resultmessage)}catch(e){u.formElError(e),console.error(e)}}catch(o){console.error("Validation error:",o)}};return D(async()=>{t.edit=r.query.edit||"",t.type=r.query.type||l.type,c()}),(o,e)=>{const s=k,w=M,v=P,E=L,V=$,h=j;return a(),I(O,null,[y(s,{id:t.edit},null,8,["id"]),N((a(),p(V,{ref_key:"formEl",ref:i,model:t,onSubmit:U(g,["prevent"])},{default:f(()=>[y(E,{label:"內容",prop:"epostbody",rules:[{required:!1,message:"內容 未填"}]},{default:f(()=>[t.type=="text"?(a(),p(w,{key:0,modelValue:t.epostbody,"onUpdate:modelValue":e[0]||(e[0]=d=>t.epostbody=d),style:{width:"98%"},type:"textarea",rows:30,placeholder:"請輸入內容"},null,8,["modelValue"])):(a(),p(v,{key:1,modelValue:t.epostbody,"onUpdate:modelValue":e[1]||(e[1]=d=>t.epostbody=d)},null,8,["modelValue"]))]),_:1}),e[2]||(e[2]=_("div",{align:"center"},[_("button",{class:"btn btn-primary"},"確定"),R("   ")],-1))]),_:1},8,["model"])),[[h,H(n).getLoading()]])],64)}}});export{A as default};
