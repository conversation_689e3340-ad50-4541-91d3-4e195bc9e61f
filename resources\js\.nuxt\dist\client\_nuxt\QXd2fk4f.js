import{d as N,f as U,r as k,a7 as F,k as q,B as u,o as a,m as d,s as I,a9 as R,ad as D,c as m,F as f,q as y,e as i,J as G,ae as L,af as M,ag as H,ah as J,l as g,ai as T,n as x,t as $,N as z,b as K,aC as P,ac as Q,_ as W}from"./D_zVjEfm.js";const X={class:"text-danger"},Y=N({__name:"my-apiform",props:{type:{type:String,required:!0,validator:s=>["select","checkbox","radio","select2","select2multiple"].includes(s)},options:{type:[Object]},isFirstDisplayFlag:{type:Boolean,default:!0},title:{type:String,default:"Select"},api:{type:String,default:"",required:!0},modelValue:{default:""}},emits:["update:modelValue","change"],setup(s,{emit:S}){const E=U(),V=S,o=s,p=k(!1),n=F({get(){const t=o.type;if(t==="checkbox")return Array.isArray(o.modelValue)?o.modelValue.map(String):(o.modelValue||"").split(",").filter(Boolean);if(t==="select2multiple"){if(o.modelValue!=null){if(typeof o.modelValue=="string"&&o.modelValue!="")return(""+o.modelValue).split(",");if(Array.isArray(o.modelValue))return o.modelValue}return[]}return String(o.modelValue??"")},set(t){Array.isArray(t)?V("update:modelValue",t.join(",")):V("update:modelValue",t)}});let r=k([]);const v=async()=>{try{p.value=!1,r.value=[];let t=await E.post(o.api,{},!1);if(t.resultcode=="0")r.value=t.data.map(l=>({value:String(Object.values(l)[0]),label:String(Object.values(l)[1]||Object.values(l)[0])}));else throw new Error(t.resultmessage)}catch(t){p.value=!0,console.error("Error fetching options:",t)}};return q(async()=>{v()}),(t,l)=>{const h=G,w=D,C=M,O=L,_=H,b=T,A=J,B=z,j=Q;return a(),u(j,{"fallback-tag":"span",fallback:"Loading comments..."},{default:d(()=>[s.type=="checkbox"?(a(),u(w,{key:0,modelValue:n.value,"onUpdate:modelValue":l[0]||(l[0]=e=>n.value=e)},{default:d(()=>[(a(!0),m(f,null,y(i(r),(e,c)=>(a(),u(h,{key:c,value:e.value,label:e.label},null,8,["value","label"]))),128))]),_:1},8,["modelValue"])):s.type=="radio"?(a(),u(O,{key:1,modelValue:n.value,"onUpdate:modelValue":l[1]||(l[1]=e=>n.value=e)},{default:d(()=>[(a(!0),m(f,null,y(i(r),(e,c)=>(a(),u(C,{key:c,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])):s.type=="select2"?(a(),u(_,{key:2,modelValue:n.value,"onUpdate:modelValue":l[2]||(l[2]=e=>n.value=e),style:{width:"100%"},filterable:"",options:i(r)},null,8,["modelValue","options"])):s.type=="select2multiple"?(a(),u(_,{key:3,modelValue:n.value,"onUpdate:modelValue":l[3]||(l[3]=e=>n.value=e),style:{width:"100%"},multiple:!0,filterable:"",options:i(r)},null,8,["modelValue","options"])):(a(),u(A,{key:4,modelValue:n.value,"onUpdate:modelValue":l[4]||(l[4]=e=>n.value=e)},{default:d(()=>[g(b,{value:""},{default:d(()=>[x($(s.title),1)]),_:1}),(a(!0),m(f,null,y(i(r),(e,c)=>(a(),u(b,{key:c,value:e.value,label:e.label},null,8,["value","label"]))),128))]),_:1},8,["modelValue"])),p.value?(a(),u(B,{key:5,class:"reconnect-icon",onClick:v,style:{display:"inline",width:"100%","text-align":"left"}},{default:d(()=>[K("span",X,[g(i(P)),l[5]||(l[5]=x(" 連線失敗，點擊重新連線"))])]),_:1})):I("",!0),R(t.$slots,"default",{},void 0,!0)]),_:3})}}}),ee=W(Y,[["__scopeId","data-v-b0094f8b"]]);export{ee as default};
