import{r as s,y as p}from"./D_zVjEfm.js";function d(){const t=s(!1);async function a(c){if(!t.value&&!(typeof window>"u"))return new Promise((r,n)=>{if(document.getElementById("recaptcha-script"))return t.value=!0,r();const e=document.createElement("script");e.id="recaptcha-script",e.src=`https://www.google.com/recaptcha/api.js?render=${c}`,e.async=!0,e.defer=!0,e.onload=()=>{t.value=!0,r()},e.onerror=()=>{n(new Error("Failed to load reCAPTCHA script."))},document.head.appendChild(e)})}async function o(c){const r=p.getConfig("RECAPTCHA_ID");if(t.value||await a(r),typeof window.grecaptcha>"u")throw new Error("grecaptcha is not available.");return new Promise((n,i)=>{window.grecaptcha.ready(()=>{window.grecaptcha.execute(r,{action:c}).then(e=>{n(e)}).catch(e=>{i(e)})})})}return{loadScript:a,getRecaptchaToken:o,isScriptLoaded:t}}export{d as u};
