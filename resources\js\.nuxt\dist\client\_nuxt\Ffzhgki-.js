import{_ as h}from"./BWS-stcc.js";import{d as f,f as v,g as y,h as w,i as x,j as n,k as j,c as i,o as c,l as r,b as t,w as k,t as l,m as L,n as d,p as D,v as E,e as N,y as T}from"./D_zVjEfm.js";const B={class:"breadcrumb-area"},H={class:"top-breadcrumb-area bg-img bg-overlay d-flex align-items-center justify-content-center",style:{"background-image":"url(/img/bg-img/about2_bg.jpg)"}},M={class:"container"},V={class:"row"},C={class:"col-12"},R={"aria-label":"breadcrumb"},S={class:"breadcrumb"},A={class:"breadcrumb-item"},O={class:"breadcrumb-item active"},$={class:"about-us-area section-padding-0-70"},q={class:"container"},z={class:"row justify-content-between"},F={class:"col-12 col-lg-12"},G={class:"row align-items-center justify-content-between"},I=["innerHTML"],Q=f({inheritAttrs:!1,__name:"[id]",props:{},setup(J){const a=v();y();const _=w();x();const s=n({eposttitle:"",epostbody:""}),u=n({id:_.params.id}),p=async()=>{try{let e=await a.post("api/epost/show",u);if(e.resultcode=="0")Object.assign(s,e.data);else throw new Error(e.resultmessage)}catch(e){T.formElError(e),console.error(e)}finally{}};return j(async()=>{p()}),(e,o)=>{const m=h,b=D,g=E;return c(),i("div",null,[r(m,{title:s.eposttitle,description:"我們提供專業的服務項目",defaultTitle:"關於我們"},null,8,["title"]),t("div",B,[t("div",H,[t("h2",null,l(s.eposttitle),1)]),t("div",M,[t("div",V,[t("div",C,[t("nav",R,[t("ol",S,[t("li",A,[r(b,{to:{path:"/"}},{default:L(()=>o[0]||(o[0]=[d(" 首頁")])),_:1})]),t("li",O,l(s.eposttitle),1)])])])])])]),k((c(),i("section",$,[t("div",q,[t("div",z,[t("div",F,[o[1]||(o[1]=t("div",{class:"section-heading2 text-center"},[t("h2",null,"關於歐悅設計"),t("p",null,[t("span",{style:{color:"#fd585c"}},"/"),d(" 我們不只創造空間，更創造品牌價值。 "),t("span",{style:{color:"#fd585c"}},"/")])],-1)),t("div",G,[t("div",{innerHTML:s.epostbody},null,8,I)])])])])])),[[g,N(a).getLoading()]])])}}});export{Q as default};
