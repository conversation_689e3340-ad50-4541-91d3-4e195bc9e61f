import{_ as S}from"./BWS-stcc.js";import{_ as O}from"./jBNyKXjo.js";import V from"./sXr78Ku-.js";import j from"./HHVucgb9.js";import{d as B,f as F,u as R,a as x,i as P,r as v,j as _,k as U,c as s,o as r,l as u,w as z,e as l,v as A,s as p,b as a,F as m,n as b,y as c,q,t as y}from"./D_zVjEfm.js";import{_ as G}from"./CF83mHfB.js";import"./G4Mf9Qin.js";const I={class:"table-responsive-md"},J={class:"table table-striped table-hover table-bordered table-fixed"},K=["innerHTML"],Q=["innerHTML"],W=["action"],X=["name"],Y=["innerHTML"],Z=["action"],tt={style:{display:"none"}},mt=B({__name:"index",props:{},setup(et){const h=F();R(),x(),P();const D=v("");let i=_({data:[]});v("");const n=_({search:"",searchname:"",searchdatename:"",page:1,sortname:"",sorttype:""});_({x:""});const C=async t=>{n.page=t,await d()},g=async t=>{n.page=1,n.sortname=t,n.sorttype=n.sorttype=="asc"?"desc":"asc",await d()},L=async t=>{n.page=1,n.sortname="",Object.assign(n,t),await d()},M=async(t=!1)=>{t&&await d()},d=async()=>{try{let t=await h.post("api/admin/formquerylog",n);if(t.resultcode=="0")Object.assign(i,t.data);else throw new Error(t.resultmessage)}catch(t){c.alert(t.message,"Error","error"),console.error(t)}};return U(async()=>{await d()}),(t,o)=>{const T=S,H=O,N=V,$=j,E=A;return r(),s(m,null,[u(T,{title:l(i).title,defaultTitle:""},null,8,["title"]),z((r(),s("div",null,[u(H,{id:"formquerylog",refs:"breadcrumb"}),u(N,{searchNames:{"":"全部","formquerylog.pagename":"功能名稱","formquerylog.pathinfo":"程式位置","formquerylog.formbody":"FORM欄位值","formquerylog.querybody":"get內容","formquerylog.raw":"raw內容"},searchDateNames:{"formquerylog.created_at":"建立日期"},onOnSearch:L}),l(i).data.length==0?(r(),s(m,{key:0},[b(" No Data ")],64)):p("",!0),a("div",I,[a("table",J,[a("thead",null,[a("tr",null,[a("th",{width:"",onClick:o[0]||(o[0]=e=>g("pagename")),class:"sortable"}," 功能名稱/程式位置 "),a("th",{width:"",onClick:o[1]||(o[1]=e=>g("formbody")),class:"sortable"}," form欄位值/raw "),a("th",{width:"",onClick:o[2]||(o[2]=e=>g("created_at")),class:"sortable"},"建立日期")])]),a("tbody",null,[("utils"in t?t.utils:l(c)).isEmpty(l(i).data)==!1?(r(!0),s(m,{key:0},q(l(i).data,(e,w)=>{var k;return r(),s("tr",{key:w},[a("td",null,[b(y(e.pagename)+" ",1),o[4]||(o[4]=a("p",null,null,-1)),b(" "+y(e.pathinfo)+" ",1),a("div",{innerHTML:("utils"in t?t.utils:l(c)).vbcrlf(e.querybody)},null,8,K)]),a("td",null,[e.formbody?(r(),s("div",{key:0,innerHTML:e.formbody},null,8,Q)):p("",!0),("utils"in t?t.utils:l(c)).isEmpty(e.formbody)?("utils"in t?t.utils:l(c)).isEmpty(e.raw)?p("",!0):(r(),s(m,{key:2},[a("div",{innerHTML:e.raw},null,8,Y),a("form",{method:"post",action:e.pathinfo,target:"_blank"},[a("textarea",tt,y(e.raw),1),o[6]||(o[6]=a("button",{class:"btn btn-primary"},"模擬測試",-1))],8,Z)],64)):(r(),s("form",{key:1,method:"post",action:e.pathinfo,target:"_blank"},[(r(!0),s(m,null,q((k=e.formbody)==null?void 0:k.split("&"),f=>(r(),s(m,{key:w},[f.includes("=")?(r(),s("textarea",{key:f,name:f.split("=")[0],style:{display:"none"}},y(f.split("=")[1]),9,X)):p("",!0)],64))),128)),o[5]||(o[5]=a("button",{class:"btn btn-primary"},"模擬測試",-1))],8,W))]),a("td",null,y(e.created_at),1)])}),128)):p("",!0)])])]),u($,{total:l(i).total,page:n.page,"onUpdate:page":o[3]||(o[3]=e=>n.page=e),onCurrentChange:C},null,8,["total","page"]),u(G,{ref_key:"myDialog",ref:D,onClosedDialog:M},null,512)])),[[E,l(h).getLoading()]])],64)}}});export{mt as default};
