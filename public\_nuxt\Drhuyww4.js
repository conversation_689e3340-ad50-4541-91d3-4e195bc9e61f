import{_ as j}from"./BWS-stcc.js";import{d as E,f as T,g as D,h as z,i as C,j as d,k as N,c as o,o as a,l as n,b as s,w as L,m as V,n as p,p as B,v as O,e as F,F as m,q as f,t as h,s as H,y as v,z as R}from"./D_zVjEfm.js";import{_ as q}from"./4qmtJa-f.js";import{_ as M}from"./I37qKz7r.js";const A={class:"breadcrumb-area"},G={class:"container"},I={class:"row"},J={class:"col-12"},K={"aria-label":"breadcrumb"},P={class:"breadcrumb"},Q={class:"breadcrumb-item"},U={class:"about-us-area section-padding-0-70"},W={class:"container"},X={class:"row justify-content-between"},Y={class:"col-12 col-lg-12"},Z={class:"row align-items-center justify-content-between"},ss={class:"alazea-portfolio-area portfolio-page bg-gray section-padding-70-40"},ts={class:"container"},es={class:"row"},os={class:"col-12"},as={class:"alazea-benefits-area"},is={class:"row"},ns={class:"wraper"},rs={class:"alazea-portfolio-area portfolio-page section-padding-70-70"},cs={class:"container"},ls={key:0,class:"customer-logos"},ps=E({__name:"[id]",props:{},setup(ds){const r=T();D(),z(),C();const u=d({data:[],total:0}),c=d({data:[],total:0}),_=d({search:"",page:1,sortname:"",sorttype:""}),b=async()=>{try{let t=await r.post("api/serviceitem",_);if(t.resultcode=="0")Object.assign(u,t.data);else throw new Error(t.resultmessage)}catch(t){v.formElError(t),console.error(t)}finally{}},w=async()=>{try{let t=await r.post("api/customer",_);if(t.resultcode=="0")Object.assign(c,t.data),await R(),y();else throw new Error(t.resultmessage)}catch(t){v.formElError(t),console.error(t)}finally{}},y=()=>{if(typeof window<"u"&&window.$){const t=window.$;t.fn.slick&&(t(".customer-logos").hasClass("slick-initialized")&&t(".customer-logos").slick("unslick"),t(".customer-logos").slick({slidesToShow:6,slidesToScroll:1,autoplay:!0,autoplaySpeed:1e3,arrows:!1,dots:!1,pauseOnHover:!1,responsive:[{breakpoint:768,settings:{slidesToShow:4}},{breakpoint:520,settings:{slidesToShow:2}}]}))}};return N(async()=>{b(),w()}),(t,e)=>{const k=j,x=B,S=q,g=M,$=O;return a(),o(m,null,[n(k,{title:"服務項目",description:"我們提供專業的服務項目",defaultTitle:"服務項目"}),s("div",A,[e[2]||(e[2]=s("div",{class:"top-breadcrumb-area bg-img bg-overlay d-flex align-items-center justify-content-center",style:{"background-image":"url(/img/bg-img/about2_bg.jpg)"}},[s("h2",null,"服務項目")],-1)),s("div",G,[s("div",I,[s("div",J,[s("nav",K,[s("ol",P,[s("li",Q,[n(x,{to:{path:"/"}},{default:V(()=>e[0]||(e[0]=[p(" 首頁")])),_:1})]),e[1]||(e[1]=s("li",{class:"breadcrumb-item active"},"服務項目",-1))])])])])])]),s("section",U,[s("div",W,[s("div",X,[s("div",Y,[e[3]||(e[3]=s("div",{class:"section-heading2 text-center"},[s("h2",null,"關於歐悅設計"),s("p",null,[s("span",{style:{color:"#fd585c"}},"/"),p(" 我們不只創造空間，更創造品牌價值。 "),s("span",{style:{color:"#fd585c"}},"/")])],-1)),s("div",Z,[n(S,{id:"about"})])])])])]),L((a(),o("section",ss,[s("div",ts,[s("div",es,[s("div",os,[s("div",as,[s("div",is,[s("ul",ns,[(a(!0),o(m,null,f(u.data,(i,l)=>(a(),o("li",{key:l,class:"single-services-area col-12 col-sm-3"},[n(g,{width:"200",height:"150",src:`/images/serviceitem/${i.img}`,noimage:"/images/no-picture.gif"},null,8,["src"]),s("h5",null,h(i.title),1),s("p",null,h(i.memo),1)]))),128))])])])])])])])),[[$,F(r).getLoading()]]),s("section",rs,[e[4]||(e[4]=s("div",{class:"section-heading2 text-center"},[s("h2",null,"我們的客戶")],-1)),s("div",cs,[c.data.length>0?(a(),o("div",ls,[(a(!0),o(m,null,f(c.data,(i,l)=>(a(),o("div",{key:l,class:"customer-logo-item"},[n(g,{width:"150",height:"100",src:`/images/customer/${i.img}`,noimage:"/images/no-picture.gif",class:"img-fluid",alt:i.title},null,8,["src","alt"])]))),128))])):H("",!0)])])],64)}}});export{ps as default};
