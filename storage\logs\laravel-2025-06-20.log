[2025-06-20 00:00:44] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-20 00:00:45] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-20 00:00:47] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-20 00:00:47] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:16.76]
  
[2025-06-20 00:00:47] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.12]
  
[2025-06-20 00:00:47] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-20 00:00:47] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:27.26]
  
[2025-06-20 00:00:47] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:1.28]
  
[2025-06-20 00:00:47] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-20 00:00:47] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:22.04]
  
[2025-06-20 00:03:14] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/swagger/index.html","post":[],"raw":null} 
[2025-06-20 00:03:14] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/swagger/index.html","post":[],"raw":null} 
[2025-06-20 00:03:17] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/images/favicon.ico","post":[],"raw":null} 
[2025-06-20 00:04:01] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-20 00:04:01] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:21.58]
  
[2025-06-20 00:04:01] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:1.95]
  
[2025-06-20 00:04:01] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-20 00:04:01] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:14.14]
  
[2025-06-20 00:04:01] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.75]
  
[2025-06-20 00:04:01] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-20 00:04:01] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:32.42]
  
[2025-06-20 00:04:17] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-20 00:04:17] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:14.49]
  
[2025-06-20 00:04:17] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:2.73]
  
[2025-06-20 00:04:17] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-20 00:04:17] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:14.11]
  
[2025-06-20 00:04:17] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-20 00:04:17] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:26.16]
  
[2025-06-20 00:04:17] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.53]
  
[2025-06-20 00:04:31] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-20 00:04:31] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:10.25]
  
[2025-06-20 00:04:31] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-20 00:04:31] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.9]
  
[2025-06-20 00:04:31] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:23.54]
  
[2025-06-20 00:04:31] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:2.4]
  
[2025-06-20 00:04:31] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-20 00:04:31] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:30.16]
  
[2025-06-20 00:04:48] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-20 00:04:48] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:23.29]
  
[2025-06-20 00:04:48] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:2.55]
  
[2025-06-20 00:04:48] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-20 00:04:48] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-20 00:04:48] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:19.2]
  
[2025-06-20 00:04:48] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:0.88]
  
[2025-06-20 00:04:48] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:22.39]
  
[2025-06-20 00:05:14] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-20 00:05:14] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-20 00:05:14] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:10.77]
  
[2025-06-20 00:05:14] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:1.68]
  
[2025-06-20 00:05:14] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-20 00:05:14] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:29.69]
  
[2025-06-20 00:05:14] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.52]
  
[2025-06-20 00:05:14] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:9.13]
  
[2025-06-20 00:06:58] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-20 00:07:00] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-20 00:07:02] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-20 00:07:03] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:29.76]
  
[2025-06-20 00:07:03] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:2.8]
  
[2025-06-20 00:07:03] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-20 00:07:03] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:16.34]
  
[2025-06-20 00:07:03] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:2.46]
  
[2025-06-20 00:07:03] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-20 00:07:03] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:21.78]
  
[2025-06-20 00:08:21] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-20 00:08:23] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-20 00:08:25] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-20 00:08:25] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-20 00:08:25] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:17.47]
  
[2025-06-20 00:08:25] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.38]
  
[2025-06-20 00:08:25] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:7.01]
  
[2025-06-20 00:08:25] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:1.49]
  
[2025-06-20 00:08:25] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-20 00:08:25] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:22.86]
  
[2025-06-20 00:08:43] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/news","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-20 00:08:43] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
  [執行超過10秒:5.17]
  
[2025-06-20 00:08:43] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate,body from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.44]
  
[2025-06-20 00:08:58] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-20 00:09:00] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-20 00:09:02] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-20 00:09:02] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:25.84]
  
[2025-06-20 00:09:02] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.41]
  
[2025-06-20 00:09:02] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/news","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-20 00:09:02] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-20 00:09:02] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
  [執行超過10秒:16.84]
  
[2025-06-20 00:09:02] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate,body from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.11]
  
[2025-06-20 00:09:02] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-20 00:09:02] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:23.39]
  
[2025-06-20 00:09:02] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:1.74]
  
[2025-06-20 00:09:02] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:7.93]
  
[2025-06-20 00:09:46] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-20 00:09:48] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-20 00:09:51] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-20 00:09:51] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:15.94]
  
[2025-06-20 00:09:51] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:2.76]
  
[2025-06-20 00:09:51] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-20 00:09:51] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:28.38]
  
[2025-06-20 00:09:51] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:2.75]
  
[2025-06-20 00:09:51] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/news","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-20 00:09:51] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
  [執行超過10秒:16.32]
  
[2025-06-20 00:09:51] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate,body from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:2.98]
  
[2025-06-20 00:09:51] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-20 00:09:51] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:13.95]
  
[2025-06-20 00:10:07] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-20 00:10:08] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-20 00:10:10] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-20 00:10:10] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-20 00:10:10] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:6.86]
  
[2025-06-20 00:10:10] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-20 00:10:10] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:28.64]
  
[2025-06-20 00:10:10] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:1.56]
  
[2025-06-20 00:10:10] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/news","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-20 00:10:11] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:27.28]
  
[2025-06-20 00:10:11] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:0.74]
  
[2025-06-20 00:10:11] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
  [執行超過10秒:24.74]
  
[2025-06-20 00:10:11] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate,body from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.2]
  
