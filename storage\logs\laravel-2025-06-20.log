[2025-06-20 00:00:44] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-20 00:00:45] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-20 00:00:47] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-20 00:00:47] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:16.76]
  
[2025-06-20 00:00:47] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.12]
  
[2025-06-20 00:00:47] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-20 00:00:47] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:27.26]
  
[2025-06-20 00:00:47] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:1.28]
  
[2025-06-20 00:00:47] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-20 00:00:47] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:22.04]
  
