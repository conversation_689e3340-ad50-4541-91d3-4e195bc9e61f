import{r as e}from"./D_zVjEfm.js";const r=e([{label:"客戶名稱",name:"title",kind:"el-input",rules:[{required:!0,message:"客戶名稱 未填"}],value:"",isedit:!0,islist:!0,issearch:!0},{label:"圖",name:"field1",kind:"my-upload",props:{width:"300",height:"300",folder:"images/serviceitem",limit:1},rules:[{required:!1,message:"圖 未上傳"}],value:"",islist:!1,isedit:!0},{label:"開始日期",name:"begindate",kind:"my-dateform",props:{type:"date"},rules:[{required:!1,message:"建立日期 未填"}],value:"",isedit:!0,islist:!0,issearch:!0},{label:"結束日期",name:"closedate",kind:"my-dateform",props:{type:"date"},rules:[{required:!1,message:"建立日期 未填"}],value:"",isedit:!0,islist:!0,issearch:!0},{label:"順序",name:"boardsort",kind:"el-input",props:{type:"number"},rules:[{required:!1,message:"順序 未填"}],value:"",isedit:!0,islist:!0,issearch:!1,memo:"數字小到大"},{label:"建立日期",name:"created_at",kind:"my-dateform",props:{type:"date"},rules:[{required:!1,message:"建立日期 未填"}],value:"",islist:!0,issearch:!0}]);export{r as fields};
