// Generated by auto imports
export {}
declare global {
  const ElIconAddLocation: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['AddLocation']
  const ElIconAim: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Aim']
  const ElIconAlarmClock: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['AlarmClock']
  const ElIconApple: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Apple']
  const ElIconArrowDown: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ArrowDown']
  const ElIconArrowDownBold: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ArrowDownBold']
  const ElIconArrowLeft: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ArrowLeft']
  const ElIconArrowLeftBold: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ArrowLeftBold']
  const ElIconArrowRight: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ArrowRight']
  const ElIconArrowRightBold: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ArrowRightBold']
  const ElIconArrowUp: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ArrowUp']
  const ElIconArrowUpBold: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ArrowUpBold']
  const ElIconAvatar: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Avatar']
  const ElIconBack: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Back']
  const ElIconBaseball: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Baseball']
  const ElIconBasketball: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Basketball']
  const ElIconBell: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Bell']
  const ElIconBellFilled: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['BellFilled']
  const ElIconBicycle: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Bicycle']
  const ElIconBottom: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Bottom']
  const ElIconBottomLeft: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['BottomLeft']
  const ElIconBottomRight: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['BottomRight']
  const ElIconBowl: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Bowl']
  const ElIconBox: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Box']
  const ElIconBriefcase: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Briefcase']
  const ElIconBrush: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Brush']
  const ElIconBrushFilled: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['BrushFilled']
  const ElIconBurger: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Burger']
  const ElIconCalendar: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Calendar']
  const ElIconCamera: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Camera']
  const ElIconCameraFilled: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['CameraFilled']
  const ElIconCaretBottom: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['CaretBottom']
  const ElIconCaretLeft: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['CaretLeft']
  const ElIconCaretRight: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['CaretRight']
  const ElIconCaretTop: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['CaretTop']
  const ElIconCellphone: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Cellphone']
  const ElIconChatDotRound: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ChatDotRound']
  const ElIconChatDotSquare: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ChatDotSquare']
  const ElIconChatLineRound: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ChatLineRound']
  const ElIconChatLineSquare: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ChatLineSquare']
  const ElIconChatRound: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ChatRound']
  const ElIconChatSquare: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ChatSquare']
  const ElIconCheck: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Check']
  const ElIconChecked: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Checked']
  const ElIconCherry: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Cherry']
  const ElIconChicken: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Chicken']
  const ElIconChromeFilled: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ChromeFilled']
  const ElIconCircleCheck: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['CircleCheck']
  const ElIconCircleCheckFilled: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['CircleCheckFilled']
  const ElIconCircleClose: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['CircleClose']
  const ElIconCircleCloseFilled: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['CircleCloseFilled']
  const ElIconCirclePlus: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['CirclePlus']
  const ElIconCirclePlusFilled: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['CirclePlusFilled']
  const ElIconClock: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Clock']
  const ElIconClose: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Close']
  const ElIconCloseBold: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['CloseBold']
  const ElIconCloudy: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Cloudy']
  const ElIconCoffee: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Coffee']
  const ElIconCoffeeCup: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['CoffeeCup']
  const ElIconCoin: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Coin']
  const ElIconColdDrink: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ColdDrink']
  const ElIconCollection: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Collection']
  const ElIconCollectionTag: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['CollectionTag']
  const ElIconComment: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Comment']
  const ElIconCompass: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Compass']
  const ElIconConnection: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Connection']
  const ElIconCoordinate: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Coordinate']
  const ElIconCopyDocument: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['CopyDocument']
  const ElIconCpu: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Cpu']
  const ElIconCreditCard: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['CreditCard']
  const ElIconCrop: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Crop']
  const ElIconDArrowLeft: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['DArrowLeft']
  const ElIconDArrowRight: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['DArrowRight']
  const ElIconDCaret: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['DCaret']
  const ElIconDataAnalysis: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['DataAnalysis']
  const ElIconDataBoard: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['DataBoard']
  const ElIconDataLine: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['DataLine']
  const ElIconDelete: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Delete']
  const ElIconDeleteFilled: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['DeleteFilled']
  const ElIconDeleteLocation: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['DeleteLocation']
  const ElIconDessert: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Dessert']
  const ElIconDiscount: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Discount']
  const ElIconDish: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Dish']
  const ElIconDishDot: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['DishDot']
  const ElIconDocument: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Document']
  const ElIconDocumentAdd: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['DocumentAdd']
  const ElIconDocumentChecked: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['DocumentChecked']
  const ElIconDocumentCopy: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['DocumentCopy']
  const ElIconDocumentDelete: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['DocumentDelete']
  const ElIconDocumentRemove: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['DocumentRemove']
  const ElIconDownload: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Download']
  const ElIconDrizzling: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Drizzling']
  const ElIconEdit: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Edit']
  const ElIconEditPen: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['EditPen']
  const ElIconEleme: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Eleme']
  const ElIconElemeFilled: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ElemeFilled']
  const ElIconElementPlus: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ElementPlus']
  const ElIconExpand: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Expand']
  const ElIconFailed: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Failed']
  const ElIconFemale: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Female']
  const ElIconFiles: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Files']
  const ElIconFilm: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Film']
  const ElIconFilter: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Filter']
  const ElIconFinished: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Finished']
  const ElIconFirstAidKit: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['FirstAidKit']
  const ElIconFlag: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Flag']
  const ElIconFold: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Fold']
  const ElIconFolder: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Folder']
  const ElIconFolderAdd: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['FolderAdd']
  const ElIconFolderChecked: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['FolderChecked']
  const ElIconFolderDelete: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['FolderDelete']
  const ElIconFolderOpened: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['FolderOpened']
  const ElIconFolderRemove: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['FolderRemove']
  const ElIconFood: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Food']
  const ElIconFootball: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Football']
  const ElIconForkSpoon: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ForkSpoon']
  const ElIconFries: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Fries']
  const ElIconFullScreen: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['FullScreen']
  const ElIconGoblet: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Goblet']
  const ElIconGobletFull: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['GobletFull']
  const ElIconGobletSquare: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['GobletSquare']
  const ElIconGobletSquareFull: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['GobletSquareFull']
  const ElIconGoldMedal: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['GoldMedal']
  const ElIconGoods: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Goods']
  const ElIconGoodsFilled: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['GoodsFilled']
  const ElIconGrape: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Grape']
  const ElIconGrid: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Grid']
  const ElIconGuide: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Guide']
  const ElIconHandbag: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Handbag']
  const ElIconHeadset: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Headset']
  const ElIconHelp: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Help']
  const ElIconHelpFilled: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['HelpFilled']
  const ElIconHide: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Hide']
  const ElIconHistogram: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Histogram']
  const ElIconHomeFilled: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['HomeFilled']
  const ElIconHotWater: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['HotWater']
  const ElIconHouse: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['House']
  const ElIconIceCream: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['IceCream']
  const ElIconIceCreamRound: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['IceCreamRound']
  const ElIconIceCreamSquare: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['IceCreamSquare']
  const ElIconIceDrink: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['IceDrink']
  const ElIconIceTea: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['IceTea']
  const ElIconInfoFilled: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['InfoFilled']
  const ElIconIphone: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Iphone']
  const ElIconKey: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Key']
  const ElIconKnifeFork: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['KnifeFork']
  const ElIconLightning: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Lightning']
  const ElIconLink: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Link']
  const ElIconList: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['List']
  const ElIconLoading: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Loading']
  const ElIconLocation: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Location']
  const ElIconLocationFilled: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['LocationFilled']
  const ElIconLocationInformation: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['LocationInformation']
  const ElIconLock: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Lock']
  const ElIconLollipop: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Lollipop']
  const ElIconMagicStick: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['MagicStick']
  const ElIconMagnet: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Magnet']
  const ElIconMale: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Male']
  const ElIconManagement: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Management']
  const ElIconMapLocation: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['MapLocation']
  const ElIconMedal: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Medal']
  const ElIconMemo: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Memo']
  const ElIconMenu: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Menu']
  const ElIconMessage: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Message']
  const ElIconMessageBox: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['MessageBox']
  const ElIconMic: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Mic']
  const ElIconMicrophone: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Microphone']
  const ElIconMilkTea: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['MilkTea']
  const ElIconMinus: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Minus']
  const ElIconMoney: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Money']
  const ElIconMonitor: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Monitor']
  const ElIconMoon: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Moon']
  const ElIconMoonNight: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['MoonNight']
  const ElIconMore: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['More']
  const ElIconMoreFilled: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['MoreFilled']
  const ElIconMostlyCloudy: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['MostlyCloudy']
  const ElIconMouse: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Mouse']
  const ElIconMug: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Mug']
  const ElIconMute: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Mute']
  const ElIconMuteNotification: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['MuteNotification']
  const ElIconNoSmoking: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['NoSmoking']
  const ElIconNotebook: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Notebook']
  const ElIconNotification: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Notification']
  const ElIconOdometer: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Odometer']
  const ElIconOfficeBuilding: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['OfficeBuilding']
  const ElIconOpen: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Open']
  const ElIconOperation: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Operation']
  const ElIconOpportunity: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Opportunity']
  const ElIconOrange: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Orange']
  const ElIconPaperclip: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Paperclip']
  const ElIconPartlyCloudy: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['PartlyCloudy']
  const ElIconPear: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Pear']
  const ElIconPhone: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Phone']
  const ElIconPhoneFilled: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['PhoneFilled']
  const ElIconPicture: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Picture']
  const ElIconPictureFilled: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['PictureFilled']
  const ElIconPictureRounded: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['PictureRounded']
  const ElIconPieChart: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['PieChart']
  const ElIconPlace: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Place']
  const ElIconPlatform: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Platform']
  const ElIconPlus: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Plus']
  const ElIconPointer: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Pointer']
  const ElIconPosition: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Position']
  const ElIconPostcard: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Postcard']
  const ElIconPouring: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Pouring']
  const ElIconPresent: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Present']
  const ElIconPriceTag: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['PriceTag']
  const ElIconPrinter: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Printer']
  const ElIconPromotion: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Promotion']
  const ElIconQuartzWatch: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['QuartzWatch']
  const ElIconQuestionFilled: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['QuestionFilled']
  const ElIconRank: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Rank']
  const ElIconReading: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Reading']
  const ElIconReadingLamp: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ReadingLamp']
  const ElIconRefresh: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Refresh']
  const ElIconRefreshLeft: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['RefreshLeft']
  const ElIconRefreshRight: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['RefreshRight']
  const ElIconRefrigerator: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Refrigerator']
  const ElIconRemove: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Remove']
  const ElIconRemoveFilled: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['RemoveFilled']
  const ElIconRight: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Right']
  const ElIconScaleToOriginal: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ScaleToOriginal']
  const ElIconSchool: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['School']
  const ElIconScissor: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Scissor']
  const ElIconSearch: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Search']
  const ElIconSelect: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Select']
  const ElIconSell: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Sell']
  const ElIconSemiSelect: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['SemiSelect']
  const ElIconService: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Service']
  const ElIconSetUp: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['SetUp']
  const ElIconSetting: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Setting']
  const ElIconShare: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Share']
  const ElIconShip: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Ship']
  const ElIconShop: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Shop']
  const ElIconShoppingBag: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ShoppingBag']
  const ElIconShoppingCart: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ShoppingCart']
  const ElIconShoppingCartFull: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ShoppingCartFull']
  const ElIconShoppingTrolley: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ShoppingTrolley']
  const ElIconSmoking: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Smoking']
  const ElIconSoccer: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Soccer']
  const ElIconSoldOut: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['SoldOut']
  const ElIconSort: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Sort']
  const ElIconSortDown: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['SortDown']
  const ElIconSortUp: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['SortUp']
  const ElIconStamp: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Stamp']
  const ElIconStar: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Star']
  const ElIconStarFilled: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['StarFilled']
  const ElIconStopwatch: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Stopwatch']
  const ElIconSuccessFilled: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['SuccessFilled']
  const ElIconSugar: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Sugar']
  const ElIconSuitcase: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Suitcase']
  const ElIconSuitcaseLine: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['SuitcaseLine']
  const ElIconSunny: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Sunny']
  const ElIconSunrise: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Sunrise']
  const ElIconSunset: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Sunset']
  const ElIconSwitch: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Switch']
  const ElIconSwitchButton: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['SwitchButton']
  const ElIconSwitchFilled: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['SwitchFilled']
  const ElIconTakeawayBox: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['TakeawayBox']
  const ElIconTicket: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Ticket']
  const ElIconTickets: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Tickets']
  const ElIconTimer: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Timer']
  const ElIconToiletPaper: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ToiletPaper']
  const ElIconTools: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Tools']
  const ElIconTop: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Top']
  const ElIconTopLeft: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['TopLeft']
  const ElIconTopRight: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['TopRight']
  const ElIconTrendCharts: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['TrendCharts']
  const ElIconTrophy: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Trophy']
  const ElIconTrophyBase: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['TrophyBase']
  const ElIconTurnOff: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['TurnOff']
  const ElIconUmbrella: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Umbrella']
  const ElIconUnlock: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Unlock']
  const ElIconUpload: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Upload']
  const ElIconUploadFilled: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['UploadFilled']
  const ElIconUser: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['User']
  const ElIconUserFilled: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['UserFilled']
  const ElIconVan: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Van']
  const ElIconVideoCamera: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['VideoCamera']
  const ElIconVideoCameraFilled: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['VideoCameraFilled']
  const ElIconVideoPause: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['VideoPause']
  const ElIconVideoPlay: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['VideoPlay']
  const ElIconView: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['View']
  const ElIconWallet: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Wallet']
  const ElIconWalletFilled: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['WalletFilled']
  const ElIconWarnTriangleFilled: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['WarnTriangleFilled']
  const ElIconWarning: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Warning']
  const ElIconWarningFilled: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['WarningFilled']
  const ElIconWatch: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Watch']
  const ElIconWatermelon: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Watermelon']
  const ElIconWindPower: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['WindPower']
  const ElIconZoomIn: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ZoomIn']
  const ElIconZoomOut: typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ZoomOut']
  const ElLoading: typeof import('../../../../../../nuxt/node_modules/element-plus/es/components/loading/index')['ElLoading']
  const ElMessage: typeof import('../../../../../../nuxt/node_modules/element-plus/es/components/message/index')['ElMessage']
  const ElMessageBox: typeof import('../../../../../../nuxt/node_modules/element-plus/es/components/message-box/index')['ElMessageBox']
  const ElNotification: typeof import('../../../../../../nuxt/node_modules/element-plus/es/components/notification/index')['ElNotification']
  const ID_INJECTION_KEY: typeof import('../../../../../../nuxt/node_modules/element-plus/es/hooks/use-id/index')['ID_INJECTION_KEY']
  const ZINDEX_INJECTION_KEY: typeof import('../../../../../../nuxt/node_modules/element-plus/es/hooks/use-z-index/index')['ZINDEX_INJECTION_KEY']
  const abortNavigation: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/router')['abortNavigation']
  const acceptHMRUpdate: typeof import('../../../../../../nuxt/node_modules/@pinia/nuxt/dist/runtime/composables')['acceptHMRUpdate']
  const addRouteMiddleware: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/router')['addRouteMiddleware']
  const asyncComputed: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['asyncComputed']
  const autoResetRef: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['autoResetRef']
  const callOnce: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/once')['callOnce']
  const cancelIdleCallback: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/compat/idle-callback')['cancelIdleCallback']
  const clearError: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/error')['clearError']
  const clearNuxtData: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/asyncData')['clearNuxtData']
  const clearNuxtState: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/state')['clearNuxtState']
  const computed: typeof import('vue')['computed']
  const computedAsync: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['computedAsync']
  const computedEager: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['computedEager']
  const computedInject: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['computedInject']
  const computedWithControl: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['computedWithControl']
  const controlledComputed: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['controlledComputed']
  const controlledRef: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['controlledRef']
  const createError: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/error')['createError']
  const createEventHook: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['createEventHook']
  const createGlobalState: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['createGlobalState']
  const createHttp: typeof import('../../utils/http')['createHttp']
  const createInjectionState: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['createInjectionState']
  const createReactiveFn: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['createReactiveFn']
  const createReusableTemplate: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['createReusableTemplate']
  const createSharedComposable: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['createSharedComposable']
  const createTemplatePromise: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['createTemplatePromise']
  const createUnrefFn: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['createUnrefFn']
  const crypto: typeof import('../../utils/crypto')['crypto']
  const customRef: typeof import('vue')['customRef']
  const debouncedRef: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['debouncedRef']
  const debouncedWatch: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['debouncedWatch']
  const defineAppConfig: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/nuxt')['defineAppConfig']
  const defineAsyncComponent: typeof import('vue')['defineAsyncComponent']
  const defineComponent: typeof import('vue')['defineComponent']
  const defineNuxtComponent: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/component')['defineNuxtComponent']
  const defineNuxtLink: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/components/nuxt-link')['defineNuxtLink']
  const defineNuxtPlugin: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/nuxt')['defineNuxtPlugin']
  const defineNuxtRouteMiddleware: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/router')['defineNuxtRouteMiddleware']
  const definePageMeta: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/pages/runtime/composables')['definePageMeta']
  const definePayloadPlugin: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/nuxt')['definePayloadPlugin']
  const definePayloadReducer: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/payload')['definePayloadReducer']
  const definePayloadReviver: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/payload')['definePayloadReviver']
  const defineStore: typeof import('../../../../../../nuxt/node_modules/@pinia/nuxt/dist/runtime/composables')['defineStore']
  const eagerComputed: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['eagerComputed']
  const effect: typeof import('vue')['effect']
  const effectScope: typeof import('vue')['effectScope']
  const emitter: typeof import('../../utils/emitter')['default']
  const extendRef: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['extendRef']
  const getAppManifest: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/manifest')['getAppManifest']
  const getCurrentInstance: typeof import('vue')['getCurrentInstance']
  const getCurrentScope: typeof import('vue')['getCurrentScope']
  const getRouteRules: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/manifest')['getRouteRules']
  const h: typeof import('vue')['h']
  const hasInjectionContext: typeof import('vue')['hasInjectionContext']
  const ignorableWatch: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['ignorableWatch']
  const inject: typeof import('vue')['inject']
  const injectHead: typeof import('../../../../../../nuxt/node_modules/@unhead/vue')['injectHead']
  const injectLocal: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['injectLocal']
  const isDefined: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['isDefined']
  const isNuxtError: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/error')['isNuxtError']
  const isPrerendered: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/payload')['isPrerendered']
  const isProxy: typeof import('vue')['isProxy']
  const isReactive: typeof import('vue')['isReactive']
  const isReadonly: typeof import('vue')['isReadonly']
  const isRef: typeof import('vue')['isRef']
  const isShallow: typeof import('vue')['isShallow']
  const isVue2: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/compat/vue-demi')['isVue2']
  const isVue3: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/compat/vue-demi')['isVue3']
  const jquery: typeof import('../../utils/jquery')['default']
  const loadPayload: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/payload')['loadPayload']
  const makeDestructurable: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['makeDestructurable']
  const markRaw: typeof import('vue')['markRaw']
  const mergeModels: typeof import('vue')['mergeModels']
  const navigateTo: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/router')['navigateTo']
  const nextTick: typeof import('vue')['nextTick']
  const onActivated: typeof import('vue')['onActivated']
  const onBeforeMount: typeof import('vue')['onBeforeMount']
  const onBeforeRouteLeave: typeof import('vue-router')['onBeforeRouteLeave']
  const onBeforeRouteUpdate: typeof import('vue-router')['onBeforeRouteUpdate']
  const onBeforeUnmount: typeof import('vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('vue')['onBeforeUpdate']
  const onClickOutside: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['onClickOutside']
  const onDeactivated: typeof import('vue')['onDeactivated']
  const onErrorCaptured: typeof import('vue')['onErrorCaptured']
  const onKeyStroke: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['onKeyStroke']
  const onLongPress: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['onLongPress']
  const onMounted: typeof import('vue')['onMounted']
  const onNuxtReady: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/ready')['onNuxtReady']
  const onPrehydrate: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/ssr')['onPrehydrate']
  const onRenderTracked: typeof import('vue')['onRenderTracked']
  const onRenderTriggered: typeof import('vue')['onRenderTriggered']
  const onScopeDispose: typeof import('vue')['onScopeDispose']
  const onServerPrefetch: typeof import('vue')['onServerPrefetch']
  const onStartTyping: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['onStartTyping']
  const onUnmounted: typeof import('vue')['onUnmounted']
  const onUpdated: typeof import('vue')['onUpdated']
  const pausableWatch: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['pausableWatch']
  const prefetchComponents: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/preload')['prefetchComponents']
  const preloadComponents: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/preload')['preloadComponents']
  const preloadPayload: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/payload')['preloadPayload']
  const preloadRouteComponents: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/preload')['preloadRouteComponents']
  const prerenderRoutes: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/ssr')['prerenderRoutes']
  const provide: typeof import('vue')['provide']
  const provideGlobalConfig: typeof import('../../../../../../nuxt/node_modules/element-plus/es/components/config-provider/src/hooks/use-global-config')['provideGlobalConfig']
  const provideLocal: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['provideLocal']
  const proxyRefs: typeof import('vue')['proxyRefs']
  const reactify: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['reactify']
  const reactifyObject: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['reactifyObject']
  const reactive: typeof import('vue')['reactive']
  const reactiveComputed: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['reactiveComputed']
  const reactiveOmit: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['reactiveOmit']
  const reactivePick: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['reactivePick']
  const readonly: typeof import('vue')['readonly']
  const ref: typeof import('vue')['ref']
  const refAutoReset: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['refAutoReset']
  const refDebounced: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['refDebounced']
  const refDefault: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['refDefault']
  const refThrottled: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['refThrottled']
  const refWithControl: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['refWithControl']
  const refreshCookie: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/cookie')['refreshCookie']
  const refreshNuxtData: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/asyncData')['refreshNuxtData']
  const reloadNuxtApp: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/chunk')['reloadNuxtApp']
  const rendervnode: typeof import('../../utils/rendervnode')['default']
  const requestIdleCallback: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/compat/idle-callback')['requestIdleCallback']
  const resolveComponent: typeof import('vue')['resolveComponent']
  const resolveRef: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['resolveRef']
  const resolveUnref: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['resolveUnref']
  const setInterval: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/compat/interval')['setInterval']
  const setPageLayout: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/router')['setPageLayout']
  const setResponseStatus: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/ssr')['setResponseStatus']
  const shallowReactive: typeof import('vue')['shallowReactive']
  const shallowReadonly: typeof import('vue')['shallowReadonly']
  const shallowRef: typeof import('vue')['shallowRef']
  const showError: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/error')['showError']
  const storeToRefs: typeof import('../../../../../../nuxt/node_modules/@pinia/nuxt/dist/runtime/composables')['storeToRefs']
  const syncRef: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['syncRef']
  const syncRefs: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['syncRefs']
  const templateRef: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['templateRef']
  const throttledRef: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['throttledRef']
  const throttledWatch: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['throttledWatch']
  const toRaw: typeof import('vue')['toRaw']
  const toReactive: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['toReactive']
  const toRef: typeof import('vue')['toRef']
  const toRefs: typeof import('vue')['toRefs']
  const toValue: typeof import('vue')['toValue']
  const triggerRef: typeof import('vue')['triggerRef']
  const tryOnBeforeMount: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['tryOnBeforeMount']
  const tryOnBeforeUnmount: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['tryOnBeforeUnmount']
  const tryOnMounted: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['tryOnMounted']
  const tryOnScopeDispose: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['tryOnScopeDispose']
  const tryOnUnmounted: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['tryOnUnmounted']
  const tryUseNuxtApp: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/nuxt')['tryUseNuxtApp']
  const unref: typeof import('vue')['unref']
  const unrefElement: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['unrefElement']
  const until: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['until']
  const updateAppConfig: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/config')['updateAppConfig']
  const useActiveElement: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useActiveElement']
  const useAnimate: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useAnimate']
  const useAppConfig: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/config')['useAppConfig']
  const useArrayDifference: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useArrayDifference']
  const useArrayEvery: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useArrayEvery']
  const useArrayFilter: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useArrayFilter']
  const useArrayFind: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useArrayFind']
  const useArrayFindIndex: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useArrayFindIndex']
  const useArrayFindLast: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useArrayFindLast']
  const useArrayIncludes: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useArrayIncludes']
  const useArrayJoin: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useArrayJoin']
  const useArrayMap: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useArrayMap']
  const useArrayReduce: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useArrayReduce']
  const useArraySome: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useArraySome']
  const useArrayUnique: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useArrayUnique']
  const useAsyncData: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/asyncData')['useAsyncData']
  const useAsyncQueue: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useAsyncQueue']
  const useAsyncState: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useAsyncState']
  const useAttrs: typeof import('vue')['useAttrs']
  const useBase64: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useBase64']
  const useBattery: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useBattery']
  const useBluetooth: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useBluetooth']
  const useBreakpoints: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useBreakpoints']
  const useBroadcastChannel: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useBroadcastChannel']
  const useBrowserLocation: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useBrowserLocation']
  const useCached: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useCached']
  const useClipboard: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useClipboard']
  const useClipboardItems: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useClipboardItems']
  const useCloned: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useCloned']
  const useColorMode: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useColorMode']
  const useConfirmDialog: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useConfirmDialog']
  const useCookie: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/cookie')['useCookie']
  const useCounter: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useCounter']
  const useCssModule: typeof import('vue')['useCssModule']
  const useCssVar: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useCssVar']
  const useCssVars: typeof import('vue')['useCssVars']
  const useCurrentElement: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useCurrentElement']
  const useCycleList: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useCycleList']
  const useDark: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useDark']
  const useDataStore: typeof import('../../stores/index')['useDataStore']
  const useDateFormat: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useDateFormat']
  const useDebounce: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useDebounce']
  const useDebounceFn: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useDebounceFn']
  const useDebouncedRefHistory: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useDebouncedRefHistory']
  const useDeviceMotion: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useDeviceMotion']
  const useDeviceOrientation: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useDeviceOrientation']
  const useDevicePixelRatio: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useDevicePixelRatio']
  const useDevicesList: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useDevicesList']
  const useDisplayMedia: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useDisplayMedia']
  const useDocumentVisibility: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useDocumentVisibility']
  const useDraggable: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useDraggable']
  const useDropZone: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useDropZone']
  const useElementBounding: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useElementBounding']
  const useElementByPoint: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useElementByPoint']
  const useElementHover: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useElementHover']
  const useElementSize: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useElementSize']
  const useElementVisibility: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useElementVisibility']
  const useError: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/error')['useError']
  const useEventBus: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useEventBus']
  const useEventListener: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useEventListener']
  const useEventSource: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useEventSource']
  const useEyeDropper: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useEyeDropper']
  const useFavicon: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useFavicon']
  const useFetch: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/fetch')['useFetch']
  const useFileDialog: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useFileDialog']
  const useFileSystemAccess: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useFileSystemAccess']
  const useFocus: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useFocus']
  const useFocusWithin: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useFocusWithin']
  const useFps: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useFps']
  const useFullscreen: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useFullscreen']
  const useGamepad: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useGamepad']
  const useGeolocation: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useGeolocation']
  const useHead: typeof import('../../../../../../nuxt/node_modules/@unhead/vue')['useHead']
  const useHeadSafe: typeof import('../../../../../../nuxt/node_modules/@unhead/vue')['useHeadSafe']
  const useHydration: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/hydrate')['useHydration']
  const useId: typeof import('vue')['useId']
  const useIdle: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useIdle']
  const useImage: typeof import('../../../../../../nuxt/node_modules/@nuxt/image/dist/runtime/composables')['useImage']
  const useInfiniteScroll: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useInfiniteScroll']
  const useIntersectionObserver: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useIntersectionObserver']
  const useInterval: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useInterval']
  const useIntervalFn: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useIntervalFn']
  const useKeyModifier: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useKeyModifier']
  const useLastChanged: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useLastChanged']
  const useLazyAsyncData: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/asyncData')['useLazyAsyncData']
  const useLazyFetch: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/fetch')['useLazyFetch']
  const useLink: typeof import('vue-router')['useLink']
  const useLoadingIndicator: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/loading-indicator')['useLoadingIndicator']
  const useLocalStorage: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useLocalStorage']
  const useMagicKeys: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useMagicKeys']
  const useManualRefHistory: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useManualRefHistory']
  const useMediaControls: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useMediaControls']
  const useMediaQuery: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useMediaQuery']
  const useMemoize: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useMemoize']
  const useMemory: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useMemory']
  const useModel: typeof import('vue')['useModel']
  const useMounted: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useMounted']
  const useMouse: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useMouse']
  const useMouseInElement: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useMouseInElement']
  const useMousePressed: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useMousePressed']
  const useMutationObserver: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useMutationObserver']
  const useNavigatorLanguage: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useNavigatorLanguage']
  const useNetwork: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useNetwork']
  const useNow: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useNow']
  const useNuxtApp: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/nuxt')['useNuxtApp']
  const useNuxtData: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/asyncData')['useNuxtData']
  const useObjectUrl: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useObjectUrl']
  const useOffsetPagination: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useOffsetPagination']
  const useOnline: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useOnline']
  const usePageLeave: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['usePageLeave']
  const useParallax: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useParallax']
  const useParentElement: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useParentElement']
  const usePerformanceObserver: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['usePerformanceObserver']
  const usePermission: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['usePermission']
  const usePinia: typeof import('../../../../../../nuxt/node_modules/@pinia/nuxt/dist/runtime/composables')['usePinia']
  const usePointer: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['usePointer']
  const usePointerLock: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['usePointerLock']
  const usePointerSwipe: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['usePointerSwipe']
  const usePreferredColorScheme: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['usePreferredColorScheme']
  const usePreferredContrast: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['usePreferredContrast']
  const usePreferredDark: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['usePreferredDark']
  const usePreferredLanguages: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['usePreferredLanguages']
  const usePreferredReducedMotion: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['usePreferredReducedMotion']
  const usePreferredReducedTransparency: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['usePreferredReducedTransparency']
  const usePreviewMode: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/preview')['usePreviewMode']
  const usePrevious: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['usePrevious']
  const useRafFn: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useRafFn']
  const useRecaptcha: typeof import('../../composables/useRecaptcha')['useRecaptcha']
  const useRefHistory: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useRefHistory']
  const useRequestEvent: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/ssr')['useRequestEvent']
  const useRequestFetch: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/ssr')['useRequestFetch']
  const useRequestHeader: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/ssr')['useRequestHeader']
  const useRequestHeaders: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/ssr')['useRequestHeaders']
  const useRequestURL: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/url')['useRequestURL']
  const useResizeObserver: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useResizeObserver']
  const useResponseHeader: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/ssr')['useResponseHeader']
  const useRoute: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/router')['useRoute']
  const useRouteAnnouncer: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/route-announcer')['useRouteAnnouncer']
  const useRouter: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/router')['useRouter']
  const useRuntimeConfig: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/nuxt')['useRuntimeConfig']
  const useRuntimeHook: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/runtime-hook')['useRuntimeHook']
  const useSSRWidth: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useSSRWidth']
  const useScreenOrientation: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useScreenOrientation']
  const useScreenSafeArea: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useScreenSafeArea']
  const useScript: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScript']
  const useScriptClarity: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptClarity']
  const useScriptCloudflareWebAnalytics: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptCloudflareWebAnalytics']
  const useScriptCrisp: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptCrisp']
  const useScriptEventPage: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptEventPage']
  const useScriptFathomAnalytics: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptFathomAnalytics']
  const useScriptGoogleAdsense: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleAdsense']
  const useScriptGoogleAnalytics: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleAnalytics']
  const useScriptGoogleMaps: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleMaps']
  const useScriptGoogleTagManager: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleTagManager']
  const useScriptHotjar: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptHotjar']
  const useScriptIntercom: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptIntercom']
  const useScriptLemonSqueezy: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptLemonSqueezy']
  const useScriptMatomoAnalytics: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptMatomoAnalytics']
  const useScriptMetaPixel: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptMetaPixel']
  const useScriptNpm: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptNpm']
  const useScriptPlausibleAnalytics: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptPlausibleAnalytics']
  const useScriptSegment: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptSegment']
  const useScriptStripe: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptStripe']
  const useScriptTag: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useScriptTag']
  const useScriptTriggerConsent: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptTriggerConsent']
  const useScriptTriggerElement: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptTriggerElement']
  const useScriptVimeoPlayer: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptVimeoPlayer']
  const useScriptXPixel: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptXPixel']
  const useScriptYouTubePlayer: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptYouTubePlayer']
  const useScroll: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useScroll']
  const useScrollLock: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useScrollLock']
  const useSeoMeta: typeof import('../../../../../../nuxt/node_modules/@unhead/vue')['useSeoMeta']
  const useServerHead: typeof import('../../../../../../nuxt/node_modules/@unhead/vue')['useServerHead']
  const useServerHeadSafe: typeof import('../../../../../../nuxt/node_modules/@unhead/vue')['useServerHeadSafe']
  const useServerSeoMeta: typeof import('../../../../../../nuxt/node_modules/@unhead/vue')['useServerSeoMeta']
  const useSessionStorage: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useSessionStorage']
  const useShadowRoot: typeof import('vue')['useShadowRoot']
  const useShare: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useShare']
  const useSlots: typeof import('vue')['useSlots']
  const useSorted: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useSorted']
  const useSpeechRecognition: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useSpeechRecognition']
  const useSpeechSynthesis: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useSpeechSynthesis']
  const useState: typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/state')['useState']
  const useStepper: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useStepper']
  const useStorageAsync: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useStorageAsync']
  const useStyleTag: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useStyleTag']
  const useSupported: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useSupported']
  const useSwipe: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useSwipe']
  const useTemplateRef: typeof import('vue')['useTemplateRef']
  const useTemplateRefsList: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useTemplateRefsList']
  const useTextDirection: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useTextDirection']
  const useTextSelection: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useTextSelection']
  const useTextareaAutosize: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useTextareaAutosize']
  const useThrottle: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useThrottle']
  const useThrottleFn: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useThrottleFn']
  const useThrottledRefHistory: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useThrottledRefHistory']
  const useTimeAgo: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useTimeAgo']
  const useTimeout: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useTimeout']
  const useTimeoutFn: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useTimeoutFn']
  const useTimeoutPoll: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useTimeoutPoll']
  const useTimestamp: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useTimestamp']
  const useToNumber: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useToNumber']
  const useToString: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useToString']
  const useToggle: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useToggle']
  const useTransition: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useTransition']
  const useTransitionState: typeof import('vue')['useTransitionState']
  const useUrlSearchParams: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useUrlSearchParams']
  const useUserMedia: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useUserMedia']
  const useVModel: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useVModel']
  const useVModels: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useVModels']
  const useVibrate: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useVibrate']
  const useVirtualList: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useVirtualList']
  const useWakeLock: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useWakeLock']
  const useWebNotification: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useWebNotification']
  const useWebSocket: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useWebSocket']
  const useWebWorker: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useWebWorker']
  const useWebWorkerFn: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useWebWorkerFn']
  const useWindowFocus: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useWindowFocus']
  const useWindowScroll: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useWindowScroll']
  const useWindowSize: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useWindowSize']
  const utils: typeof import('../../utils/index')['utils']
  const watch: typeof import('vue')['watch']
  const watchArray: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['watchArray']
  const watchAtMost: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['watchAtMost']
  const watchDebounced: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['watchDebounced']
  const watchDeep: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['watchDeep']
  const watchEffect: typeof import('vue')['watchEffect']
  const watchIgnorable: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['watchIgnorable']
  const watchImmediate: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['watchImmediate']
  const watchOnce: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['watchOnce']
  const watchPausable: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['watchPausable']
  const watchPostEffect: typeof import('vue')['watchPostEffect']
  const watchSyncEffect: typeof import('vue')['watchSyncEffect']
  const watchThrottled: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['watchThrottled']
  const watchTriggerable: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['watchTriggerable']
  const watchWithFilter: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['watchWithFilter']
  const whenever: typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['whenever']
  const withCtx: typeof import('vue')['withCtx']
  const withDirectives: typeof import('vue')['withDirectives']
  const withKeys: typeof import('vue')['withKeys']
  const withMemo: typeof import('vue')['withMemo']
  const withModifiers: typeof import('vue')['withModifiers']
  const withScopeId: typeof import('vue')['withScopeId']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Component, ComponentPublicInstance, ComputedRef, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, MaybeRef, MaybeRefOrGetter, VNode } from 'vue'
  import('vue')
}
// for vue template auto import
import { UnwrapRef } from 'vue'
declare module 'vue' {
  interface ComponentCustomProperties {
    readonly ElIconAddLocation: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['AddLocation']>
    readonly ElIconAim: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Aim']>
    readonly ElIconAlarmClock: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['AlarmClock']>
    readonly ElIconApple: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Apple']>
    readonly ElIconArrowDown: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ArrowDown']>
    readonly ElIconArrowDownBold: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ArrowDownBold']>
    readonly ElIconArrowLeft: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ArrowLeft']>
    readonly ElIconArrowLeftBold: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ArrowLeftBold']>
    readonly ElIconArrowRight: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ArrowRight']>
    readonly ElIconArrowRightBold: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ArrowRightBold']>
    readonly ElIconArrowUp: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ArrowUp']>
    readonly ElIconArrowUpBold: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ArrowUpBold']>
    readonly ElIconAvatar: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Avatar']>
    readonly ElIconBack: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Back']>
    readonly ElIconBaseball: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Baseball']>
    readonly ElIconBasketball: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Basketball']>
    readonly ElIconBell: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Bell']>
    readonly ElIconBellFilled: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['BellFilled']>
    readonly ElIconBicycle: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Bicycle']>
    readonly ElIconBottom: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Bottom']>
    readonly ElIconBottomLeft: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['BottomLeft']>
    readonly ElIconBottomRight: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['BottomRight']>
    readonly ElIconBowl: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Bowl']>
    readonly ElIconBox: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Box']>
    readonly ElIconBriefcase: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Briefcase']>
    readonly ElIconBrush: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Brush']>
    readonly ElIconBrushFilled: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['BrushFilled']>
    readonly ElIconBurger: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Burger']>
    readonly ElIconCalendar: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Calendar']>
    readonly ElIconCamera: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Camera']>
    readonly ElIconCameraFilled: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['CameraFilled']>
    readonly ElIconCaretBottom: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['CaretBottom']>
    readonly ElIconCaretLeft: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['CaretLeft']>
    readonly ElIconCaretRight: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['CaretRight']>
    readonly ElIconCaretTop: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['CaretTop']>
    readonly ElIconCellphone: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Cellphone']>
    readonly ElIconChatDotRound: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ChatDotRound']>
    readonly ElIconChatDotSquare: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ChatDotSquare']>
    readonly ElIconChatLineRound: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ChatLineRound']>
    readonly ElIconChatLineSquare: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ChatLineSquare']>
    readonly ElIconChatRound: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ChatRound']>
    readonly ElIconChatSquare: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ChatSquare']>
    readonly ElIconCheck: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Check']>
    readonly ElIconChecked: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Checked']>
    readonly ElIconCherry: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Cherry']>
    readonly ElIconChicken: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Chicken']>
    readonly ElIconChromeFilled: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ChromeFilled']>
    readonly ElIconCircleCheck: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['CircleCheck']>
    readonly ElIconCircleCheckFilled: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['CircleCheckFilled']>
    readonly ElIconCircleClose: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['CircleClose']>
    readonly ElIconCircleCloseFilled: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['CircleCloseFilled']>
    readonly ElIconCirclePlus: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['CirclePlus']>
    readonly ElIconCirclePlusFilled: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['CirclePlusFilled']>
    readonly ElIconClock: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Clock']>
    readonly ElIconClose: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Close']>
    readonly ElIconCloseBold: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['CloseBold']>
    readonly ElIconCloudy: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Cloudy']>
    readonly ElIconCoffee: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Coffee']>
    readonly ElIconCoffeeCup: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['CoffeeCup']>
    readonly ElIconCoin: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Coin']>
    readonly ElIconColdDrink: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ColdDrink']>
    readonly ElIconCollection: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Collection']>
    readonly ElIconCollectionTag: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['CollectionTag']>
    readonly ElIconComment: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Comment']>
    readonly ElIconCompass: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Compass']>
    readonly ElIconConnection: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Connection']>
    readonly ElIconCoordinate: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Coordinate']>
    readonly ElIconCopyDocument: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['CopyDocument']>
    readonly ElIconCpu: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Cpu']>
    readonly ElIconCreditCard: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['CreditCard']>
    readonly ElIconCrop: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Crop']>
    readonly ElIconDArrowLeft: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['DArrowLeft']>
    readonly ElIconDArrowRight: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['DArrowRight']>
    readonly ElIconDCaret: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['DCaret']>
    readonly ElIconDataAnalysis: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['DataAnalysis']>
    readonly ElIconDataBoard: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['DataBoard']>
    readonly ElIconDataLine: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['DataLine']>
    readonly ElIconDelete: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Delete']>
    readonly ElIconDeleteFilled: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['DeleteFilled']>
    readonly ElIconDeleteLocation: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['DeleteLocation']>
    readonly ElIconDessert: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Dessert']>
    readonly ElIconDiscount: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Discount']>
    readonly ElIconDish: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Dish']>
    readonly ElIconDishDot: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['DishDot']>
    readonly ElIconDocument: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Document']>
    readonly ElIconDocumentAdd: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['DocumentAdd']>
    readonly ElIconDocumentChecked: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['DocumentChecked']>
    readonly ElIconDocumentCopy: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['DocumentCopy']>
    readonly ElIconDocumentDelete: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['DocumentDelete']>
    readonly ElIconDocumentRemove: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['DocumentRemove']>
    readonly ElIconDownload: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Download']>
    readonly ElIconDrizzling: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Drizzling']>
    readonly ElIconEdit: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Edit']>
    readonly ElIconEditPen: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['EditPen']>
    readonly ElIconEleme: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Eleme']>
    readonly ElIconElemeFilled: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ElemeFilled']>
    readonly ElIconElementPlus: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ElementPlus']>
    readonly ElIconExpand: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Expand']>
    readonly ElIconFailed: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Failed']>
    readonly ElIconFemale: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Female']>
    readonly ElIconFiles: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Files']>
    readonly ElIconFilm: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Film']>
    readonly ElIconFilter: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Filter']>
    readonly ElIconFinished: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Finished']>
    readonly ElIconFirstAidKit: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['FirstAidKit']>
    readonly ElIconFlag: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Flag']>
    readonly ElIconFold: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Fold']>
    readonly ElIconFolder: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Folder']>
    readonly ElIconFolderAdd: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['FolderAdd']>
    readonly ElIconFolderChecked: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['FolderChecked']>
    readonly ElIconFolderDelete: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['FolderDelete']>
    readonly ElIconFolderOpened: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['FolderOpened']>
    readonly ElIconFolderRemove: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['FolderRemove']>
    readonly ElIconFood: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Food']>
    readonly ElIconFootball: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Football']>
    readonly ElIconForkSpoon: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ForkSpoon']>
    readonly ElIconFries: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Fries']>
    readonly ElIconFullScreen: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['FullScreen']>
    readonly ElIconGoblet: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Goblet']>
    readonly ElIconGobletFull: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['GobletFull']>
    readonly ElIconGobletSquare: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['GobletSquare']>
    readonly ElIconGobletSquareFull: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['GobletSquareFull']>
    readonly ElIconGoldMedal: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['GoldMedal']>
    readonly ElIconGoods: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Goods']>
    readonly ElIconGoodsFilled: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['GoodsFilled']>
    readonly ElIconGrape: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Grape']>
    readonly ElIconGrid: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Grid']>
    readonly ElIconGuide: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Guide']>
    readonly ElIconHandbag: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Handbag']>
    readonly ElIconHeadset: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Headset']>
    readonly ElIconHelp: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Help']>
    readonly ElIconHelpFilled: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['HelpFilled']>
    readonly ElIconHide: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Hide']>
    readonly ElIconHistogram: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Histogram']>
    readonly ElIconHomeFilled: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['HomeFilled']>
    readonly ElIconHotWater: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['HotWater']>
    readonly ElIconHouse: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['House']>
    readonly ElIconIceCream: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['IceCream']>
    readonly ElIconIceCreamRound: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['IceCreamRound']>
    readonly ElIconIceCreamSquare: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['IceCreamSquare']>
    readonly ElIconIceDrink: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['IceDrink']>
    readonly ElIconIceTea: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['IceTea']>
    readonly ElIconInfoFilled: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['InfoFilled']>
    readonly ElIconIphone: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Iphone']>
    readonly ElIconKey: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Key']>
    readonly ElIconKnifeFork: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['KnifeFork']>
    readonly ElIconLightning: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Lightning']>
    readonly ElIconLink: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Link']>
    readonly ElIconList: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['List']>
    readonly ElIconLoading: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Loading']>
    readonly ElIconLocation: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Location']>
    readonly ElIconLocationFilled: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['LocationFilled']>
    readonly ElIconLocationInformation: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['LocationInformation']>
    readonly ElIconLock: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Lock']>
    readonly ElIconLollipop: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Lollipop']>
    readonly ElIconMagicStick: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['MagicStick']>
    readonly ElIconMagnet: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Magnet']>
    readonly ElIconMale: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Male']>
    readonly ElIconManagement: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Management']>
    readonly ElIconMapLocation: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['MapLocation']>
    readonly ElIconMedal: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Medal']>
    readonly ElIconMemo: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Memo']>
    readonly ElIconMenu: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Menu']>
    readonly ElIconMessage: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Message']>
    readonly ElIconMessageBox: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['MessageBox']>
    readonly ElIconMic: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Mic']>
    readonly ElIconMicrophone: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Microphone']>
    readonly ElIconMilkTea: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['MilkTea']>
    readonly ElIconMinus: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Minus']>
    readonly ElIconMoney: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Money']>
    readonly ElIconMonitor: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Monitor']>
    readonly ElIconMoon: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Moon']>
    readonly ElIconMoonNight: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['MoonNight']>
    readonly ElIconMore: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['More']>
    readonly ElIconMoreFilled: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['MoreFilled']>
    readonly ElIconMostlyCloudy: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['MostlyCloudy']>
    readonly ElIconMouse: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Mouse']>
    readonly ElIconMug: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Mug']>
    readonly ElIconMute: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Mute']>
    readonly ElIconMuteNotification: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['MuteNotification']>
    readonly ElIconNoSmoking: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['NoSmoking']>
    readonly ElIconNotebook: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Notebook']>
    readonly ElIconNotification: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Notification']>
    readonly ElIconOdometer: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Odometer']>
    readonly ElIconOfficeBuilding: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['OfficeBuilding']>
    readonly ElIconOpen: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Open']>
    readonly ElIconOperation: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Operation']>
    readonly ElIconOpportunity: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Opportunity']>
    readonly ElIconOrange: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Orange']>
    readonly ElIconPaperclip: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Paperclip']>
    readonly ElIconPartlyCloudy: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['PartlyCloudy']>
    readonly ElIconPear: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Pear']>
    readonly ElIconPhone: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Phone']>
    readonly ElIconPhoneFilled: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['PhoneFilled']>
    readonly ElIconPicture: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Picture']>
    readonly ElIconPictureFilled: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['PictureFilled']>
    readonly ElIconPictureRounded: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['PictureRounded']>
    readonly ElIconPieChart: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['PieChart']>
    readonly ElIconPlace: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Place']>
    readonly ElIconPlatform: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Platform']>
    readonly ElIconPlus: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Plus']>
    readonly ElIconPointer: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Pointer']>
    readonly ElIconPosition: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Position']>
    readonly ElIconPostcard: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Postcard']>
    readonly ElIconPouring: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Pouring']>
    readonly ElIconPresent: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Present']>
    readonly ElIconPriceTag: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['PriceTag']>
    readonly ElIconPrinter: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Printer']>
    readonly ElIconPromotion: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Promotion']>
    readonly ElIconQuartzWatch: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['QuartzWatch']>
    readonly ElIconQuestionFilled: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['QuestionFilled']>
    readonly ElIconRank: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Rank']>
    readonly ElIconReading: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Reading']>
    readonly ElIconReadingLamp: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ReadingLamp']>
    readonly ElIconRefresh: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Refresh']>
    readonly ElIconRefreshLeft: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['RefreshLeft']>
    readonly ElIconRefreshRight: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['RefreshRight']>
    readonly ElIconRefrigerator: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Refrigerator']>
    readonly ElIconRemove: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Remove']>
    readonly ElIconRemoveFilled: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['RemoveFilled']>
    readonly ElIconRight: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Right']>
    readonly ElIconScaleToOriginal: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ScaleToOriginal']>
    readonly ElIconSchool: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['School']>
    readonly ElIconScissor: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Scissor']>
    readonly ElIconSearch: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Search']>
    readonly ElIconSelect: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Select']>
    readonly ElIconSell: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Sell']>
    readonly ElIconSemiSelect: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['SemiSelect']>
    readonly ElIconService: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Service']>
    readonly ElIconSetUp: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['SetUp']>
    readonly ElIconSetting: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Setting']>
    readonly ElIconShare: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Share']>
    readonly ElIconShip: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Ship']>
    readonly ElIconShop: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Shop']>
    readonly ElIconShoppingBag: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ShoppingBag']>
    readonly ElIconShoppingCart: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ShoppingCart']>
    readonly ElIconShoppingCartFull: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ShoppingCartFull']>
    readonly ElIconShoppingTrolley: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ShoppingTrolley']>
    readonly ElIconSmoking: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Smoking']>
    readonly ElIconSoccer: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Soccer']>
    readonly ElIconSoldOut: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['SoldOut']>
    readonly ElIconSort: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Sort']>
    readonly ElIconSortDown: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['SortDown']>
    readonly ElIconSortUp: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['SortUp']>
    readonly ElIconStamp: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Stamp']>
    readonly ElIconStar: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Star']>
    readonly ElIconStarFilled: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['StarFilled']>
    readonly ElIconStopwatch: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Stopwatch']>
    readonly ElIconSuccessFilled: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['SuccessFilled']>
    readonly ElIconSugar: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Sugar']>
    readonly ElIconSuitcase: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Suitcase']>
    readonly ElIconSuitcaseLine: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['SuitcaseLine']>
    readonly ElIconSunny: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Sunny']>
    readonly ElIconSunrise: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Sunrise']>
    readonly ElIconSunset: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Sunset']>
    readonly ElIconSwitch: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Switch']>
    readonly ElIconSwitchButton: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['SwitchButton']>
    readonly ElIconSwitchFilled: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['SwitchFilled']>
    readonly ElIconTakeawayBox: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['TakeawayBox']>
    readonly ElIconTicket: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Ticket']>
    readonly ElIconTickets: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Tickets']>
    readonly ElIconTimer: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Timer']>
    readonly ElIconToiletPaper: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ToiletPaper']>
    readonly ElIconTools: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Tools']>
    readonly ElIconTop: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Top']>
    readonly ElIconTopLeft: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['TopLeft']>
    readonly ElIconTopRight: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['TopRight']>
    readonly ElIconTrendCharts: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['TrendCharts']>
    readonly ElIconTrophy: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Trophy']>
    readonly ElIconTrophyBase: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['TrophyBase']>
    readonly ElIconTurnOff: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['TurnOff']>
    readonly ElIconUmbrella: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Umbrella']>
    readonly ElIconUnlock: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Unlock']>
    readonly ElIconUpload: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Upload']>
    readonly ElIconUploadFilled: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['UploadFilled']>
    readonly ElIconUser: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['User']>
    readonly ElIconUserFilled: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['UserFilled']>
    readonly ElIconVan: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Van']>
    readonly ElIconVideoCamera: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['VideoCamera']>
    readonly ElIconVideoCameraFilled: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['VideoCameraFilled']>
    readonly ElIconVideoPause: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['VideoPause']>
    readonly ElIconVideoPlay: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['VideoPlay']>
    readonly ElIconView: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['View']>
    readonly ElIconWallet: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Wallet']>
    readonly ElIconWalletFilled: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['WalletFilled']>
    readonly ElIconWarnTriangleFilled: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['WarnTriangleFilled']>
    readonly ElIconWarning: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Warning']>
    readonly ElIconWarningFilled: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['WarningFilled']>
    readonly ElIconWatch: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Watch']>
    readonly ElIconWatermelon: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['Watermelon']>
    readonly ElIconWindPower: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['WindPower']>
    readonly ElIconZoomIn: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ZoomIn']>
    readonly ElIconZoomOut: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@element-plus/icons-vue/dist/index')['ZoomOut']>
    readonly ElLoading: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/element-plus/es/components/loading/index')['ElLoading']>
    readonly ElMessage: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/element-plus/es/components/message/index')['ElMessage']>
    readonly ElMessageBox: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/element-plus/es/components/message-box/index')['ElMessageBox']>
    readonly ElNotification: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/element-plus/es/components/notification/index')['ElNotification']>
    readonly ID_INJECTION_KEY: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/element-plus/es/hooks/use-id/index')['ID_INJECTION_KEY']>
    readonly ZINDEX_INJECTION_KEY: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/element-plus/es/hooks/use-z-index/index')['ZINDEX_INJECTION_KEY']>
    readonly abortNavigation: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/router')['abortNavigation']>
    readonly acceptHMRUpdate: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@pinia/nuxt/dist/runtime/composables')['acceptHMRUpdate']>
    readonly addRouteMiddleware: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/router')['addRouteMiddleware']>
    readonly asyncComputed: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['asyncComputed']>
    readonly autoResetRef: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['autoResetRef']>
    readonly callOnce: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/once')['callOnce']>
    readonly cancelIdleCallback: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/compat/idle-callback')['cancelIdleCallback']>
    readonly clearError: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/error')['clearError']>
    readonly clearNuxtData: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/asyncData')['clearNuxtData']>
    readonly clearNuxtState: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/state')['clearNuxtState']>
    readonly computed: UnwrapRef<typeof import('vue')['computed']>
    readonly computedAsync: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['computedAsync']>
    readonly computedEager: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['computedEager']>
    readonly computedInject: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['computedInject']>
    readonly computedWithControl: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['computedWithControl']>
    readonly controlledComputed: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['controlledComputed']>
    readonly controlledRef: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['controlledRef']>
    readonly createError: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/error')['createError']>
    readonly createEventHook: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['createEventHook']>
    readonly createGlobalState: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['createGlobalState']>
    readonly createHttp: UnwrapRef<typeof import('../../utils/http')['createHttp']>
    readonly createInjectionState: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['createInjectionState']>
    readonly createReactiveFn: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['createReactiveFn']>
    readonly createReusableTemplate: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['createReusableTemplate']>
    readonly createSharedComposable: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['createSharedComposable']>
    readonly createTemplatePromise: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['createTemplatePromise']>
    readonly createUnrefFn: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['createUnrefFn']>
    readonly crypto: UnwrapRef<typeof import('../../utils/crypto')['crypto']>
    readonly customRef: UnwrapRef<typeof import('vue')['customRef']>
    readonly debouncedRef: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['debouncedRef']>
    readonly debouncedWatch: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['debouncedWatch']>
    readonly defineAppConfig: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/nuxt')['defineAppConfig']>
    readonly defineAsyncComponent: UnwrapRef<typeof import('vue')['defineAsyncComponent']>
    readonly defineComponent: UnwrapRef<typeof import('vue')['defineComponent']>
    readonly defineNuxtComponent: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/component')['defineNuxtComponent']>
    readonly defineNuxtLink: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/components/nuxt-link')['defineNuxtLink']>
    readonly defineNuxtPlugin: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/nuxt')['defineNuxtPlugin']>
    readonly defineNuxtRouteMiddleware: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/router')['defineNuxtRouteMiddleware']>
    readonly definePageMeta: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/pages/runtime/composables')['definePageMeta']>
    readonly definePayloadPlugin: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/nuxt')['definePayloadPlugin']>
    readonly definePayloadReducer: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/payload')['definePayloadReducer']>
    readonly definePayloadReviver: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/payload')['definePayloadReviver']>
    readonly defineStore: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@pinia/nuxt/dist/runtime/composables')['defineStore']>
    readonly eagerComputed: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['eagerComputed']>
    readonly effect: UnwrapRef<typeof import('vue')['effect']>
    readonly effectScope: UnwrapRef<typeof import('vue')['effectScope']>
    readonly emitter: UnwrapRef<typeof import('../../utils/emitter')['default']>
    readonly extendRef: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['extendRef']>
    readonly getAppManifest: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/manifest')['getAppManifest']>
    readonly getCurrentInstance: UnwrapRef<typeof import('vue')['getCurrentInstance']>
    readonly getCurrentScope: UnwrapRef<typeof import('vue')['getCurrentScope']>
    readonly getRouteRules: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/manifest')['getRouteRules']>
    readonly h: UnwrapRef<typeof import('vue')['h']>
    readonly hasInjectionContext: UnwrapRef<typeof import('vue')['hasInjectionContext']>
    readonly ignorableWatch: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['ignorableWatch']>
    readonly inject: UnwrapRef<typeof import('vue')['inject']>
    readonly injectHead: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@unhead/vue')['injectHead']>
    readonly injectLocal: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['injectLocal']>
    readonly isDefined: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['isDefined']>
    readonly isNuxtError: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/error')['isNuxtError']>
    readonly isPrerendered: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/payload')['isPrerendered']>
    readonly isProxy: UnwrapRef<typeof import('vue')['isProxy']>
    readonly isReactive: UnwrapRef<typeof import('vue')['isReactive']>
    readonly isReadonly: UnwrapRef<typeof import('vue')['isReadonly']>
    readonly isRef: UnwrapRef<typeof import('vue')['isRef']>
    readonly isShallow: UnwrapRef<typeof import('vue')['isShallow']>
    readonly isVue2: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/compat/vue-demi')['isVue2']>
    readonly isVue3: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/compat/vue-demi')['isVue3']>
    readonly jquery: UnwrapRef<typeof import('../../utils/jquery')['default']>
    readonly loadPayload: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/payload')['loadPayload']>
    readonly makeDestructurable: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['makeDestructurable']>
    readonly markRaw: UnwrapRef<typeof import('vue')['markRaw']>
    readonly mergeModels: UnwrapRef<typeof import('vue')['mergeModels']>
    readonly navigateTo: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/router')['navigateTo']>
    readonly nextTick: UnwrapRef<typeof import('vue')['nextTick']>
    readonly onActivated: UnwrapRef<typeof import('vue')['onActivated']>
    readonly onBeforeMount: UnwrapRef<typeof import('vue')['onBeforeMount']>
    readonly onBeforeRouteLeave: UnwrapRef<typeof import('vue-router')['onBeforeRouteLeave']>
    readonly onBeforeRouteUpdate: UnwrapRef<typeof import('vue-router')['onBeforeRouteUpdate']>
    readonly onBeforeUnmount: UnwrapRef<typeof import('vue')['onBeforeUnmount']>
    readonly onBeforeUpdate: UnwrapRef<typeof import('vue')['onBeforeUpdate']>
    readonly onClickOutside: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['onClickOutside']>
    readonly onDeactivated: UnwrapRef<typeof import('vue')['onDeactivated']>
    readonly onErrorCaptured: UnwrapRef<typeof import('vue')['onErrorCaptured']>
    readonly onKeyStroke: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['onKeyStroke']>
    readonly onLongPress: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['onLongPress']>
    readonly onMounted: UnwrapRef<typeof import('vue')['onMounted']>
    readonly onNuxtReady: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/ready')['onNuxtReady']>
    readonly onPrehydrate: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/ssr')['onPrehydrate']>
    readonly onRenderTracked: UnwrapRef<typeof import('vue')['onRenderTracked']>
    readonly onRenderTriggered: UnwrapRef<typeof import('vue')['onRenderTriggered']>
    readonly onScopeDispose: UnwrapRef<typeof import('vue')['onScopeDispose']>
    readonly onServerPrefetch: UnwrapRef<typeof import('vue')['onServerPrefetch']>
    readonly onStartTyping: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['onStartTyping']>
    readonly onUnmounted: UnwrapRef<typeof import('vue')['onUnmounted']>
    readonly onUpdated: UnwrapRef<typeof import('vue')['onUpdated']>
    readonly pausableWatch: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['pausableWatch']>
    readonly prefetchComponents: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/preload')['prefetchComponents']>
    readonly preloadComponents: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/preload')['preloadComponents']>
    readonly preloadPayload: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/payload')['preloadPayload']>
    readonly preloadRouteComponents: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/preload')['preloadRouteComponents']>
    readonly prerenderRoutes: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/ssr')['prerenderRoutes']>
    readonly provide: UnwrapRef<typeof import('vue')['provide']>
    readonly provideGlobalConfig: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/element-plus/es/components/config-provider/src/hooks/use-global-config')['provideGlobalConfig']>
    readonly provideLocal: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['provideLocal']>
    readonly proxyRefs: UnwrapRef<typeof import('vue')['proxyRefs']>
    readonly reactify: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['reactify']>
    readonly reactifyObject: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['reactifyObject']>
    readonly reactive: UnwrapRef<typeof import('vue')['reactive']>
    readonly reactiveComputed: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['reactiveComputed']>
    readonly reactiveOmit: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['reactiveOmit']>
    readonly reactivePick: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['reactivePick']>
    readonly readonly: UnwrapRef<typeof import('vue')['readonly']>
    readonly ref: UnwrapRef<typeof import('vue')['ref']>
    readonly refAutoReset: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['refAutoReset']>
    readonly refDebounced: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['refDebounced']>
    readonly refDefault: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['refDefault']>
    readonly refThrottled: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['refThrottled']>
    readonly refWithControl: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['refWithControl']>
    readonly refreshCookie: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/cookie')['refreshCookie']>
    readonly refreshNuxtData: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/asyncData')['refreshNuxtData']>
    readonly reloadNuxtApp: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/chunk')['reloadNuxtApp']>
    readonly rendervnode: UnwrapRef<typeof import('../../utils/rendervnode')['default']>
    readonly requestIdleCallback: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/compat/idle-callback')['requestIdleCallback']>
    readonly resolveComponent: UnwrapRef<typeof import('vue')['resolveComponent']>
    readonly resolveRef: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['resolveRef']>
    readonly resolveUnref: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['resolveUnref']>
    readonly setInterval: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/compat/interval')['setInterval']>
    readonly setPageLayout: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/router')['setPageLayout']>
    readonly setResponseStatus: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/ssr')['setResponseStatus']>
    readonly shallowReactive: UnwrapRef<typeof import('vue')['shallowReactive']>
    readonly shallowReadonly: UnwrapRef<typeof import('vue')['shallowReadonly']>
    readonly shallowRef: UnwrapRef<typeof import('vue')['shallowRef']>
    readonly showError: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/error')['showError']>
    readonly storeToRefs: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@pinia/nuxt/dist/runtime/composables')['storeToRefs']>
    readonly syncRef: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['syncRef']>
    readonly syncRefs: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['syncRefs']>
    readonly templateRef: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['templateRef']>
    readonly throttledRef: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['throttledRef']>
    readonly throttledWatch: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['throttledWatch']>
    readonly toRaw: UnwrapRef<typeof import('vue')['toRaw']>
    readonly toReactive: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['toReactive']>
    readonly toRef: UnwrapRef<typeof import('vue')['toRef']>
    readonly toRefs: UnwrapRef<typeof import('vue')['toRefs']>
    readonly toValue: UnwrapRef<typeof import('vue')['toValue']>
    readonly triggerRef: UnwrapRef<typeof import('vue')['triggerRef']>
    readonly tryOnBeforeMount: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['tryOnBeforeMount']>
    readonly tryOnBeforeUnmount: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['tryOnBeforeUnmount']>
    readonly tryOnMounted: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['tryOnMounted']>
    readonly tryOnScopeDispose: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['tryOnScopeDispose']>
    readonly tryOnUnmounted: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['tryOnUnmounted']>
    readonly tryUseNuxtApp: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/nuxt')['tryUseNuxtApp']>
    readonly unref: UnwrapRef<typeof import('vue')['unref']>
    readonly unrefElement: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['unrefElement']>
    readonly until: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['until']>
    readonly updateAppConfig: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/config')['updateAppConfig']>
    readonly useActiveElement: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useActiveElement']>
    readonly useAnimate: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useAnimate']>
    readonly useAppConfig: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/config')['useAppConfig']>
    readonly useArrayDifference: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useArrayDifference']>
    readonly useArrayEvery: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useArrayEvery']>
    readonly useArrayFilter: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useArrayFilter']>
    readonly useArrayFind: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useArrayFind']>
    readonly useArrayFindIndex: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useArrayFindIndex']>
    readonly useArrayFindLast: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useArrayFindLast']>
    readonly useArrayIncludes: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useArrayIncludes']>
    readonly useArrayJoin: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useArrayJoin']>
    readonly useArrayMap: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useArrayMap']>
    readonly useArrayReduce: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useArrayReduce']>
    readonly useArraySome: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useArraySome']>
    readonly useArrayUnique: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useArrayUnique']>
    readonly useAsyncData: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/asyncData')['useAsyncData']>
    readonly useAsyncQueue: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useAsyncQueue']>
    readonly useAsyncState: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useAsyncState']>
    readonly useAttrs: UnwrapRef<typeof import('vue')['useAttrs']>
    readonly useBase64: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useBase64']>
    readonly useBattery: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useBattery']>
    readonly useBluetooth: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useBluetooth']>
    readonly useBreakpoints: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useBreakpoints']>
    readonly useBroadcastChannel: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useBroadcastChannel']>
    readonly useBrowserLocation: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useBrowserLocation']>
    readonly useCached: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useCached']>
    readonly useClipboard: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useClipboard']>
    readonly useClipboardItems: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useClipboardItems']>
    readonly useCloned: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useCloned']>
    readonly useColorMode: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useColorMode']>
    readonly useConfirmDialog: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useConfirmDialog']>
    readonly useCookie: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/cookie')['useCookie']>
    readonly useCounter: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useCounter']>
    readonly useCssModule: UnwrapRef<typeof import('vue')['useCssModule']>
    readonly useCssVar: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useCssVar']>
    readonly useCssVars: UnwrapRef<typeof import('vue')['useCssVars']>
    readonly useCurrentElement: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useCurrentElement']>
    readonly useCycleList: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useCycleList']>
    readonly useDark: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useDark']>
    readonly useDataStore: UnwrapRef<typeof import('../../stores/index')['useDataStore']>
    readonly useDateFormat: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useDateFormat']>
    readonly useDebounce: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useDebounce']>
    readonly useDebounceFn: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useDebounceFn']>
    readonly useDebouncedRefHistory: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useDebouncedRefHistory']>
    readonly useDeviceMotion: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useDeviceMotion']>
    readonly useDeviceOrientation: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useDeviceOrientation']>
    readonly useDevicePixelRatio: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useDevicePixelRatio']>
    readonly useDevicesList: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useDevicesList']>
    readonly useDisplayMedia: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useDisplayMedia']>
    readonly useDocumentVisibility: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useDocumentVisibility']>
    readonly useDraggable: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useDraggable']>
    readonly useDropZone: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useDropZone']>
    readonly useElementBounding: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useElementBounding']>
    readonly useElementByPoint: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useElementByPoint']>
    readonly useElementHover: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useElementHover']>
    readonly useElementSize: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useElementSize']>
    readonly useElementVisibility: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useElementVisibility']>
    readonly useError: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/error')['useError']>
    readonly useEventBus: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useEventBus']>
    readonly useEventListener: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useEventListener']>
    readonly useEventSource: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useEventSource']>
    readonly useEyeDropper: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useEyeDropper']>
    readonly useFavicon: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useFavicon']>
    readonly useFetch: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/fetch')['useFetch']>
    readonly useFileDialog: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useFileDialog']>
    readonly useFileSystemAccess: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useFileSystemAccess']>
    readonly useFocus: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useFocus']>
    readonly useFocusWithin: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useFocusWithin']>
    readonly useFps: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useFps']>
    readonly useFullscreen: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useFullscreen']>
    readonly useGamepad: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useGamepad']>
    readonly useGeolocation: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useGeolocation']>
    readonly useHead: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@unhead/vue')['useHead']>
    readonly useHeadSafe: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@unhead/vue')['useHeadSafe']>
    readonly useHydration: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/hydrate')['useHydration']>
    readonly useId: UnwrapRef<typeof import('vue')['useId']>
    readonly useIdle: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useIdle']>
    readonly useImage: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@nuxt/image/dist/runtime/composables')['useImage']>
    readonly useInfiniteScroll: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useInfiniteScroll']>
    readonly useIntersectionObserver: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useIntersectionObserver']>
    readonly useInterval: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useInterval']>
    readonly useIntervalFn: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useIntervalFn']>
    readonly useKeyModifier: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useKeyModifier']>
    readonly useLastChanged: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useLastChanged']>
    readonly useLazyAsyncData: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/asyncData')['useLazyAsyncData']>
    readonly useLazyFetch: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/fetch')['useLazyFetch']>
    readonly useLink: UnwrapRef<typeof import('vue-router')['useLink']>
    readonly useLoadingIndicator: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/loading-indicator')['useLoadingIndicator']>
    readonly useLocalStorage: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useLocalStorage']>
    readonly useMagicKeys: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useMagicKeys']>
    readonly useManualRefHistory: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useManualRefHistory']>
    readonly useMediaControls: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useMediaControls']>
    readonly useMediaQuery: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useMediaQuery']>
    readonly useMemoize: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useMemoize']>
    readonly useMemory: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useMemory']>
    readonly useModel: UnwrapRef<typeof import('vue')['useModel']>
    readonly useMounted: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useMounted']>
    readonly useMouse: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useMouse']>
    readonly useMouseInElement: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useMouseInElement']>
    readonly useMousePressed: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useMousePressed']>
    readonly useMutationObserver: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useMutationObserver']>
    readonly useNavigatorLanguage: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useNavigatorLanguage']>
    readonly useNetwork: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useNetwork']>
    readonly useNow: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useNow']>
    readonly useNuxtApp: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/nuxt')['useNuxtApp']>
    readonly useNuxtData: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/asyncData')['useNuxtData']>
    readonly useObjectUrl: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useObjectUrl']>
    readonly useOffsetPagination: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useOffsetPagination']>
    readonly useOnline: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useOnline']>
    readonly usePageLeave: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['usePageLeave']>
    readonly useParallax: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useParallax']>
    readonly useParentElement: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useParentElement']>
    readonly usePerformanceObserver: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['usePerformanceObserver']>
    readonly usePermission: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['usePermission']>
    readonly usePinia: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@pinia/nuxt/dist/runtime/composables')['usePinia']>
    readonly usePointer: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['usePointer']>
    readonly usePointerLock: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['usePointerLock']>
    readonly usePointerSwipe: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['usePointerSwipe']>
    readonly usePreferredColorScheme: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['usePreferredColorScheme']>
    readonly usePreferredContrast: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['usePreferredContrast']>
    readonly usePreferredDark: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['usePreferredDark']>
    readonly usePreferredLanguages: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['usePreferredLanguages']>
    readonly usePreferredReducedMotion: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['usePreferredReducedMotion']>
    readonly usePreferredReducedTransparency: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['usePreferredReducedTransparency']>
    readonly usePreviewMode: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/preview')['usePreviewMode']>
    readonly usePrevious: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['usePrevious']>
    readonly useRafFn: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useRafFn']>
    readonly useRecaptcha: UnwrapRef<typeof import('../../composables/useRecaptcha')['useRecaptcha']>
    readonly useRefHistory: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useRefHistory']>
    readonly useRequestEvent: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/ssr')['useRequestEvent']>
    readonly useRequestFetch: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/ssr')['useRequestFetch']>
    readonly useRequestHeader: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/ssr')['useRequestHeader']>
    readonly useRequestHeaders: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/ssr')['useRequestHeaders']>
    readonly useRequestURL: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/url')['useRequestURL']>
    readonly useResizeObserver: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useResizeObserver']>
    readonly useResponseHeader: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/ssr')['useResponseHeader']>
    readonly useRoute: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/router')['useRoute']>
    readonly useRouteAnnouncer: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/route-announcer')['useRouteAnnouncer']>
    readonly useRouter: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/router')['useRouter']>
    readonly useRuntimeConfig: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/nuxt')['useRuntimeConfig']>
    readonly useRuntimeHook: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/runtime-hook')['useRuntimeHook']>
    readonly useSSRWidth: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useSSRWidth']>
    readonly useScreenOrientation: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useScreenOrientation']>
    readonly useScreenSafeArea: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useScreenSafeArea']>
    readonly useScript: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScript']>
    readonly useScriptClarity: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptClarity']>
    readonly useScriptCloudflareWebAnalytics: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptCloudflareWebAnalytics']>
    readonly useScriptCrisp: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptCrisp']>
    readonly useScriptEventPage: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptEventPage']>
    readonly useScriptFathomAnalytics: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptFathomAnalytics']>
    readonly useScriptGoogleAdsense: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleAdsense']>
    readonly useScriptGoogleAnalytics: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleAnalytics']>
    readonly useScriptGoogleMaps: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleMaps']>
    readonly useScriptGoogleTagManager: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleTagManager']>
    readonly useScriptHotjar: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptHotjar']>
    readonly useScriptIntercom: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptIntercom']>
    readonly useScriptLemonSqueezy: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptLemonSqueezy']>
    readonly useScriptMatomoAnalytics: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptMatomoAnalytics']>
    readonly useScriptMetaPixel: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptMetaPixel']>
    readonly useScriptNpm: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptNpm']>
    readonly useScriptPlausibleAnalytics: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptPlausibleAnalytics']>
    readonly useScriptSegment: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptSegment']>
    readonly useScriptStripe: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptStripe']>
    readonly useScriptTag: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useScriptTag']>
    readonly useScriptTriggerConsent: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptTriggerConsent']>
    readonly useScriptTriggerElement: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptTriggerElement']>
    readonly useScriptVimeoPlayer: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptVimeoPlayer']>
    readonly useScriptXPixel: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptXPixel']>
    readonly useScriptYouTubePlayer: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/script-stubs')['useScriptYouTubePlayer']>
    readonly useScroll: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useScroll']>
    readonly useScrollLock: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useScrollLock']>
    readonly useSeoMeta: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@unhead/vue')['useSeoMeta']>
    readonly useServerHead: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@unhead/vue')['useServerHead']>
    readonly useServerHeadSafe: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@unhead/vue')['useServerHeadSafe']>
    readonly useServerSeoMeta: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@unhead/vue')['useServerSeoMeta']>
    readonly useSessionStorage: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useSessionStorage']>
    readonly useShadowRoot: UnwrapRef<typeof import('vue')['useShadowRoot']>
    readonly useShare: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useShare']>
    readonly useSlots: UnwrapRef<typeof import('vue')['useSlots']>
    readonly useSorted: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useSorted']>
    readonly useSpeechRecognition: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useSpeechRecognition']>
    readonly useSpeechSynthesis: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useSpeechSynthesis']>
    readonly useState: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/nuxt/dist/app/composables/state')['useState']>
    readonly useStepper: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useStepper']>
    readonly useStorageAsync: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useStorageAsync']>
    readonly useStyleTag: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useStyleTag']>
    readonly useSupported: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useSupported']>
    readonly useSwipe: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useSwipe']>
    readonly useTemplateRef: UnwrapRef<typeof import('vue')['useTemplateRef']>
    readonly useTemplateRefsList: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useTemplateRefsList']>
    readonly useTextDirection: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useTextDirection']>
    readonly useTextSelection: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useTextSelection']>
    readonly useTextareaAutosize: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useTextareaAutosize']>
    readonly useThrottle: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useThrottle']>
    readonly useThrottleFn: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useThrottleFn']>
    readonly useThrottledRefHistory: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useThrottledRefHistory']>
    readonly useTimeAgo: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useTimeAgo']>
    readonly useTimeout: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useTimeout']>
    readonly useTimeoutFn: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useTimeoutFn']>
    readonly useTimeoutPoll: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useTimeoutPoll']>
    readonly useTimestamp: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useTimestamp']>
    readonly useToNumber: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useToNumber']>
    readonly useToString: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useToString']>
    readonly useToggle: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useToggle']>
    readonly useTransition: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useTransition']>
    readonly useTransitionState: UnwrapRef<typeof import('vue')['useTransitionState']>
    readonly useUrlSearchParams: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useUrlSearchParams']>
    readonly useUserMedia: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useUserMedia']>
    readonly useVModel: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useVModel']>
    readonly useVModels: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useVModels']>
    readonly useVibrate: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useVibrate']>
    readonly useVirtualList: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useVirtualList']>
    readonly useWakeLock: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useWakeLock']>
    readonly useWebNotification: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useWebNotification']>
    readonly useWebSocket: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useWebSocket']>
    readonly useWebWorker: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useWebWorker']>
    readonly useWebWorkerFn: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useWebWorkerFn']>
    readonly useWindowFocus: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useWindowFocus']>
    readonly useWindowScroll: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useWindowScroll']>
    readonly useWindowSize: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['useWindowSize']>
    readonly utils: UnwrapRef<typeof import('../../utils/index')['utils']>
    readonly watch: UnwrapRef<typeof import('vue')['watch']>
    readonly watchArray: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['watchArray']>
    readonly watchAtMost: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['watchAtMost']>
    readonly watchDebounced: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['watchDebounced']>
    readonly watchDeep: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['watchDeep']>
    readonly watchEffect: UnwrapRef<typeof import('vue')['watchEffect']>
    readonly watchIgnorable: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['watchIgnorable']>
    readonly watchImmediate: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['watchImmediate']>
    readonly watchOnce: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['watchOnce']>
    readonly watchPausable: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['watchPausable']>
    readonly watchPostEffect: UnwrapRef<typeof import('vue')['watchPostEffect']>
    readonly watchSyncEffect: UnwrapRef<typeof import('vue')['watchSyncEffect']>
    readonly watchThrottled: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['watchThrottled']>
    readonly watchTriggerable: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['watchTriggerable']>
    readonly watchWithFilter: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['watchWithFilter']>
    readonly whenever: UnwrapRef<typeof import('../../../../../../nuxt/node_modules/@vueuse/core')['whenever']>
    readonly withCtx: UnwrapRef<typeof import('vue')['withCtx']>
    readonly withDirectives: UnwrapRef<typeof import('vue')['withDirectives']>
    readonly withKeys: UnwrapRef<typeof import('vue')['withKeys']>
    readonly withMemo: UnwrapRef<typeof import('vue')['withMemo']>
    readonly withModifiers: UnwrapRef<typeof import('vue')['withModifiers']>
    readonly withScopeId: UnwrapRef<typeof import('vue')['withScopeId']>
  }
}