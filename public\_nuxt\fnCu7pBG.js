import{d as q,i as C,a7 as v,y as V,B as n,o,ac as D,m as i,a9 as N,ad as F,c as m,F as c,q as y,J as U,ae as O,af as w,ag as G,P as I,ah as L,l as P,n as R,ai as J,_ as K}from"./D_zVjEfm.js";const M=q({__name:"my-xmlform",props:{type:{type:String,required:!0,validator:r=>["select","checkbox","radio","select2"].includes(r)},options:{type:Array},getData:{type:[String,Array],default:()=>[]},isFirstDisplayFlag:{type:Boolean,default:!0},xpath:{type:String,required:!0},className:{type:String,default:""},modelValue:{type:[String,Array,Number],default:""},required:{type:Boolean,default:!1}},emits:["update:modelValue","change"],setup(r,{expose:b,emit:g}){const x=C(),_=g,a=r,u=v({get(){return a.type==="checkbox"?a.modelValue?Array.isArray(a.modelValue)?a.modelValue:String(a.modelValue).split(",").filter(Boolean):[]:String(a.modelValue??"")},set(t){_("change",t),_("update:modelValue",t)}}),d=v(()=>{if(!a.xpath)return console.error("my-xmlform: xpath is required"),[];if(a.options)return a.options;const t=x.setup[a.xpath].KIND||[];let l=[];return Array.isArray(t)?l=t.map(s=>({value:V.isEmpty(s.傳回值)==!1?s.傳回值:s.資料,label:String(s.資料)})):l.push({value:V.isEmpty(t.傳回值)==!1?t.傳回值:t.資料,label:String(t.資料)}),l});return b({localOptions:d,localModelValue:u}),(t,l)=>{const s=U,k=F,S=w,E=O,h=G,f=J,A=L,B=D;return o(),n(B,{"fallback-tag":"span",fallback:"Loading"},{default:i(()=>[r.type=="checkbox"?(o(),n(k,{key:0,modelValue:u.value,"onUpdate:modelValue":l[0]||(l[0]=e=>u.value=e)},{default:i(()=>[(o(!0),m(c,null,y(d.value,(e,p)=>(o(),n(s,{key:p,value:e.value,label:e.label},null,8,["value","label"]))),128))]),_:1},8,["modelValue"])):r.type=="radio"?(o(),n(E,{key:1,modelValue:u.value,"onUpdate:modelValue":l[1]||(l[1]=e=>u.value=e)},{default:i(()=>[(o(!0),m(c,null,y(d.value,(e,p)=>(o(),n(S,{key:p,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])):r.type=="select2"?(o(),n(h,I({key:2,modelValue:u.value,"onUpdate:modelValue":l[2]||(l[2]=e=>u.value=e),style:{width:"100%"},filterable:""},a,{options:d.value}),null,16,["modelValue","options"])):(o(),n(A,{key:3,modelValue:u.value,"onUpdate:modelValue":l[3]||(l[3]=e=>u.value=e)},{default:i(()=>[P(f,{value:""},{default:i(()=>l[4]||(l[4]=[R("Select")])),_:1}),(o(!0),m(c,null,y(d.value,(e,p)=>(o(),n(f,{key:p,value:e.value,label:e.label},null,8,["value","label"]))),128))]),_:1},8,["modelValue"])),N(t.$slots,"default",{},void 0,!0)]),_:3})}}}),$=K(M,[["__scopeId","data-v-26fd26ca"]]);export{$ as default};
