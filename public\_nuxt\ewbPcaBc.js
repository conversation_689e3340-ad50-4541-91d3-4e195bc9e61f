import{r as e}from"./D_zVjEfm.js";const l=e([{label:"標題",name:"title",kind:"el-input",rules:[{required:!0,message:"標題 未填"}],value:"",islist:!0,issearch:!0,isedit:!0},{label:"副標題",name:"memo",kind:"el-input",value:"",islist:!1,isedit:!0},{label:"圖",name:"body",kind:"my-upload",props:{width:"1118",height:"600",folder:"images/banner",limit:1},rules:[{required:!1,message:"圖 未上傳"}],value:"",islist:!1,isedit:!0},{label:"按鋌1-標題",name:"field1",kind:"el-input",rules:[{required:!1,message:"按鋌1- 未填"}],value:"",islist:!1,issearch:!1,isedit:!0},{label:"按鋌1-連結",name:"field2",kind:"el-input",props:{placeholder:"https://www.yahoo.com.tw",type:"url"},rules:[{required:!1,message:"錯誤"},{type:"url",required:!1,message:"連結未填"}],value:"",islist:!1,isedit:!0},{label:"按鋌1-開啟方式",name:"field3",kind:"my-xmlform",props:{type:"radio",xpath:"開啟方式"},rules:[{required:!1,message:"開啟方式 未填"}],value:"",islist:!1,issearch:!1,isedit:!0},{label:"按鋌2-標題",name:"field4",kind:"el-input",rules:[{required:!1,message:"按鋌1- 未填"}],value:"",islist:!1,issearch:!1,isedit:!0},{label:"按鋌2-連結",name:"field5",kind:"el-input",props:{placeholder:"https://www.yahoo.com.tw",type:"url"},rules:[{required:!1,message:"錯誤"},{type:"url",required:!1,message:"連結未填"}],value:"",islist:!1,isedit:!0},{label:"按鋌2-開啟方式",name:"field3",kind:"my-xmlform",props:{type:"radio",xpath:"開啟方式"},rules:[{required:!1,message:"開啟方式 未填"}],value:"",islist:!1,issearch:!1,isedit:!0},{label:"按鋌3-標題",name:"field7",kind:"el-input",rules:[{required:!1,message:"按鋌1- 未填"}],value:"",islist:!1,issearch:!1,isedit:!0},{label:"按鋌3-連結",name:"field8",kind:"el-input",props:{placeholder:"https://www.yahoo.com.tw",type:"url"},rules:[{required:!1,message:"錯誤"},{type:"url",required:!1,message:"連結未填"}],value:"",islist:!1,isedit:!0},{label:"按鋌3-開啟方式",name:"field9",kind:"my-xmlform",props:{type:"radio",xpath:"開啟方式"},rules:[{required:!1,message:"開啟方式 未填"}],value:"",islist:!1,issearch:!1,isedit:!0},{label:"開始日期",name:"begindate",kind:"my-dateform",props:{type:"date"},rules:[{required:!1,message:"建立日期 未填"}],value:"",islist:!0,issearch:!0,isedit:!0},{label:"結束日期",name:"closedate",kind:"my-dateform",props:{type:"date"},rules:[{required:!1,message:"結束日期 未填"}],value:"",islist:!0,issearch:!0,isedit:!0},{label:"順序",name:"boardsort",kind:"el-input",props:{type:"number"},rules:[{required:!1,message:"順序 未填"}],value:"",isedit:!0,islist:!0,issearch:!1,memo:"數字小到大"},{label:"建立日期",name:"created_at",kind:"my-dateform",props:{type:"date"},rules:[{required:!0,message:"建立日期 未填"}],value:"",islist:!0,issearch:!0,isedit:!0}]);export{l as fields};
