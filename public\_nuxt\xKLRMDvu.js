import{d as u,j as m,Z as g,an as _,c as l,o as c,b as a,a9 as f,s as v,a3 as h,t as y,F as k,_ as x}from"./D_zVjEfm.js";const b={class:"container-fluid p-1"},w={"aria-label":"Page navigation example"},C={class:"pagination justify-content-end"},P={class:"page-item active","aria-current":"page"},S={class:"page-link"},E={key:0,class:"page-item"},$=u({__name:"my-paginatesimple",props:{data:{type:Object,required:!0,default:0}},emits:["current-change"],setup(r,{emit:p}){const d=p,n=m({previous:1,next:1}),s=r,o=async t=>{d("current-change",t),window.scrollTo(0,0);try{document.querySelector(".el-main").scrollTop=0}catch{}try{const e=document.querySelector('a[name="top"]');e&&e.scrollIntoView({behavior:"smooth",block:"start"})}catch{}};return g(async()=>{s.data!=null&&(n.previous=s.data.prev_page_url?new URL(s.data.prev_page_url).searchParams.get("page"):1,n.next=s.data.next_page_url?new URL(s.data.next_page_url).searchParams.get("page"):1)}),_((t,e,i)=>{throw new Error("my-pagination:"+t.message+`
Stack trace:`+t.stack)}),(t,e)=>(c(),l(k,null,[a("div",b,[a("nav",w,[a("ul",C,[a("li",{class:h(["page-item di",`page-item ${r.data.current_page==1?"disabled":""}`])},[a("a",{class:"page-link",href:"#",onClick:e[0]||(e[0]=i=>o(n.previous)),"aria-label":"Previous"},e[2]||(e[2]=[a("span",{"aria-hidden":"true"},"«",-1)]))],2),a("li",P,[a("span",S,y(r.data.current_page),1)]),n.next!=null?(c(),l("li",E,[a("a",{class:"page-link",href:"#",onClick:e[1]||(e[1]=i=>o(n.next)),"aria-label":"Next"},e[3]||(e[3]=[a("span",{"aria-hidden":"true"},"»",-1)]))])):v("",!0)])])]),f(t.$slots,"default",{},void 0,!0)],64))}}),q=x($,[["__scopeId","data-v-d3a43fe8"]]);export{q as default};
