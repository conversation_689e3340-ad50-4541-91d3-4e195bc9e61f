// Generated by nuxi
{
  "compilerOptions": {
    "paths": {
      "nitropack/types": [
        "../../../../../nuxt/node_modules/nitropack/types"
      ],
      "nitropack": [
        "../../../../../nuxt/node_modules/nitropack"
      ],
      "defu": [
        "../../../../../nuxt/node_modules/defu"
      ],
      "h3": [
        "../../../../../nuxt/node_modules/h3"
      ],
      "consola": [
        "../../../../../nuxt/node_modules/consola"
      ],
      "ofetch": [
        "../../../../../nuxt/node_modules/ofetch"
      ],
      "@unhead/vue": [
        "../../../../../nuxt/node_modules/@unhead/vue"
      ],
      "@nuxt/devtools": [
        "../../../../../nuxt/node_modules/@nuxt/devtools"
      ],
      "@vue/runtime-core": [
        "../../../../../nuxt/node_modules/@vue/runtime-core"
      ],
      "@vue/compiler-sfc": [
        "../../../../../nuxt/node_modules/@vue/compiler-sfc"
      ],
      "unplugin-vue-router/client": [
        "../../../../../nuxt/node_modules/unplugin-vue-router/client"
      ],
      "@nuxt/schema": [
        "../../../../../nuxt/node_modules/@nuxt/schema"
      ],
      "nuxt": [
        "../../../../../nuxt/node_modules/nuxt"
      ],
      "~": [
        ".."
      ],
      "~/*": [
        "../*"
      ],
      "@": [
        ".."
      ],
      "@/*": [
        "../*"
      ],
      "~~": [
        ".."
      ],
      "~~/*": [
        "../*"
      ],
      "@@": [
        ".."
      ],
      "@@/*": [
        "../*"
      ],
      "#shared": [
        "../shared"
      ],
      "assets": [
        "../assets"
      ],
      "assets/*": [
        "../assets/*"
      ],
      "public": [
        "../public"
      ],
      "public/*": [
        "../public/*"
      ],
      "#app": [
        "../../../../../nuxt/node_modules/nuxt/dist/app"
      ],
      "#app/*": [
        "../../../../../nuxt/node_modules/nuxt/dist/app/*"
      ],
      "vue-demi": [
        "../../../../../nuxt/node_modules/nuxt/dist/app/compat/vue-demi"
      ],
      "#image": [
        "../../../../../nuxt/node_modules/@nuxt/image/dist/runtime"
      ],
      "#image/*": [
        "../../../../../nuxt/node_modules/@nuxt/image/dist/runtime/*"
      ],
      "#vue-router": [
        "../../../../../nuxt/node_modules/vue-router"
      ],
      "#imports": [
        "./imports"
      ],
      "#app-manifest": [
        "./manifest/meta/dev"
      ],
      "#components": [
        "./components"
      ],
      "#build": [
        "."
      ],
      "#build/*": [
        "./*"
      ]
    },
    "esModuleInterop": true,
    "skipLibCheck": true,
    "target": "ESNext",
    "allowJs": true,
    "resolveJsonModule": true,
    "moduleDetection": "force",
    "isolatedModules": true,
    "verbatimModuleSyntax": true,
    "strict": true,
    "noUncheckedIndexedAccess": false,
    "forceConsistentCasingInFileNames": true,
    "noImplicitOverride": true,
    "module": "preserve",
    "noEmit": true,
    "lib": [
      "ESNext",
      "dom",
      "dom.iterable",
      "webworker"
    ],
    "jsx": "preserve",
    "jsxImportSource": "vue",
    "types": [],
    "moduleResolution": "Bundler",
    "useDefineForClassFields": true,
    "noImplicitThis": true,
    "allowSyntheticDefaultImports": true
  },
  "include": [
    "../**/*",
    "../.config/nuxt.*",
    "./nuxt.d.ts",
    "../../../../../nuxt/node_modules/@pinia/nuxt/runtime",
    "../../../../../nuxt/node_modules/@pinia/nuxt/dist/runtime",
    "../../../../../nuxt/node_modules/@element-plus/nuxt/runtime",
    "../../../../../nuxt/node_modules/@element-plus/nuxt/dist/runtime",
    "../../../../../nuxt/node_modules/@vueuse/nuxt/runtime",
    "../../../../../nuxt/node_modules/@vueuse/nuxt/dist/runtime",
    "../../../../../nuxt/node_modules/@nuxt/image/runtime",
    "../../../../../nuxt/node_modules/@nuxt/image/dist/runtime",
    "../../../../../nuxt/node_modules/@nuxt/telemetry/runtime",
    "../../../../../nuxt/node_modules/@nuxt/telemetry/dist/runtime",
    "..",
    "../../../../../nuxt/node_modules/nuxt/dist/app",
    "../../../../../nuxt/node_modules/nuxt/dist/app/compat/vue-demi",
    "../../../../../nuxt/node_modules/vue-router/dist/vue-router.node"
  ],
  "exclude": [
    "../dist",
    "../.data",
    "../node_modules",
    "../../../../../nuxt/node_modules/nuxt/node_modules",
    "../../../../../nuxt/node_modules/@pinia/nuxt/runtime/server",
    "../../../../../nuxt/node_modules/@pinia/nuxt/dist/runtime/server",
    "../../../../../nuxt/node_modules/@element-plus/nuxt/runtime/server",
    "../../../../../nuxt/node_modules/@element-plus/nuxt/dist/runtime/server",
    "../../../../../nuxt/node_modules/@vueuse/nuxt/runtime/server",
    "../../../../../nuxt/node_modules/@vueuse/nuxt/dist/runtime/server",
    "../../../../../nuxt/node_modules/@nuxt/image/runtime/server",
    "../../../../../nuxt/node_modules/@nuxt/image/dist/runtime/server",
    "../../../../../nuxt/node_modules/@nuxt/telemetry/runtime/server",
    "../../../../../nuxt/node_modules/@nuxt/telemetry/dist/runtime/server",
    "../../../../../../Users/<USER>/Downloads/.output"
  ]
}