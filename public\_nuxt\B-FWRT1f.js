import{d as $,f as D,g as E,h as V,i as B,j as x,k as z,c,o as r,b as t,l as i,m as d,n as m,p as A,w as F,v as H,e as g,F as R,q as S,s as k,a3 as M,t as h,y as C}from"./D_zVjEfm.js";import{_ as O}from"./I37qKz7r.js";import P from"./HHVucgb9.js";const T={class:"breadcrumb-area"},U={class:"container"},G={class:"row"},I={class:"col-12"},J={"aria-label":"breadcrumb"},K={class:"breadcrumb"},Q={class:"breadcrumb-item"},W={class:"shop-page section-padding-0-100"},X={class:"container"},Y={class:"shop-sorting-data d-flex flex-wrap align-items-center justify-content-between"},Z={class:"search_by_terms"},tt={class:"row"},st={class:"col-12 col-md-8 col-lg-12"},ot={class:"shop-products-area"},at={class:"row"},et={class:"single-product-area mb-50"},nt={class:"product-img"},it={key:0,class:"product-tag"},ct={class:"product-meta d-flex"},rt=["onClick"],dt={class:"product-info mt-25 text-center"},lt={key:0,class:"memo"},ut={class:"row"},_t={class:"col-12"},ft=$({__name:"index",props:{},setup(pt){const f=D();E(),V();const e=B(),p=x({data:[],total:0}),l=x({search:"",page:1,sortname:"",sorttype:""});function j(o){l.page=o,v()}const q=async o=>{if(console.log(o),!o)return;e.data.cart||(e.data.cart=[]);let s=e.data.cart;var n={id:o,qty:1};s instanceof Array?s.filter(u=>u&&u.id==o).length==0&&e.data.cart.push(n):(e.data.cart=[],e.data.cart.push(n)),C.toast("加入成功")},v=async()=>{try{let o=await f.post("api/product",l);if(o.resultcode=="0")Object.assign(p,o.data);else throw new Error(o.resultmessage)}catch(o){C.formElError(o),console.error(o)}finally{}};return z(async()=>{v()}),(o,s)=>{const n=A,b=O,u=P,L=H;return r(),c("div",null,[t("div",T,[s[3]||(s[3]=t("div",{class:"top-breadcrumb-area bg-img bg-overlay d-flex align-items-center justify-content-center",style:{"background-image":"url(img/bg-img/products_bg.jpg)"}},[t("h2",null,"熱銷產品")],-1)),t("div",U,[t("div",G,[t("div",I,[t("nav",J,[t("ol",K,[t("li",Q,[i(n,{to:{path:"/"}},{default:d(()=>s[1]||(s[1]=[m("首頁")])),_:1})]),s[2]||(s[2]=t("li",{class:"breadcrumb-item active"},"熱銷產品",-1))])])])])])]),t("section",W,[t("div",X,[t("div",Y,[s[5]||(s[5]=t("div",{class:"shop-page-count"},[t("p",null,"詢價車")],-1)),t("div",Z,[i(n,{to:{path:"/cart",query:{}},class:"btn alazea-btn2"},{default:d(()=>s[4]||(s[4]=[m(" 我的詢價清單 ")])),_:1})])]),F((r(),c("div",tt,[t("div",st,[t("div",ot,[t("div",at,[(r(!0),c(R,null,S(p.data,(a,N)=>{var y,w;return r(),c("div",{key:N,class:"col-12 col-sm-6 col-lg-4"},[t("div",et,[t("div",nt,[i(n,{to:{path:"/product/show/"+a.id,query:{}}},{default:d(()=>[i(b,{width:"350",height:"415",src:`images/product/${a.img}`,noimage:"images/no-picture.gif"},null,8,["src"])]),_:2},1032,["to"]),a.location&&a.location.toLowerCase().includes("hot")?(r(),c("div",it,s[6]||(s[6]=[t("a",{href:"#"},"Hot",-1)]))):k("",!0),t("div",ct,[t("a",{href:"javascript:void(0)",onClick:_=>q(a.id),class:M((y=g(e).data.cart)!=null&&y.find(_=>_.id===a.id)?"wishlist-btn-active":"wishlist-btn")},h((w=g(e).data.cart)!=null&&w.find(_=>_.id===a.id)?"已加入詢價":"加入詢價"),11,rt),i(n,{to:{path:`/product/show/${a.id}`,query:{}},class:"add-to-cart-btn"},{default:d(()=>s[7]||(s[7]=[m("瀏覽細節")])),_:2},1032,["to"])])]),t("div",dt,[i(n,{to:{path:`/product/show/${a.id}`,query:{}}},{default:d(()=>[t("h6",null,h(a.title),1)]),_:2},1032,["to"]),a.memo?(r(),c("p",lt,h(a.memo),1)):k("",!0)])])])}),128))])])])])),[[L,g(f).getLoading()]]),t("div",ut,[t("div",_t,[i(u,{total:p.total,page:l.page,"onUpdate:page":s[0]||(s[0]=a=>l.page=a),onCurrentChange:j},null,8,["total","page"])])])])])])}}});export{ft as default};
