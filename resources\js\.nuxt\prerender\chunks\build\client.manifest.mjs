const client_manifest = {
  "../../../../nuxt/node_modules/nuxt/dist/app/entry.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "D_zVjEfm.js",
    "name": "entry",
    "src": "../../../../nuxt/node_modules/nuxt/dist/app/entry.js",
    "isEntry": true,
    "dynamicImports": [
      "fakes/adminlogin.js",
      "fakes/cart.js",
      "fakes/contact.js",
      "fakes/data.js",
      "fakes/forgetpassword.js",
      "fakes/login.js",
      "fakes/ordergroup.js",
      "layouts/admin.vue",
      "layouts/front.vue",
      "layouts/raw.vue"
    ],
    "css": [
      "entry.BGE7dmog.css"
    ]
  },
  "entry.BGE7dmog.css": {
    "file": "entry.BGE7dmog.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "_!~{01a}~.js": {
    "resourceType": "style",
    "prefetch": true,
    "preload": true,
    "file": "my-dialog.CpyJbbYg.css",
    "src": "_!~{01a}~.js"
  },
  "_2eTUCwpK.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "2eTUCwpK.js",
    "name": "my-ckeditor.vue",
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "_4qmtJa-f.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "4qmtJa-f.js",
    "name": "my-epost.vue",
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "_BWS-stcc.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BWS-stcc.js",
    "name": "my-seo.vue",
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "_CF83mHfB.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CF83mHfB.js",
    "name": "my-dialog.vue",
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ],
    "dynamicImports": [
      "pages/admin/adminuser/edit.vue",
      "pages/admin/adminuser/index.vue",
      "pages/admin/adminuserloginlog/index.vue",
      "pages/admin/board/edit.vue",
      "pages/admin/board/index.vue",
      "pages/admin/config/edit.vue",
      "pages/admin/epost/edit.vue",
      "pages/admin/excelimport/index.vue",
      "pages/admin/feedback/edit.vue",
      "pages/admin/feedback/index.vue",
      "pages/admin/formquerylog/index.vue",
      "pages/admin/index/index.vue",
      "pages/admin/kind/edit.vue",
      "pages/admin/kind/index.vue",
      "pages/admin/login/index.vue",
      "pages/admin/ordergroup/edit.vue",
      "pages/admin/ordergroup/index.vue",
      "pages/admin/ordergroup/show.vue",
      "pages/admin/product/edit.vue",
      "pages/admin/product/index.vue",
      "pages/admin/useredit/edit.vue",
      "pages/admin/viewfile/edit.vue"
    ],
    "css": [
      "my-dialog.CpyJbbYg.css"
    ]
  },
  "my-dialog.CpyJbbYg.css": {
    "file": "my-dialog.CpyJbbYg.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "_CKhpvNlx.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CKhpvNlx.js",
    "name": "useRecaptcha",
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "_CQvHppDm.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CQvHppDm.js",
    "name": "my-buttonadd.vue",
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "_G4Mf9Qin.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "G4Mf9Qin.js",
    "name": "my-dateform.vue",
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "_I37qKz7r.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "I37qKz7r.js",
    "name": "my-img.vue",
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "__cPjsZyH.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "_cPjsZyH.js",
    "name": "my-buttondel.vue",
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "_jBNyKXjo.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "jBNyKXjo.js",
    "name": "my-breadcrumb.vue",
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "components/my-apicascader.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CnUqgQ1w.js",
    "name": "my-apicascader",
    "src": "components/my-apicascader.vue",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "components/my-apiform.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "QXd2fk4f.js",
    "name": "my-apiform",
    "src": "components/my-apiform.vue",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ],
    "css": [
      "my-apiform.ocLFtWCT.css"
    ]
  },
  "my-apiform.ocLFtWCT.css": {
    "file": "my-apiform.ocLFtWCT.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "components/my-apiselectform.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BnF1OonV.js",
    "name": "my-apiselectform",
    "src": "components/my-apiselectform.vue",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "components/my-autocomplete.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CoMNu2FA.js",
    "name": "my-autocomplete",
    "src": "components/my-autocomplete.vue",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "components/my-breadcrumb.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "GLgMn1Be.js",
    "name": "my-breadcrumb",
    "src": "components/my-breadcrumb.vue",
    "isDynamicEntry": true,
    "imports": [
      "_jBNyKXjo.js",
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "components/my-buttonadd.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BH8TMaCm.js",
    "name": "my-buttonadd",
    "src": "components/my-buttonadd.vue",
    "isDynamicEntry": true,
    "imports": [
      "_CQvHppDm.js",
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "components/my-buttondel.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CUUWlDlo.js",
    "name": "my-buttondel",
    "src": "components/my-buttondel.vue",
    "isDynamicEntry": true,
    "imports": [
      "__cPjsZyH.js",
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "components/my-ckeditor.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "lQdoouag.js",
    "name": "my-ckeditor",
    "src": "components/my-ckeditor.vue",
    "isDynamicEntry": true,
    "imports": [
      "_2eTUCwpK.js",
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "components/my-dateform.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BCnACKoP.js",
    "name": "my-dateform",
    "src": "components/my-dateform.vue",
    "isDynamicEntry": true,
    "imports": [
      "_G4Mf9Qin.js",
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "components/my-dialog.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BTA9pSr6.js",
    "name": "my-dialog",
    "src": "components/my-dialog.vue",
    "isDynamicEntry": true,
    "imports": [
      "_CF83mHfB.js",
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "components/my-epost.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "Dxn8K8vU.js",
    "name": "my-epost",
    "src": "components/my-epost.vue",
    "isDynamicEntry": true,
    "imports": [
      "_4qmtJa-f.js",
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "components/my-img.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "C2U-GmCX.js",
    "name": "my-img",
    "src": "components/my-img.vue",
    "isDynamicEntry": true,
    "imports": [
      "_I37qKz7r.js",
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "components/my-paginatesimple.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "xKLRMDvu.js",
    "name": "my-paginatesimple",
    "src": "components/my-paginatesimple.vue",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ],
    "css": [
      "my-paginatesimple.kqrW_3Cg.css"
    ]
  },
  "my-paginatesimple.kqrW_3Cg.css": {
    "file": "my-paginatesimple.kqrW_3Cg.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "components/my-pagination.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "HHVucgb9.js",
    "name": "my-pagination",
    "src": "components/my-pagination.vue",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ],
    "css": [
      "my-pagination.C28Xn-qp.css"
    ]
  },
  "my-pagination.C28Xn-qp.css": {
    "file": "my-pagination.C28Xn-qp.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "components/my-search.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "sXr78Ku-.js",
    "name": "my-search",
    "src": "components/my-search.vue",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js",
      "_G4Mf9Qin.js"
    ],
    "css": [
      "my-search.Din1H_Ix.css"
    ]
  },
  "my-search.Din1H_Ix.css": {
    "file": "my-search.Din1H_Ix.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "components/my-seo.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "D3JRigd2.js",
    "name": "my-seo",
    "src": "components/my-seo.vue",
    "isDynamicEntry": true,
    "imports": [
      "_BWS-stcc.js",
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "components/my-upload.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CKt8fKXZ.js",
    "name": "my-upload",
    "src": "components/my-upload.vue",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ],
    "css": [
      "my-upload.C1aM5ZLD.css"
    ]
  },
  "my-upload.C1aM5ZLD.css": {
    "file": "my-upload.C1aM5ZLD.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "components/my-xmlform.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "fnCu7pBG.js",
    "name": "my-xmlform",
    "src": "components/my-xmlform.vue",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ],
    "css": [
      "my-xmlform.DrN1F5vA.css"
    ]
  },
  "my-xmlform.DrN1F5vA.css": {
    "file": "my-xmlform.DrN1F5vA.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "datas/aboutpage.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DEPTJ9I4.js",
    "name": "aboutpage",
    "src": "datas/aboutpage.js",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "datas/all.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "AII-GxSx.js",
    "name": "all",
    "src": "datas/all.js",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "datas/banner.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "ewbPcaBc.js",
    "name": "banner",
    "src": "datas/banner.js",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "datas/case.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DeQTbgbd.js",
    "name": "case",
    "src": "datas/case.js",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "datas/customer.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "MMxUCDix.js",
    "name": "customer",
    "src": "datas/customer.js",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "datas/news.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DIRD0ar_.js",
    "name": "news",
    "src": "datas/news.js",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "datas/productkind.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DZ_RHEX4.js",
    "name": "productkind",
    "src": "datas/productkind.js",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "datas/service.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "B2ebk17F.js",
    "name": "service",
    "src": "datas/service.js",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "datas/serviceitem.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DbPQOmG-.js",
    "name": "serviceitem",
    "src": "datas/serviceitem.js",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "fakes/adminlogin.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "D89mYBQu.js",
    "name": "adminlogin",
    "src": "fakes/adminlogin.js",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "fakes/cart.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "D4E4NNmG.js",
    "name": "cart",
    "src": "fakes/cart.js",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "fakes/contact.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BdSOIv-e.js",
    "name": "contact",
    "src": "fakes/contact.js",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "fakes/data.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DkONAkuC.js",
    "name": "data",
    "src": "fakes/data.js",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "fakes/forgetpassword.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "k0vJOLy_.js",
    "name": "forgetpassword",
    "src": "fakes/forgetpassword.js",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "fakes/login.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "B4My3aCe.js",
    "name": "login",
    "src": "fakes/login.js",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "fakes/ordergroup.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DSoKeBb0.js",
    "name": "ordergroup",
    "src": "fakes/ordergroup.js",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "layouts/admin.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BAdlGw-2.js",
    "name": "admin",
    "src": "layouts/admin.vue",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ],
    "css": [
      "admin.Df1HJPlM.css"
    ]
  },
  "admin.Df1HJPlM.css": {
    "file": "admin.Df1HJPlM.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "layouts/front.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DWwBub4e.js",
    "name": "front",
    "src": "layouts/front.vue",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js",
      "_4qmtJa-f.js"
    ]
  },
  "layouts/raw.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "D4zfHrzP.js",
    "name": "raw",
    "src": "layouts/raw.vue",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "pages/[...slug].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "lqapCnqi.js",
    "name": "_...slug_",
    "src": "pages/[...slug].vue",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ],
    "css": [
      "_...DTKvc_Os.css"
    ]
  },
  "_...DTKvc_Os.css": {
    "file": "_...DTKvc_Os.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/about/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "C4ZGnHXW.js",
    "name": "index",
    "src": "pages/about/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "_BWS-stcc.js",
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js",
      "_4qmtJa-f.js",
      "_I37qKz7r.js"
    ],
    "css": [
      "index.Dnd3IMGT.css"
    ]
  },
  "index.Dnd3IMGT.css": {
    "file": "index.Dnd3IMGT.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/aboutpage/[id].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "x2Cw3tm9.js",
    "name": "_id_",
    "src": "pages/aboutpage/[id].vue",
    "isDynamicEntry": true,
    "imports": [
      "_BWS-stcc.js",
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "pages/admin/adminuser/edit.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "t9XsTDlZ.js",
    "name": "edit",
    "src": "pages/admin/adminuser/edit.vue",
    "isDynamicEntry": true,
    "imports": [
      "_jBNyKXjo.js",
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js",
      "components/my-xmlform.vue"
    ]
  },
  "pages/admin/adminuser/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CJ4yysAX.js",
    "name": "index",
    "src": "pages/admin/adminuser/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "_jBNyKXjo.js",
      "components/my-search.vue",
      "__cPjsZyH.js",
      "_CQvHppDm.js",
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js",
      "components/my-pagination.vue",
      "_CF83mHfB.js",
      "_G4Mf9Qin.js"
    ]
  },
  "pages/admin/adminuserloginlog/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BBMl127G.js",
    "name": "index",
    "src": "pages/admin/adminuserloginlog/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "_jBNyKXjo.js",
      "components/my-search.vue",
      "components/my-pagination.vue",
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js",
      "_CF83mHfB.js",
      "_G4Mf9Qin.js"
    ]
  },
  "pages/admin/board/edit.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "D98v7cVU.js",
    "name": "edit",
    "src": "pages/admin/board/edit.vue",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js",
      "_jBNyKXjo.js"
    ],
    "dynamicImports": [
      "datas/aboutpage.js",
      "datas/all.js",
      "datas/banner.js",
      "datas/case.js",
      "datas/customer.js",
      "datas/news.js",
      "datas/productkind.js",
      "datas/service.js",
      "datas/serviceitem.js"
    ]
  },
  "pages/admin/board/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BJKtYJtC.js",
    "name": "index",
    "src": "pages/admin/board/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js",
      "_jBNyKXjo.js",
      "components/my-search.vue",
      "__cPjsZyH.js",
      "_CQvHppDm.js",
      "components/my-pagination.vue",
      "_CF83mHfB.js",
      "_G4Mf9Qin.js"
    ],
    "dynamicImports": [
      "datas/aboutpage.js",
      "datas/all.js",
      "datas/banner.js",
      "datas/case.js",
      "datas/customer.js",
      "datas/news.js",
      "datas/productkind.js",
      "datas/service.js",
      "datas/serviceitem.js"
    ]
  },
  "pages/admin/config/edit.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "ChWBnVGr.js",
    "name": "edit",
    "src": "pages/admin/config/edit.vue",
    "isDynamicEntry": true,
    "imports": [
      "_jBNyKXjo.js",
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "pages/admin/epost/edit.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "Bq1ujDbh.js",
    "name": "edit",
    "src": "pages/admin/epost/edit.vue",
    "isDynamicEntry": true,
    "imports": [
      "_jBNyKXjo.js",
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js",
      "_2eTUCwpK.js"
    ]
  },
  "pages/admin/excelimport/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DQgfFnhs.js",
    "name": "index",
    "src": "pages/admin/excelimport/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "pages/admin/feedback/edit.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CQF4jwmL.js",
    "name": "edit",
    "src": "pages/admin/feedback/edit.vue",
    "isDynamicEntry": true,
    "imports": [
      "_jBNyKXjo.js",
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "pages/admin/feedback/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BzDCd5bH.js",
    "name": "index",
    "src": "pages/admin/feedback/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "_BWS-stcc.js",
      "_jBNyKXjo.js",
      "components/my-search.vue",
      "__cPjsZyH.js",
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js",
      "components/my-pagination.vue",
      "_CF83mHfB.js",
      "_G4Mf9Qin.js"
    ]
  },
  "pages/admin/formquerylog/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "ODG5hW3s.js",
    "name": "index",
    "src": "pages/admin/formquerylog/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "_BWS-stcc.js",
      "_jBNyKXjo.js",
      "components/my-search.vue",
      "components/my-pagination.vue",
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js",
      "_CF83mHfB.js",
      "_G4Mf9Qin.js"
    ]
  },
  "pages/admin/index/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "Dc9n8SY3.js",
    "name": "index",
    "src": "pages/admin/index/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "pages/admin/kind/edit.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DFv2tWN4.js",
    "name": "edit",
    "src": "pages/admin/kind/edit.vue",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js",
      "_jBNyKXjo.js"
    ],
    "dynamicImports": [
      "datas/aboutpage.js",
      "datas/all.js",
      "datas/banner.js",
      "datas/case.js",
      "datas/customer.js",
      "datas/news.js",
      "datas/productkind.js",
      "datas/service.js",
      "datas/serviceitem.js"
    ]
  },
  "pages/admin/kind/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "GgDM1tNs.js",
    "name": "index",
    "src": "pages/admin/kind/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js",
      "_jBNyKXjo.js",
      "components/my-search.vue",
      "__cPjsZyH.js",
      "_CQvHppDm.js",
      "components/my-pagination.vue",
      "_CF83mHfB.js",
      "_G4Mf9Qin.js"
    ],
    "dynamicImports": [
      "datas/aboutpage.js",
      "datas/all.js",
      "datas/banner.js",
      "datas/case.js",
      "datas/customer.js",
      "datas/news.js",
      "datas/productkind.js",
      "datas/service.js",
      "datas/serviceitem.js"
    ]
  },
  "pages/admin/login/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "k4dtWBPv.js",
    "name": "index",
    "src": "pages/admin/login/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js",
      "_CKhpvNlx.js"
    ],
    "css": [
      "index.B9BDNnw-.css"
    ]
  },
  "index.B9BDNnw-.css": {
    "file": "index.B9BDNnw-.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/admin/ordergroup/edit.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CyCvQXuY.js",
    "name": "edit",
    "src": "pages/admin/ordergroup/edit.vue",
    "isDynamicEntry": true,
    "imports": [
      "_jBNyKXjo.js",
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "pages/admin/ordergroup/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "y9aR0iNK.js",
    "name": "index",
    "src": "pages/admin/ordergroup/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "_BWS-stcc.js",
      "_jBNyKXjo.js",
      "components/my-search.vue",
      "__cPjsZyH.js",
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js",
      "components/my-pagination.vue",
      "_CF83mHfB.js",
      "_G4Mf9Qin.js"
    ]
  },
  "pages/admin/ordergroup/show.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CBWOu-An.js",
    "name": "show",
    "src": "pages/admin/ordergroup/show.vue",
    "isDynamicEntry": true,
    "imports": [
      "_jBNyKXjo.js",
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "pages/admin/product/edit.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BBdYVKME.js",
    "name": "edit",
    "src": "pages/admin/product/edit.vue",
    "isDynamicEntry": true,
    "imports": [
      "_jBNyKXjo.js",
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js",
      "components/my-upload.vue",
      "components/my-xmlform.vue",
      "_2eTUCwpK.js",
      "_G4Mf9Qin.js"
    ]
  },
  "pages/admin/product/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "Cu6CK232.js",
    "name": "index",
    "src": "pages/admin/product/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "_BWS-stcc.js",
      "_jBNyKXjo.js",
      "components/my-search.vue",
      "__cPjsZyH.js",
      "_CQvHppDm.js",
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js",
      "components/my-pagination.vue",
      "_CF83mHfB.js",
      "_G4Mf9Qin.js"
    ]
  },
  "pages/admin/useredit/edit.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CJyaJRfW.js",
    "name": "edit",
    "src": "pages/admin/useredit/edit.vue",
    "isDynamicEntry": true,
    "imports": [
      "_jBNyKXjo.js",
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "pages/admin/viewfile/edit.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DyBIXSDg.js",
    "name": "edit",
    "src": "pages/admin/viewfile/edit.vue",
    "isDynamicEntry": true,
    "imports": [
      "_jBNyKXjo.js",
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js",
      "_2eTUCwpK.js"
    ]
  },
  "pages/cart/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CHhaEufr.js",
    "name": "index",
    "src": "pages/cart/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js",
      "_CKhpvNlx.js"
    ]
  },
  "pages/case/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "Di1egPhV.js",
    "name": "index",
    "src": "pages/case/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js",
      "components/my-pagination.vue"
    ]
  },
  "pages/contact/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CtAOoyOS.js",
    "name": "index",
    "src": "pages/contact/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js",
      "_CKhpvNlx.js"
    ]
  },
  "pages/index/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "mvrQR9SN.js",
    "name": "index",
    "src": "pages/index/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "_I37qKz7r.js",
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "pages/logs/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DARJfepB.js",
    "name": "index",
    "src": "pages/logs/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ],
    "css": [
      "index.DiQGt0YV.css"
    ]
  },
  "index.DiQGt0YV.css": {
    "file": "index.DiQGt0YV.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/news/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "Cw7SrP5O.js",
    "name": "index",
    "src": "pages/news/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "_I37qKz7r.js",
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js",
      "components/my-pagination.vue"
    ]
  },
  "pages/news/show/[id].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DdfCv5JP.js",
    "name": "_id_",
    "src": "pages/news/show/[id].vue",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js",
      "_I37qKz7r.js"
    ]
  },
  "pages/page/[id].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "Ffzhgki-.js",
    "name": "_id_",
    "src": "pages/page/[id].vue",
    "isDynamicEntry": true,
    "imports": [
      "_BWS-stcc.js",
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "pages/privacy/[id].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "Drhuyww4.js",
    "name": "_id_",
    "src": "pages/privacy/[id].vue",
    "isDynamicEntry": true,
    "imports": [
      "_BWS-stcc.js",
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js",
      "_4qmtJa-f.js",
      "_I37qKz7r.js"
    ],
    "css": [
      "_id_.BVyat0Ok.css"
    ]
  },
  "_id_.BVyat0Ok.css": {
    "file": "_id_.BVyat0Ok.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/privacy/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BJ3qErLu.js",
    "name": "index",
    "src": "pages/privacy/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "_BWS-stcc.js",
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js",
      "_4qmtJa-f.js"
    ]
  },
  "pages/product/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "B-FWRT1f.js",
    "name": "index",
    "src": "pages/product/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js",
      "_I37qKz7r.js",
      "components/my-pagination.vue"
    ]
  },
  "pages/product/show/[id].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DXdbt4Fc.js",
    "name": "_id_",
    "src": "pages/product/show/[id].vue",
    "isDynamicEntry": true,
    "imports": [
      "_BWS-stcc.js",
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "pages/vue/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DiwT4FyQ.js",
    "name": "index",
    "src": "pages/vue/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "../../../../nuxt/node_modules/nuxt/dist/app/entry.js"
    ]
  }
};

export { client_manifest as default };
//# sourceMappingURL=client.manifest.mjs.map
