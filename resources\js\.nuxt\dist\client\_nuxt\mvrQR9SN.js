import{_ as R}from"./I37qKz7r.js";import{a4 as W,d as q,i as A,f as H,g as U,h as M,r as y,j as g,k as J,c as o,o as l,b as e,F as r,q as m,x as S,t as i,s as b,n as N,l as k,m as B,p as P,y as h,z as Q}from"./D_zVjEfm.js";const G=W("/img/bg-img/about_photo01.jpg"),K={class:"hero-area"},X={class:"container h-100"},Y={class:"row h-100 align-items-center"},Z={class:"col-12"},ee={class:"hero-slides-content text-left"},te={class:"welcome-btn-group"},se=["href","target"],ae=["href","target"],oe=["href","target"],le={class:"our-services-area section-padding-100-0"},ie={class:"container"},ne={class:"row panel-group mb-100"},ce={class:"tab-group col-12 col-lg-4"},re=["for","onClick"],de={class:"content-group col-12 col-lg-8"},ue={class:"alazea-video-area",style:{display:"block !important"}},me={class:"video-container"},_e=["src","title"],ge={class:"note",style:{display:"block !important"}},he={class:"our-services-area bg-gray section-padding-100-0"},ve={class:"container"},pe={class:"row justify-content-between"},fe={class:"col-12 col-lg-h"},we={class:"row"},ye={class:"alazea-service-area mb-100 col-12 col-lg-6"},be={class:"service-icon mr-30"},ke=["src"],xe={class:"service-content"},Ee={class:"alazea-service-area mb-100 col-12 col-lg-6"},ze={class:"service-icon mr-30"},Ce=["src"],$e={class:"service-content"},je={class:"alazea-blog-area section-padding-100-0"},Fe={class:"container"},Oe={class:"row justify-content-center"},Se={class:"single-blog-post mb-100"},Ne={class:"post-thumbnail mb-30"},Be={class:"post-content"},Te={class:"post-meta"},Ve={class:"svg-inline--fa fa-clock fa-w-16","aria-hidden":"true",style:{color:"#ff5657","margin-right":"5px"},"data-prefix":"far","data-icon":"clock",role:"img",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512","data-fa-i2svg":""},De={class:"post-excerpt"},qe=q({__name:"index",setup(Ie){A();const _=H();U(),M();const v=y(null);y([]);const d=g({resultcode:0,resultmessage:"",data:[]}),x=g({resultcode:0,resultmessage:"",data:[]}),p=g({resultcode:0,resultmessage:"",data:[]}),f=g({resultcode:0,resultmessage:"",data:[]}),T=()=>{if(v.value&&window.$&&p.data.length>0){const s=window.$,a=s(v.value);a.hasClass("owl-loaded")&&a.trigger("destroy.owl.carousel"),a.owlCarousel({items:1,margin:0,loop:!0,nav:!1,dots:!1,autoplay:!0,center:!0,autoplayTimeout:5e3,smartSpeed:1e3})}},V=async()=>{try{let s=await _.post("api/service",{});if(s.resultcode=="0")Object.assign(d,s.data),d.data.length>0&&E(d.data[0].id);else throw new Error(s.resultmessage)}catch(s){h.formElError(s),console.error(s)}finally{}},D=async()=>{try{let s=await _.post("api/news",{pagesize:6});if(s.resultcode=="0")Object.assign(x,s.data);else throw new Error(s.resultmessage)}catch(s){h.formElError(s),console.error(s)}finally{}},I=async()=>{try{let s=await _.post("api/banner",{});if(s.resultcode=="0")Object.assign(p,s.data),await Q(),T();else throw new Error(s.resultmessage)}catch(s){h.formElError(s),console.error(s)}finally{}},L=async()=>{try{let s=await _.post("api/serviceitem",{});if(s.resultcode=="0")Object.assign(f,s.data);else throw new Error(s.resultmessage)}catch(s){h.formElError(s),console.error(s)}finally{}},c=y(null),E=async s=>{const a=d.data.find(w=>w.id==s);c.value=a||null};return J(async()=>{await new Promise(s=>{const a=()=>{window.$?s(!0):setTimeout(a,100)};a()})}),V(),D(),I(),L(),(s,a)=>{var C,$,j,F;const w=R,z=P;return l(),o(r,null,[e("section",K,[e("div",{class:"hero-post-slides owl-carousel",ref_key:"bannerCarousel",ref:v},[(l(!0),o(r,null,m(p.data,(t,n)=>(l(),o("div",{key:n,class:"single-hero-post bg-overlay"},[e("div",{class:"slide-img bg-img",style:S({backgroundImage:`url(/images/banner/${t.body})`})},null,4),e("div",X,[e("div",Y,[e("div",Z,[e("div",ee,[e("h2",null,i(t.title),1),e("p",null,i(t.memo)+"!",1),e("div",te,[t.field1?(l(),o("a",{key:0,href:t.field2||"#",target:t.field3,class:"btn alazea-btn"},i(t.field1),9,se)):b("",!0),t.field4?(l(),o("a",{key:1,href:t.field5||"#",target:t.field6,class:"btn alazea-btn"},i(t.field4),9,ae)):b("",!0),t.field7?(l(),o("a",{key:2,href:t.field8||"#",target:t.field9,class:"btn alazea-btn"},i(t.field7),9,oe)):b("",!0)])])])])])]))),128))],512)]),e("section",le,[e("div",ie,[a[0]||(a[0]=e("div",{class:"row"},[e("div",{class:"col-12"},[e("div",{class:"section-heading2 text-center"},[e("h2",null,"我們的服務"),e("p",null,"WHAT WE DO")])])],-1)),e("div",ne,[e("div",ce,[(l(!0),o(r,null,m(d.data,(t,n)=>{var u,O;return l(),o("label",{key:n,for:"radio"+t.id,style:S({color:((u=c.value)==null?void 0:u.id)==t.id?"#FFF":"#333",background:((O=c.value)==null?void 0:O.id)==t.id?"#fd585c":"#FFF"}),onClick:Le=>E(t.id)},i(t.title),13,re)}),128))]),e("div",de,[e("div",ue,[e("div",me,[e("iframe",{src:(C=c.value)==null?void 0:C.youtube,title:($=c.value)==null?void 0:$.title,frameborder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",referrerpolicy:"strict-origin-when-cross-origin",allowfullscreen:!0},null,8,_e)]),e("div",ge,[e("h3",null,i((j=c.value)==null?void 0:j.title),1),N(" "+i((F=c.value)==null?void 0:F.body),1)])])])])])]),e("section",he,[e("div",ve,[a[2]||(a[2]=e("div",{class:"row"},[e("div",{class:"col-12"},[e("div",{class:"section-heading2 text-center"},[e("h2",null,"關於我們"),e("p",null,"WHO WE ARE")])])],-1)),e("div",pe,[a[1]||(a[1]=e("div",{class:"col-12 col-lg-5"},[e("div",{class:"alazea-about-area mb-100"},[e("img",{src:G,alt:""})])],-1)),e("div",fe,[e("div",we,[e("div",ye,[(l(!0),o(r,null,m(f.data.slice(0,4),(t,n)=>(l(),o("div",{class:"single-service-area d-flex align-items-center wow fadeInUp","data-wow-delay":"100ms",style:{"animation-delay":"100ms","animation-name":"none"},key:n},[e("div",be,[e("img",{width:"90",height:"90",src:`images/serviceitem/${t.img}`},null,8,ke)]),e("div",xe,[e("h5",null,i(t.title),1)])]))),128))]),e("div",Ee,[(l(!0),o(r,null,m(f.data.slice(-4),(t,n)=>(l(),o("div",{class:"single-service-area d-flex align-items-center wow fadeInUp","data-wow-delay":"100ms",style:{"animation-delay":"100ms","animation-name":"none"},key:n},[e("div",ze,[e("img",{width:"90",height:"90",src:`images/serviceitem/${t.img}`},null,8,Ce)]),e("div",$e,[e("h5",null,i(t.title),1)])]))),128))])])])])])]),e("section",je,[e("div",Fe,[a[4]||(a[4]=e("div",{class:"row"},[e("div",{class:"col-12"},[e("div",{class:"section-heading text-center"},[e("h2",null,"最新消息")])])],-1)),e("div",Oe,[(l(!0),o(r,null,m(x.data,(t,n)=>{var u;return l(),o("div",{key:n,class:"col-12 col-md-6 col-lg-4"},[e("div",Se,[e("div",Ne,[k(z,{to:{path:`/news/show/${t.id}`,query:{}}},{default:B(()=>[k(w,{src:`images/news/${t.img}`,noimage:"images/no-picture.gif"},null,8,["src"])]),_:2},1032,["to"])]),e("div",Be,[k(z,{to:{path:`/news/show/${t.id}`,query:{}},class:"post-title"},{default:B(()=>[e("h5",null,i(t.title),1)]),_:2},1032,["to"]),e("div",Te,[(l(),o("svg",Ve,a[3]||(a[3]=[e("path",{fill:"currentColor",d:"M256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8zm0 448c-110.5 0-200-89.5-200-200S145.5 56 256 56s200 89.5 200 200-89.5 200-200 200zm61.8-104.4l-84.9-61.7c-3.1-2.3-4.9-5.9-4.9-9.7V116c0-6.6 5.4-12 12-12h32c6.6 0 12 5.4 12 12v141.7l66.8 48.6c5.4 3.9 6.5 11.4 2.6 16.8L334.6 349c-3.9 5.3-11.4 6.5-16.8 2.6z"},null,-1)]))),N(" "+i(t.created_at),1)]),e("p",De,i(t.memo||((u=t.body)==null?void 0:u.substring(0,100))+"..."),1)])])])}),128))])])])],64)}}});export{qe as default};
