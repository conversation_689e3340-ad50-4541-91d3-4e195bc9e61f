import{d as _,f as g,r,k as V,y as h,a7 as y,B as v,o as w,m as k,l as C,a9 as x,aD as E,ac as B}from"./D_zVjEfm.js";const b=_({__name:"my-autocomplete",props:["modelValue","api"],emits:["update:modelValue","change"],setup(n,{emit:u}){const c=g(),o=n,p=u;r("");const l=r([]),d=(e,t)=>{let a=l.value;a=e?a.filter(m(e)):a,console.log(["results",a]),t(a)},m=e=>t=>t.value.toLowerCase().indexOf(e.toLowerCase())===0;V(async()=>{try{let e=await c.post(o.api,{},!1);if(e.resultcode=="0")l.value=e.data.map(t=>({value:t.title,id:""+t.id}));else throw new Error(e.resultmessage)}catch(e){h.formElError(e),console.error(e)}finally{}});const s=y({get(){return o.modelValue},set:e=>{p("update:modelValue",e)}});return(e,t)=>{const a=E,i=B;return w(),v(i,{"fallback-tag":"span",fallback:"Loading"},{default:k(()=>[C(a,{modelValue:s.value,"onUpdate:modelValue":t[0]||(t[0]=f=>s.value=f),"fetch-suggestions":d,placeholder:"Please input","trigger-on-focus":!1},null,8,["modelValue"]),x(e.$slots,"default")]),_:3})}}});export{b as default};
