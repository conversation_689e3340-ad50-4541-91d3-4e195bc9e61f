import{_ as k}from"./jBNyKXjo.js";import D from"./sXr78Ku-.js";import $ from"./HHVucgb9.js";import{d as x,f as S,g as E,i as N,r as g,j as p,k as j,w as B,v as L,e as O,c as d,o as m,l as i,b as e,F as P,q as F,t as r,y as I}from"./D_zVjEfm.js";import{_ as V}from"./CF83mHfB.js";import"./G4Mf9Qin.js";const q={class:"table-responsive-md"},H={class:"table table-striped table-hover table-bordered table-fixed"},Q=x({__name:"index",props:{},setup(M){const u=S();E(),N();const _=g(""),c=p({data:[]});g("");const n=p({search:"",searchname:"",searchdatename:"",page:1,sortname:"",sorttype:"",del:[]});function f(t){n.page=t,l()}const s=async t=>{n.page=1,n.sortname=t,n.sorttype=n.sorttype=="asc"?"desc":"asc",l()},b=async t=>{n.page=1,n.sortname="",Object.assign(n,t),l()},y=(t=!1)=>{t&&l()},l=async()=>{try{let t=await u.post("api/admin/adminuserloginlog",n);if(t.resultcode=="0")Object.assign(c,t.data);else throw new Error(t.resultmessage)}catch(t){I.formElError(t),console.error(t)}finally{}};return j(async()=>{l()}),(t,a)=>{const h=k,v=D,w=$,C=L;return B((m(),d("div",null,[i(h,{id:"adminuserloginlog",refs:"breadcrumb"}),i(v,{searchNames:{"":"全部","adminuserloginlog.account":"帳號","adminuserloginlog.clientip":"IP","adminuserloginlog.loginstatus":"登入狀態"},searchDateNames:{"adminuserloginlog.logouttime":"時間","adminuserloginlog.created_at":"建立日期"},onOnSearch:b}),e("div",q,[e("table",H,[e("thead",null,[e("tr",null,[e("th",{width:"",onClick:a[0]||(a[0]=o=>s("account")),class:"sortable"},"帳號"),e("th",{width:"",onClick:a[1]||(a[1]=o=>s("clientip")),class:"sortable"},"IP"),e("th",{width:"",onClick:a[2]||(a[2]=o=>s("loginstatus")),class:"sortable"},"登入狀態"),e("th",{width:"",onClick:a[3]||(a[3]=o=>s("logouttime")),class:"sortable"},"時間"),e("th",{width:"",onClick:a[4]||(a[4]=o=>s("created_at")),class:"sortable"},"建立日期")])]),e("tbody",null,[(m(!0),d(P,null,F(c.data,(o,R)=>(m(),d("tr",null,[e("td",null,r(o.account),1),e("td",null,r(o.clientip),1),e("td",null,r(o.loginstatus),1),e("td",null,r(o.logouttime),1),e("td",null,r(o.created_at),1)]))),256))])])]),i(w,{total:c.total,page:n.page,"onUpdate:page":a[5]||(a[5]=o=>n.page=o),onCurrentChange:f},null,8,["total","page"]),i(V,{ref_key:"myDialog",ref:_,onClosedDialog:y},null,512)])),[[C,O(u).getLoading()]])}}});export{Q as default};
