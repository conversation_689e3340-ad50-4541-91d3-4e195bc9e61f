const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DEPTJ9I4.js","./D_zVjEfm.js","./entry.BGE7dmog.css","./AII-GxSx.js","./ewbPcaBc.js","./DeQTbgbd.js","./MMxUCDix.js","./DIRD0ar_.js","./DZ_RHEX4.js","./B2ebk17F.js","./DbPQOmG-.js"])))=>i.map(i=>d[i]);
import{d as G,f as W,g as Y,h as Z,i as ee,r as j,j as k,Q as te,k as ae,L as d,y as c,w as N,v as oe,e as _,c as l,o as s,l as m,b as n,s as S,J as ne,K as se,F as p,q as L,t as E,B as re,n as w}from"./D_zVjEfm.js";import{_ as le}from"./jBNyKXjo.js";import ie from"./sXr78Ku-.js";import{_ as de}from"./_cPjsZyH.js";import{_ as me}from"./CQvHppDm.js";import ue from"./HHVucgb9.js";import{_ as ce}from"./CF83mHfB.js";import"./G4Mf9Qin.js";const _e={class:"d-flex justify-content-between"},pe={key:0},fe={class:"table-responsive-md"},ye={class:"table table-striped table-hover table-bordered table-fixed"},he={align:"center",width:"90"},ge=["onClick"],ve={valign:"top",title:"Edit",align:"center"},be=["onClick"],xe=G({__name:"index",props:{kind:{default:""}},setup(O){var A;const h=W();Y();const x=Z(),D=ee(),V=j(""),P=k({}),R=k({}),g=O,f=k({data:[]}),v=j([]);j("");const a=k({search:"",searchname:"",searchdatename:"",page:1,sortname:"",sorttype:"",del:[],kind:(A=x.query)==null?void 0:A.idkind});function B(e){a.page=e,u()}const q=async e=>{a.page=1,a.sortname=e,a.sorttype=a.sorttype=="asc"?"desc":"asc",await u()},M=async e=>{a.page=1,a.sortname="",Object.assign(a,e),await u()},U=(e=!1)=>{e&&u()},F=async e=>{f.data.forEach(t=>{t.del=e?t.id:0})},H=async()=>{try{a.del=f.data.filter(t=>t.del!==0).map(t=>t.del).filter(t=>t!=null)||[];let e=await h.post("api/admin/kind/destroy",a);if(e.resultcode=="0")c.toast(e.resultmessage),u();else throw new Error(e.resultmessage)}catch(e){c.formElError(e),console.error(e)}finally{}},u=async()=>{try{h.setLoading(!0);let e=await h.post("api/admin/kind",a);if(e.resultcode=="0")Object.assign(f,e.data);else throw new Error(e.resultmessage)}catch(e){c.formElError(e),console.error(e)}finally{}};te(()=>g.kind,async e=>{a.kind=g.kind,J(),u()},{deep:!0,immediate:!0}),ae(async()=>{});async function J(){try{const e=Object.assign({"/datas/aboutpage.js":()=>d(()=>import("./DEPTJ9I4.js"),__vite__mapDeps([0,1,2]),import.meta.url),"/datas/all.js":()=>d(()=>import("./AII-GxSx.js"),__vite__mapDeps([3,1,2]),import.meta.url),"/datas/banner.js":()=>d(()=>import("./ewbPcaBc.js"),__vite__mapDeps([4,1,2]),import.meta.url),"/datas/case.js":()=>d(()=>import("./DeQTbgbd.js"),__vite__mapDeps([5,1,2]),import.meta.url),"/datas/customer.js":()=>d(()=>import("./MMxUCDix.js"),__vite__mapDeps([6,1,2]),import.meta.url),"/datas/news.js":()=>d(()=>import("./DIRD0ar_.js"),__vite__mapDeps([7,1,2]),import.meta.url),"/datas/productkind.js":()=>d(()=>import("./DZ_RHEX4.js"),__vite__mapDeps([8,1,2]),import.meta.url),"/datas/service.js":()=>d(()=>import("./B2ebk17F.js"),__vite__mapDeps([9,1,2]),import.meta.url),"/datas/serviceitem.js":()=>d(()=>import("./DbPQOmG-.js"),__vite__mapDeps([10,1,2]),import.meta.url)}),t=`/datas/${x.query.kind}.js`;if(e[t])e[t]().then(y=>{v.value=y.fields._rawValue.filter(i=>i.islist==!0),v.value.forEach(i=>{if(i&&i.issearch===!0){let b=i.name;i.kind=="my-dateform"?R[b]=i.label:P[b]=i.label}})}).catch(y=>{throw new Error(y.message)});else throw new Error(`Module ${t} does not exist`)}catch(e){c.formElError(e)}}return(e,t)=>{var $,I;const y=le,i=ie,b=de,K=me,T=ne,Q=ue,X=oe;return N((s(),l("div",null,[m(y,{id:O.kind,refs:"breadcrumb"},null,8,["id"]),m(i,{searchNames:P,searchDateNames:R,onOnSearch:M},null,8,["searchNames","searchDateNames"]),n("div",_e,[[999].includes(($=_(D).adminuser)==null?void 0:$.role)?(s(),l("div",pe,[m(b,{onClick:H})])):S("",!0),t[2]||(t[2]=n("div",null,null,-1)),n("div",null,[m(K,{onClick:t[0]||(t[0]=o=>V.value.open("admin/kind/edit",{...g},0))})])]),n("div",fe,[n("table",ye,[n("thead",null,[n("tr",null,[n("th",he,[N(m(T,{onChange:F},null,512),[[se,[999].includes((I=_(D).adminuser)==null?void 0:I.role)]])]),(s(!0),l(p,null,L(v.value,(o,z)=>(s(),l("th",{width:"",onClick:C=>q(o.name),class:"sortable"},E(o.label),9,ge))),256))])]),n("tbody",null,[(s(!0),l(p,null,L(f.data,(o,z)=>{var C;return s(),l("tr",null,[n("td",ve,[[999].includes((C=_(D).adminuser)==null?void 0:C.role)?(s(),re(T,{key:0,modelValue:o.del,"onUpdate:modelValue":r=>o.del=r,"true-value":o.id},null,8,["modelValue","onUpdate:modelValue","true-value"])):S("",!0),t[4]||(t[4]=w("   ")),n("button",{type:"button",class:"btn btn-success btn-sm",onClick:r=>V.value.open("admin/kind/edit",{...g,edit:o.id},{})},t[3]||(t[3]=[n("i",{class:"fa fa-edit","aria-hidden":"true"},null,-1)]),8,be)]),(s(!0),l(p,null,L(v.value,(r,ke)=>(s(),l("td",null,[r.kind=="my-xmlform"?(s(),l(p,{key:0},[w(E(("utils"in e?e.utils:_(c)).getXmlSearch(r.label,o[r.name])),1)],64)):r.kind=="my-dateform"?(s(),l(p,{key:1},[w(E(("utils"in e?e.utils:_(c)).formatDate(o[r.name])),1)],64)):(s(),l(p,{key:2},[w(E(typeof r.listname<"u"?o[r.listname]:o[r.name]),1)],64))]))),256))])}),256))]),t[5]||(t[5]=n("tbody",null,null,-1))])]),m(Q,{total:f.total,page:a.page,"onUpdate:page":t[1]||(t[1]=o=>a.page=o),onCurrentChange:B},null,8,["total","page"]),m(ce,{ref_key:"myDialog",ref:V,onClosedDialog:U},null,512)])),[[X,_(h).getLoading()]])}}});export{xe as default};
