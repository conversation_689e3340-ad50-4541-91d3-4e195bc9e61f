import{_ as B}from"./jBNyKXjo.js";import{d as L,f as I,i as q,u as x,h as C,r as _,j as v,k as F,Y as M,c as r,o as d,l as N,w as a,v as j,e as c,B as R,m as S,b as s,F as p,q as V,t as l,y as f,I as $}from"./D_zVjEfm.js";const H={class:"card card-success"},O={class:"card-body"},P={class:"table-responsive-md"},Y={class:"table table-striped table-hover table-bordered table-fixed"},z={class:"card card-warning"},A={class:"card-body"},G={class:"form-group row"},J={class:"col-md-10"},K={class:"form-group row"},Q={class:"col-md-10"},T={class:"form-group row"},U={class:"col-md-10"},W={class:"form-group row"},X={class:"col-md-10"},Z={class:"form-group row"},ss={class:"col-md-10"},os={class:"form-group row"},ts={class:"col-md-10"},ls={class:"form-group row"},es={class:"col-md-10"},ds={class:"form-group row"},rs={class:"col-md-10"},as={class:"form-group row"},is={class:"col-md-10"},ns={class:"form-group row"},cs={class:"col-md-10"},ms={align:"center"},ps=L({__name:"show",props:{id:{default:""}},emits:["closed-dialog"],setup(b,{emit:g}){const i=I();q(),x();const w=g,y=b;C(),_(0);const h=_(null),t=v({datas:[]});v({});const D=async()=>{try{let e=await i.post("api/admin/ordergroup/show",{id:y.id});if(e.resultcode=="0")Object.assign(t,e.data);else throw new Error(e.resultmessage)}catch(e){f.alert(e.message),console.error(e)}finally{}};return F(async()=>{D()}),(e,o)=>{const k=B,E=$,m=M("vbcrlf"),u=j;return d(),r(p,null,[N(k,{id:"ordergroup"}),a((d(),R(E,{ref_key:"formEl",ref:h,style:{width:"98%"},model:t},{default:S(()=>[s("div",H,[o[2]||(o[2]=s("div",{class:"card-header"},[s("h3",{class:"card-title"},"訂單明細")],-1)),s("div",O,[s("div",P,[s("table",Y,[o[1]||(o[1]=s("tr",null,[s("th",null,"名稱"),s("th",null,"數量")],-1)),s("tbody",null,[(d(!0),r(p,null,V(t.datas,(n,us)=>(d(),r("tr",null,[s("td",null,l(n.title),1),s("td",null,l(n.qty),1)]))),256))])])])])]),s("div",z,[o[13]||(o[13]=s("div",{class:"card-header"},[s("h3",{class:"card-title"},"基本資料")],-1)),s("div",A,[a((d(),r("div",null,[s("div",G,[o[3]||(o[3]=s("label",{class:"col-md-2"},"訂單編號：",-1)),s("div",J,l(t.ordergroupnumber),1)]),s("div",K,[o[4]||(o[4]=s("label",{class:"col-md-2"},"您的姓名：",-1)),s("div",Q,l(t.rname),1)]),s("div",T,[o[5]||(o[5]=s("label",{class:"col-md-2"},"公司名稱：",-1)),s("div",U,l(t.rcompany),1)]),s("div",W,[o[6]||(o[6]=s("label",{class:"col-md-2"},"您的LINE ID：",-1)),s("div",X,l(t.rline_id),1)]),s("div",Z,[o[7]||(o[7]=s("label",{class:"col-md-2"},"您的手機：",-1)),s("div",ss,l(t.rmobile),1)]),s("div",os,[o[8]||(o[8]=s("label",{class:"col-md-2"},"採購目的：",-1)),s("div",ts,l(t.rpurpose),1)]),s("div",ls,[o[9]||(o[9]=s("label",{class:"col-md-2"},"您的需求：",-1)),s("div",es,[a(s("div",null,null,512),[[m,t.requirements]])])]),s("div",ds,[o[10]||(o[10]=s("label",{class:"col-md-2"},"IP：",-1)),s("div",rs,l(t.clientip),1)]),s("div",as,[o[11]||(o[11]=s("label",{class:"col-md-2"},"備註：",-1)),s("div",is,[a(s("div",null,null,512),[[m,t.rmemo]])])]),s("div",ns,[o[12]||(o[12]=s("label",{class:"col-md-2"},"建立日期：",-1)),s("div",cs,l(("utils"in e?e.utils:c(f)).formatDate(t.created_at,"yyyy-MM-dd hh:mm:ss")),1)])])),[[u,c(i).getLoading()]])])]),s("div",ms,[s("button",{type:"button",class:"btn btn-info",onClick:o[0]||(o[0]=n=>w("closed-dialog",!1))},"返回")])]),_:1},8,["model"])),[[u,c(i).getLoading()]])],64)}}});export{ps as default};
