import{r as e}from"./D_zVjEfm.js";const r=e([{label:"標題",name:"title",kind:"el-input",rules:[{required:!0,message:"標題 未填"}],value:"",isedit:!0,islist:!0,issearch:!0},{label:"本文",name:"body",kind:"my-ckeditor",props:{},rules:[{rows:4,required:!0,message:"本文 未填"}],value:"",isedit:!0,islist:!1,issearch:!0},{label:"開始日期",name:"begindate",kind:"my-dateform",props:{type:"date"},rules:[{required:!1,message:"建立日期 未填"}],value:"",isedit:!0,islist:!0,issearch:!0},{label:"結束日期",name:"closedate",kind:"my-dateform",props:{type:"date"},rules:[{required:!1,message:"建立日期 未填"}],value:"",isedit:!0,islist:!0,issearch:!0},{label:"順序",name:"boardsort",kind:"el-input",props:{type:"number"},rules:[{required:!1,message:"順序 未填"}],value:"",isedit:!0,islist:!0,issearch:!1,memo:"數字大到小"},{label:"建立日期",name:"created_at",kind:"my-dateform",props:{type:"date"},rules:[{required:!1,message:"建立日期 未填"}],value:"",islist:!0,issearch:!0}]);export{r as fields};
