import{_ as C}from"./BWS-stcc.js";import{d as I,f as N,g as V,h as B,i as F,j as m,r as y,k as O,c as a,o as i,l,b as e,w as H,m as R,n as b,p as q,v as A,e as M,F as g,q as k,t as S,s as x,x as P,y as $,z as Q,A as G}from"./D_zVjEfm.js";import{_ as J}from"./4qmtJa-f.js";import{_ as K}from"./I37qKz7r.js";const U={class:"breadcrumb-area"},W={class:"container"},X={class:"row"},Y={class:"col-12"},Z={"aria-label":"breadcrumb"},ee={class:"breadcrumb"},te={class:"breadcrumb-item"},se={class:"about-us-area section-padding-0-70"},oe={class:"container"},ae={class:"row justify-content-between"},ie={class:"col-12 col-lg-12"},ne={class:"row align-items-center justify-content-between"},re={class:"alazea-portfolio-area portfolio-page bg-gray section-padding-70-40"},le={class:"container"},ce={class:"row"},de={class:"col-12"},ue={class:"alazea-benefits-area"},pe={class:"row"},me={class:"wraper"},ge={class:"alazea-portfolio-area portfolio-page section-padding-70-70"},fe={class:"container",style:{position:"relative","min-height":"150px"}},_e={key:1,class:"text-center py-4",style:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)","z-index":"10",width:"100%"}},ke=I({__name:"index",props:{},setup(ve){const d=N();V(),B(),F();const f=m({data:[],total:0}),n=m({data:[],total:0}),_=m({search:"",page:1,sortname:"",sorttype:""}),o=y(!1),c=y(!1),v=()=>typeof window>"u"?!1:typeof window.$>"u"?(console.warn("jQuery 尚未載入"),!1):typeof window.$.fn.slick>"u"?(console.warn("Slick 插件尚未載入"),!1):!0,T=async()=>new Promise(s=>{const t=G(()=>{v()&&(clearInterval(t),c.value=!0,console.log("所有必要腳本已載入完成"),s())},100);setTimeout(()=>{clearInterval(t),c.value||(console.warn("腳本載入超時，但仍嘗試初始化"),c.value=!0,s())},1e4)}),w=async()=>{if(typeof window>"u"){console.warn("非瀏覽器環境，跳過輪播初始化");return}if(c.value||(console.log("等待腳本載入完成..."),await T()),!v()){console.error("腳本載入失敗，跳過輪播初始化"),o.value=!0;return}const s=window.$,t=s(".customer-logos");if(t.length===0){console.warn("客戶輪播元素不存在"),o.value=!0;return}if(t.children().length===0){console.warn("客戶輪播沒有內容"),o.value=!0;return}try{console.log("開始初始化客戶輪播..."),t.hasClass("slick-initialized")&&(console.log("銷毀現有的 Slick 實例"),t.slick("unslick")),t.slick({slidesToShow:6,slidesToScroll:1,autoplay:!0,autoplaySpeed:2e3,arrows:!1,dots:!1,pauseOnHover:!1,infinite:!0,speed:500,cssEase:"linear",responsive:[{breakpoint:1024,settings:{slidesToShow:4}},{breakpoint:768,settings:{slidesToShow:3}},{breakpoint:480,settings:{slidesToShow:2}}]}),o.value=!0,console.log("客戶輪播初始化成功")}catch(u){console.error("Slick 輪播初始化失敗:",u),o.value=!0}},E=async()=>{try{let s=await d.post("api/service",_);if(s.resultcode=="0")Object.assign(f,s.data);else throw new Error(s.resultmessage)}catch(s){$.formElError(s),console.error(s)}finally{}},j=async()=>{try{o.value=!1;let s=await d.post("api/customer",_);if(s.resultcode=="0")Object.assign(n,s.data),console.log("客戶資料載入成功:",n.data.length,"筆"),await Q(),n.data.length>0?setTimeout(async()=>{await w()},500):o.value=!0;else throw new Error(s.resultmessage)}catch(s){$.formElError(s),console.error("載入客戶資料失敗:",s),o.value=!0}};return O(async()=>{await E(),await j(),typeof window<"u"&&(document.readyState==="complete"?console.log("頁面已完成載入"):window.addEventListener("load",()=>{console.log("頁面載入完成事件觸發"),n.data.length>0&&!o.value&&setTimeout(async()=>{await w()},1e3)}))}),(s,t)=>{const u=C,z=q,L=J,h=K,D=A;return i(),a(g,null,[l(u,{title:"關於我們",description:"我們提供專業的服務項目",defaultTitle:""}),e("div",U,[t[2]||(t[2]=e("div",{class:"top-breadcrumb-area bg-img bg-overlay d-flex align-items-center justify-content-center",style:{"background-image":"url(/img/bg-img/about2_bg.jpg)"}},[e("h2",null,"服務項目")],-1)),e("div",W,[e("div",X,[e("div",Y,[e("nav",Z,[e("ol",ee,[e("li",te,[l(z,{to:{path:"/"}},{default:R(()=>t[0]||(t[0]=[b(" 首頁")])),_:1})]),t[1]||(t[1]=e("li",{class:"breadcrumb-item active"},"服務項目",-1))])])])])])]),e("section",se,[e("div",oe,[e("div",ae,[e("div",ie,[t[3]||(t[3]=e("div",{class:"section-heading2 text-center"},[e("h2",null,"關於歐悅設計"),e("p",null,[e("span",{style:{color:"#fd585c"}},"/"),b(" 我們不只創造空間，更創造品牌價值。 "),e("span",{style:{color:"#fd585c"}},"/")])],-1)),e("div",ne,[l(L,{id:"about"})])])])])]),H((i(),a("section",re,[e("div",le,[e("div",ce,[e("div",de,[e("div",ue,[e("div",pe,[e("ul",me,[(i(!0),a(g,null,k(f.data,(r,p)=>(i(),a("li",{key:p,class:"single-services-area col-12 col-sm-3"},[l(h,{width:"200",height:"150",src:r.img?`/images/service/${r.img}`:"/images/no-picture.gif",noimage:"/images/no-picture.gif"},null,8,["src"]),e("h5",null,S(r.title),1),e("p",null,S(r.memo),1)]))),128))])])])])])])])),[[D,M(d).getLoading()]]),e("section",ge,[t[5]||(t[5]=e("div",{class:"section-heading2 text-center"},[e("h2",null,"我們的客戶")],-1)),e("div",fe,[n.data.length>0?(i(),a("div",{key:0,class:"customer-logos slider",style:P({opacity:o.value?1:0,transition:"opacity 0.3s ease"})},[(i(!0),a(g,null,k(n.data,(r,p)=>(i(),a("div",{class:"slide",key:p},[l(h,{width:"150",height:"100",src:`/images/customer/${r.img}`,alt:r.title,noimage:"/images/no-picture.gif"},null,8,["src","alt"])]))),128))],4)):x("",!0),n.data.length>0&&!o.value?(i(),a("div",_e,t[4]||(t[4]=[e("div",{class:"spinner-border text-primary",role:"status"},[e("span",{class:"sr-only"},"載入中...")],-1),e("p",{class:"mt-2"},"正在初始化客戶展示...",-1)]))):x("",!0)])])],64)}}});export{ke as default};
