import{d as v,r,a7 as p,y as n,k as _,c as l,o as u,F as I,s as m,a9 as y,P as S}from"./D_zVjEfm.js";const k=["src"],A={key:1},P=v({__name:"my-img",props:{src:{type:String,required:!0},noimage:{type:String,required:!1}},setup(c){const e=c,s=r(null),a=r(!0),o=r(!0),t=r(!0),f=p(()=>a.value?n.getConfig("API_BASE")+e.src:e.noimage?n.getConfig("API_BASE")+e.noimage:""),d=()=>{o.value=!1,a.value?(a.value=!1,e.noimage||(t.value=!1)):t.value=!1};return _(()=>{s.value&&(s.value.src=n.getConfig("API_BASE")+e.src,a.value=!0)}),(i,g)=>(u(),l(I,null,[t.value?(u(),l("img",S({key:0,src:f.value},i.$attrs,{onError:d,ref_key:"imageRef",ref:s,onLoad:g[0]||(g[0]=B=>o.value=!1)}),null,16,k)):m("",!0),o.value?(u(),l("div",A,"loading...")):m("",!0),y(i.$slots,"default")],64))}});export{P as _};
