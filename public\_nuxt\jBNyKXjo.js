import{d as _,r as f,i as b,Q as y,k,y as v,B as n,o as l,m as t,l as d,s as i,aa as B,n as u,t as S,a9 as g,ab as x}from"./D_zVjEfm.js";const E=_({__name:"my-breadcrumb",props:{id:String,title:String||""},setup(m){const o=m,a=f(""),c=b();return y(()=>o.id,async r=>{try{a.value=c.adminuser.navs[r]}catch{}},{deep:!0,immediate:!0}),k(async()=>{v.isEmpty(o.title)||(a.value=o.title)}),(r,e)=>{const s=B,p=x;return l(),n(p,{separator:"/",style:{"padding-bottom":"10px"}},{default:t(()=>[d(s,null,{default:t(()=>e[0]||(e[0]=[u("位置 ")])),_:1}),d(s,null,{default:t(()=>e[1]||(e[1]=[u("首頁")])),_:1}),a.value!=""?(l(),n(s,{key:0},{default:t(()=>[u(S(a.value),1)]),_:1})):i("",!0),r.$slots.default?(l(),n(s,{key:1},{default:t(()=>[g(r.$slots,"default")]),_:3})):i("",!0)]),_:3})}}});export{E as _};
