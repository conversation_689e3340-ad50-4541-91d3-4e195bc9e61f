const template = "<div class=\"loader\"></div>\r\n<style>\r\n    :root {\r\n        --loading-color-0: #4285F4;    /* 0% 藍色 */\r\n        --loading-color-25: #DB4437;   /* 25% 紅色 */\r\n        --loading-color-50: #F4B400;   /* 50% 黃色 */\r\n        --loading-color-100: #0F9D58;  /* 100% 綠色 */\r\n        --loading-size: 80px;\r\n        --loading-border-width: 6px;\r\n        --loading-speed: 1.5s;\r\n    }\r\n\r\n    .loader {\r\n        position: fixed;\r\n        top: calc(50% - var(--loading-size) / 2);\r\n        left: calc(50% - var(--loading-size) / 2);\r\n        z-index: 1031;\r\n        width: var(--loading-size);\r\n        height: var(--loading-size);\r\n        box-sizing: border-box;\r\n        border: solid var(--loading-border-width) transparent;\r\n        border-radius: 50%;\r\n        -webkit-animation: loading var(--loading-speed) linear infinite;\r\n        animation: loading var(--loading-speed) linear infinite;\r\n    }\r\n\r\n    @-webkit-keyframes loading {\r\n        0% {\r\n            -webkit-transform: rotate(0deg);\r\n            transform: rotate(0deg);\r\n            border-top-color: var(--loading-color-0);\r\n            border-left-color: var(--loading-color-0);\r\n            border-bottom-color: var(--loading-color-0);\r\n        }\r\n        25% {\r\n            border-top-color: var(--loading-color-25);\r\n            border-left-color: var(--loading-color-25);\r\n            border-bottom-color: var(--loading-color-25);\r\n        }\r\n        50% {\r\n            border-top-color: var(--loading-color-50);\r\n            border-left-color: var(--loading-color-50);\r\n            border-bottom-color: var(--loading-color-50);\r\n        }\r\n        100% {\r\n            -webkit-transform: rotate(360deg);\r\n            transform: rotate(360deg);\r\n            border-top-color: var(--loading-color-100);\r\n            border-left-color: var(--loading-color-100);\r\n            border-bottom-color: var(--loading-color-100);\r\n        }\r\n    }\r\n\r\n    @keyframes loading {\r\n        0% {\r\n            transform: rotate(0deg);\r\n            border-top-color: var(--loading-color-0);\r\n            border-left-color: var(--loading-color-0);\r\n            border-bottom-color: var(--loading-color-0);\r\n        }\r\n        25% {\r\n            border-top-color: var(--loading-color-25);\r\n            border-left-color: var(--loading-color-25);\r\n            border-bottom-color: var(--loading-color-25);\r\n        }\r\n        50% {\r\n            border-top-color: var(--loading-color-50);\r\n            border-left-color: var(--loading-color-50);\r\n            border-bottom-color: var(--loading-color-50);\r\n        }\r\n        100% {\r\n            transform: rotate(360deg);\r\n            border-top-color: var(--loading-color-100);\r\n            border-left-color: var(--loading-color-100);\r\n            border-bottom-color: var(--loading-color-100);\r\n        }\r\n    }\r\n</style>";

export { template };
//# sourceMappingURL=_virtual_spa-template.mjs.map
