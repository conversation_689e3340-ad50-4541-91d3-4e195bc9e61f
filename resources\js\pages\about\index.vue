<template>
    <my-seo :title="'關於我們'" :description="'我們提供專業的服務項目'" defaultTitle=""> </my-seo>
    <div class="breadcrumb-area">
        <!-- Top Breadcrumb Area -->
        <div
            class="top-breadcrumb-area bg-img bg-overlay d-flex align-items-center justify-content-center"
            style="background-image: url(/img/bg-img/about2_bg.jpg)"
        >
            <h2>服務項目</h2>
        </div>

        <div class="container">
            <div class="row">
                <div class="col-12">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><NuxtLink :to="{ path: '/' }"> 首頁</NuxtLink></li>
                            <li class="breadcrumb-item active">服務項目</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
    <section class="about-us-area section-padding-0-70">
        <div class="container">
            <div class="row justify-content-between">
                <div class="col-12 col-lg-12">
                    <!-- Section Heading -->
                    <div class="section-heading2 text-center">
                        <h2>關於歐悅設計</h2>
                        <p>
                            <span style="color: #fd585c">/</span> 我們不只創造空間，更創造品牌價值。 <span style="color: #fd585c">/</span>
                        </p>
                    </div>

                    <div class="row align-items-center justify-content-between">
                        <my-epost id="about"></my-epost>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="alazea-portfolio-area portfolio-page bg-gray section-padding-70-40" v-loading="http.getLoading()">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="alazea-benefits-area">
                        <div class="row">
                            <ul class="wraper">
                                <!-- 使用 v-for 遍歷服務資料 -->
                                <li v-for="(rs, index) in data_service.data" :key="index" class="single-services-area col-12 col-sm-3">
                                    <!-- 顯示服務圖片 -->
                                    <my-img
                                        width="200"
                                        height="150"
                                        :src="rs.img ? `/images/service/${rs.img}` : '/images/no-picture.gif'"
                                        noimage="/images/no-picture.gif"
                                    />

                                    <!-- 顯示服務標題 -->
                                    <h5>{{ rs.title }}</h5>

                                    <!-- 顯示服務描述 -->
                                    <p>{{ rs.memo }}</p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="alazea-portfolio-area portfolio-page section-padding-70-70">
        <div class="section-heading2 text-center">
            <h2>我們的客戶</h2>
        </div>

        <div class="container" style="position: relative; min-height: 150px">
            <!-- Vue 客戶輪播組件 -->
            <div class="vue-carousel-container" v-if="customerData.data.length > 0">
                <div class="vue-carousel-track" :style="carouselTrackStyle" @mouseenter="pauseAutoplay" @mouseleave="resumeAutoplay">
                    <!-- 客戶項目 - 為了無縫循環，複製一份資料 -->
                    <div class="vue-carousel-item" v-for="(rs, index) in duplicatedCustomerData" :key="`customer-${index}`">
                        <my-img
                            width="126"
                            height="88"
                            :src="`/images/customer/${rs.img}`"
                            :alt="rs.title"
                            noimage="/images/no-picture.gif"
                        />
                    </div>
                </div>
            </div>

            <!-- 載入中提示 -->
            <div
                v-if="customerData.data.length === 0"
                class="text-center py-4"
                style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 10; width: 100%"
            >
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">載入中...</span>
                </div>
                <p class="mt-2">正在載入客戶資料...</p>
            </div>
        </div>
    </section>
</template>
<script setup lang="ts">
import { computed, nextTick, onMounted, onUnmounted, reactive, ref } from 'vue'

const http = createHttp() // http套件
const router = useRouter()
const route = useRoute()
const store = useDataStore()

const props = defineProps({}) // 接受外面送來的參數

// 使用 reactive 定義對象
const data_service = reactive({
    data: [] as any[], // 服務資料陣列
    total: 0 // 總筆數
})

// customer 資料物件
const customerData = reactive({
    data: [] as customer[], // 客戶資料陣列
    total: 0 // 總筆數
})

const inputs = reactive({
    search: '', // 搜尋關鍵字
    page: 1, // 目前頁數
    sortname: '', // 排序欄位
    sorttype: '' // 排序方式
})

// Vue 輪播相關響應式資料
const currentTranslateX = ref(0) // 當前偏移量
const autoplayTimer = ref<NodeJS.Timeout | null>(null) // 自動播放計時器
const isAutoplayPaused = ref(false) // 是否暫停自動播放
const itemWidth = ref(126) // 每個項目的寬度
const visibleItems = ref(6) // 可見項目數量
const animationSpeed = ref(500) // 動畫速度(ms)
const autoplayInterval = ref(2000) // 自動播放間隔(ms)

// 計算屬性：為了無縫循環而複製的客戶資料
const duplicatedCustomerData = computed(() => {
    if (customerData.data.length === 0) return []

    // 複製兩份資料以實現無縫循環
    return [...customerData.data, ...customerData.data]
})

// 計算屬性：輪播軌道的樣式
const carouselTrackStyle = computed(() => {
    return {
        transform: `translateX(${currentTranslateX.value}px)`,
        transition: `transform ${animationSpeed.value}ms ease-in-out`,
        display: 'flex',
        width: `${duplicatedCustomerData.value.length * itemWidth.value}px`
    }
})

// 響應式設計：根據螢幕尺寸調整可見項目數量
const updateVisibleItems = () => {
    if (typeof window === 'undefined') return

    const width = window.innerWidth
    if (width <= 480) {
        visibleItems.value = 2 // 小手機
        itemWidth.value = 100
    } else if (width <= 768) {
        visibleItems.value = 3 // 手機
        itemWidth.value = 110
    } else if (width <= 1024) {
        visibleItems.value = 4 // 平板
        itemWidth.value = 118
    } else {
        visibleItems.value = 6 // 桌面
        itemWidth.value = 126
    }
}

// 移動到下一個項目
const moveToNext = () => {
    if (customerData.data.length === 0) return

    // 計算下一個位置
    const nextTranslate = currentTranslateX.value - itemWidth.value
    const maxTranslate = -customerData.data.length * itemWidth.value

    if (nextTranslate <= maxTranslate) {
        // 如果超出範圍，重置到開始位置（無縫循環）
        currentTranslateX.value = 0
    } else {
        currentTranslateX.value = nextTranslate
    }
}

// 開始自動播放
const startAutoplay = () => {
    if (autoplayTimer.value) {
        clearInterval(autoplayTimer.value)
    }

    autoplayTimer.value = setInterval(() => {
        if (!isAutoplayPaused.value) {
            moveToNext()
        }
    }, autoplayInterval.value)
}

// 暫停自動播放
const pauseAutoplay = () => {
    isAutoplayPaused.value = true
}

// 恢復自動播放
const resumeAutoplay = () => {
    isAutoplayPaused.value = false
}

// 停止自動播放
const stopAutoplay = () => {
    if (autoplayTimer.value) {
        clearInterval(autoplayTimer.value)
        autoplayTimer.value = null
    }
}

// 初始化輪播
const initCarousel = () => {
    if (customerData.data.length > 0) {
        updateVisibleItems()
        startAutoplay()
    }
}

// 下載資料
const getData_service = async () => {
    try {
        let rep = (await http.post('api/service', inputs)) as ApiResponse

        if (rep.resultcode == '0') {
            Object.assign(data_service, rep.data)
        } else {
            throw new Error(rep.resultmessage)
        }
    } catch (error) {
        utils.formElError(error)
        console.error(error)
    } finally {
    }
}

// 下載客戶資料
const getData_customer = async () => {
    try {
        let rep = (await http.post('api/customer', inputs)) as ApiResponse

        if (rep.resultcode == '0') {
            Object.assign(customerData, rep.data)
            console.log('客戶資料載入成功:', customerData.data.length, '筆')

            // 資料載入完成後初始化輪播
            await nextTick()
            initCarousel()
        } else {
            throw new Error(rep.resultmessage)
        }
    } catch (error) {
        utils.formElError(error)
        console.error('載入客戶資料失敗:', error)
    }
}

// 監聽視窗大小變化
const handleResize = () => {
    updateVisibleItems()
}

onMounted(async () => {
    // 載入數據
    await getData_service()
    await getData_customer()

    // 監聽視窗大小變化
    if (typeof window !== 'undefined') {
        window.addEventListener('resize', handleResize)
    }
})

onUnmounted(() => {
    // 清理計時器和事件監聽器
    stopAutoplay()
    if (typeof window !== 'undefined') {
        window.removeEventListener('resize', handleResize)
    }
})
</script>

<style lang="scss">
// Vue 客戶輪播樣式
.vue-carousel-container {
    overflow: hidden;
    width: 100%;
    position: relative;

    .vue-carousel-track {
        display: flex;
        align-items: center;
        will-change: transform; // 優化動畫性能

        .vue-carousel-item {
            flex-shrink: 0;
            padding: 10px;
            text-align: center;
            box-sizing: border-box;

            img {
                max-width: 100%;
                height: auto;
                object-fit: contain;
                opacity: 0.7;
                transition: opacity 0.3s ease;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

                &:hover {
                    opacity: 1;
                    transform: scale(1.05);
                    transition: all 0.3s ease;
                }
            }
        }
    }
}

// 響應式設計
@media (max-width: 1024px) {
    .vue-carousel-container .vue-carousel-track .vue-carousel-item {
        padding: 8px;
    }
}

@media (max-width: 768px) {
    .vue-carousel-container .vue-carousel-track .vue-carousel-item {
        padding: 6px;

        img {
            max-height: 70px;
        }
    }
}

@media (max-width: 480px) {
    .vue-carousel-container .vue-carousel-track .vue-carousel-item {
        padding: 4px;

        img {
            max-height: 60px;
        }
    }
}
</style>
