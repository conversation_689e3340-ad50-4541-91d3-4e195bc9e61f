import{d as R,i as B,f as L,g as z,h as F,r as D,j as T,Z as $,k as H,y,c as u,o as c,b as e,s as f,w as M,l as o,m as d,n as p,p as Z,v as G,e as g,$ as J,a0 as K,F as x,q as N,t as q,a1 as O,a2 as P,E as Q,C as W,H as X,I as Y}from"./D_zVjEfm.js";import{u as tt}from"./CKhpvNlx.js";const et={class:"breadcrumb-area"},st={class:"container"},ot={class:"row"},at={class:"col-12"},lt={"aria-label":"breadcrumb"},rt={class:"breadcrumb"},nt={class:"breadcrumb-item"},dt={class:"breadcrumb-item"},it={key:0,class:"container"},ut={class:"row"},ct={class:"col-12 mb-20 font14"},mt={class:"inquiryBtn"},pt={class:"container-fluid"},_t={class:"cart-area section-padding-0-100 clearfix"},ft={class:"container vt-html"},vt={class:"row vt-col-loop vt-no-resize"},bt={class:"col-12"},yt={key:0},gt={class:"table-responsive-md"},ht={class:"table table-striped table-hover table-bordered table-fixed"},Vt=["onClick"],wt={key:0,class:"emptyCart"},Et={class:"inquiryBtn"},kt={class:"col-12 center"},xt={key:1},qt={class:"row vt-col-loop vt-no-resize"},At={class:"col-12"},Dt={class:"table-responsive-md"},Nt={class:"table table-striped table-hover table-bordered table-fixed"},Ct={class:"col-12"},It={class:"contact-form-area mt-70"},Ut={class:"row"},jt={class:"col-12 col-md-6"},St={class:"col-12 col-md-6"},Rt={class:"col-12 col-md-6"},Bt={class:"col-12 col-md-6"},Lt={class:"col-12 col-md-6"},zt={class:"col-12 col-md-6"},Ft={class:"col-12"},Tt={key:2},$t={ref:"recaptchaRef"},Gt=R({__name:"index",setup(Ht){const{getRecaptchaToken:C}=tt(),r=B(),V=L();z(),F();let _=D(0);const w=D(),s=T({id:0,ordergroupnumber:"",rname:"",rcompany:"",rline_id:"",rmobile:"",remail:"",rpurpose:"",requirements:"",clientip:"",rmemo:"",datas:[],google_recaptcha_token:""});$(async()=>{s.datas&&Array.isArray(s.datas)&&s.datas.length>0&&r.data&&typeof r.data=="object"&&(r.data.cart=s.datas)});const I=async()=>{if(!w.value){console.warn("表單元素不存在");return}try{if(await w.value.validate())try{s.google_recaptcha_token=await C("submit");const t=await V.post("api/ordergroup/store",s);if(t.resultcode==="0")_.value=2,r.data&&typeof r.data=="object"&&y.mapClear(r.data);else throw new Error(t.resultmessage||"提交失敗")}catch(t){console.error("提交表單時發生錯誤:",t),y.formElError(t.message||"提交表單時發生錯誤")}}catch(n){console.error("表單驗證失敗:",n),y.formElError("表單驗證失敗，請檢查必填欄位")}},U=n=>{try{if(r.data&&typeof r.data=="object"){const t=r.data.cart||[];Array.isArray(t)&&n>=0&&n<t.length&&(y.onDel(t,n),r.data.cart=t,s.datas=[...t])}}catch(t){console.error("刪除項目時發生錯誤:",t),y.formElError("刪除項目時發生錯誤")}},j=async()=>{var n,t;try{const i=(n=r.data)==null?void 0:n.cart;if(!i||!Array.isArray(i)||i.length===0){console.info("購物車為空或無效"),s.datas=[];return}const v=i.map(l=>l==null?void 0:l.id).filter(l=>l&&Number.isInteger(Number(l))).map(l=>Number(l));if(v.length===0){console.warn("購物車中無有效的商品 ID"),s.datas=[];return}const E=v.join(","),b=await V.post("api/cart",{id:E});if(b.resultcode=="0"&&((t=b.data)!=null&&t.data))s.datas=b.data.data,s.datas&&Array.isArray(s.datas)&&s.datas.forEach(l=>{const m=i.find(k=>k.id===l.id);l.qty=(m==null?void 0:m.qty)||1});else throw new Error(b.resultmessage||"載入購物車資料失敗")}catch(i){y.formElError(i.message||"載入購物車資料時發生錯誤"),s.datas=[]}};return H(async()=>{var n;try{r.data.cart||(r.data.cart=[]);const t=(n=r.data)==null?void 0:n.cart;t&&Array.isArray(t)&&t.length>0?(console.log("購物車中的資料:",t),s.datas=[...t],await j()):(console.log("購物車為空，初始化為空陣列"),s.datas=[])}catch(t){console.error("初始化時發生錯誤:",t),s.datas=[]}}),(n,t)=>{const i=Z,v=J,E=K,b=O,l=W,m=Q,k=Y,S=G;return c(),u(x,null,[e("div",et,[t[11]||(t[11]=e("div",{class:"top-breadcrumb-area bg-img bg-overlay d-flex align-items-center justify-content-center",style:{"background-image":"url(/img/bg-img/products_bg.jpg)"}},[e("h2",null,"熱銷產品")],-1)),e("div",st,[e("div",ot,[e("div",at,[e("nav",lt,[e("ol",rt,[e("li",nt,[o(i,{to:"/"},{default:d(()=>t[8]||(t[8]=[p(" 首頁")])),_:1})]),e("li",dt,[o(i,{to:"/product"},{default:d(()=>t[9]||(t[9]=[p("熱銷產品")])),_:1})]),t[10]||(t[10]=e("li",{class:"breadcrumb-item active"},"我的詢價清單",-1))])])])])])]),!s.datas||s.datas.length===0?(c(),u("div",it,[e("div",ut,[e("div",ct,[t[13]||(t[13]=p(" 請至我們的")),e("span",mt,[o(i,{to:{path:"/product",query:{}}},{default:d(()=>t[12]||(t[12]=[p(" 熱銷產品總覽頁 ")])),_:1})]),t[14]||(t[14]=p('，按下"')),t[15]||(t[15]=e("span",{class:"redTxt"},"加入詢價",-1)),t[16]||(t[16]=p('"按鈕複選您想詢價的產品，下一步點選"')),t[17]||(t[17]=e("span",{class:"redTxt"},"我的詢價清單",-1)),t[18]||(t[18]=p('"按鈕後，於詢價清單頁下方填寫資料取得報價，提交表單後，我們將會盡快提供報價給您。 '))])])])):f("",!0),M((c(),u("div",pt,[e("div",_t,[e("div",ft,[e("div",vt,[e("div",bt,[o(E,{style:{width:"100%"},active:g(_),"finish-status":"success"},{default:d(()=>[o(v,{title:"詢價清單"}),o(v,{title:"資料填寫"}),o(v,{title:"送出詢價表單"})]),_:1},8,["active"]),t[28]||(t[28]=e("p",null,null,-1)),g(_)==0?(c(),u("div",yt,[e("div",gt,[e("table",ht,[t[20]||(t[20]=e("thead",null,[e("tr",null,[e("th",null,"名稱"),e("th",null,"數量")])],-1)),e("tbody",null,[s.datas&&s.datas.length>0?(c(!0),u(x,{key:0},N(s.datas,(a,h)=>(c(),u("tr",{key:a.id||h},[e("td",null,q(a.title),1),e("td",null,[o(b,{min:1,modelValue:a.qty,"onUpdate:modelValue":A=>a.qty=A,step:1},null,8,["modelValue","onUpdate:modelValue"])]),e("td",null,[e("a",{href:"javascript:void(0)",onClick:A=>U(h),class:"text-danger"},t[19]||(t[19]=[e("i",{class:"fas fa-times"},null,-1)]),8,Vt)])]))),128)):f("",!0)])]),!s.datas||s.datas.length===0?(c(),u("div",wt,[t[22]||(t[22]=p(" 詢價車裡目前沒有產品， ")),e("span",Et,[o(i,{to:{path:"/product",query:{}}},{default:d(()=>t[21]||(t[21]=[p(" 立即詢價 > ")])),_:1})])])):f("",!0)]),e("div",kt,[s.datas&&s.datas.length>0?(c(),u("button",{key:0,onClick:t[0]||(t[0]=a=>P(_)?_.value=1:_=1),type:"button",class:"btn alazea-btn mt-15"}," 下一步 ")):f("",!0)])])):f("",!0),g(_)==1?(c(),u("div",xt,[o(k,{ref_key:"formEl",ref:w,model:s,onSubmit:X(I,["prevent"])},{default:d(()=>[e("div",qt,[e("div",At,[e("div",Dt,[e("table",Nt,[t[23]||(t[23]=e("thead",null,[e("tr",null,[e("th",null,"名稱"),e("th",null,"數量")])],-1)),e("tbody",null,[(c(!0),u(x,null,N(s.datas||[],(a,h)=>(c(),u("tr",{key:a.id||h},[e("td",null,q(a.title),1),e("td",null,q(a.qty),1)]))),128))])])]),t[26]||(t[26]=e("h2",null,"詢價表單",-1)),e("div",Ct,[e("div",It,[t[25]||(t[25]=e("h4",{class:"title mb-20 text-center"},"詢價表單",-1)),e("div",Ut,[e("div",jt,[o(m,{prop:"rname",rules:[{required:!0,message:"您的姓名 未填"}]},{default:d(()=>[o(l,{modelValue:s.rname,"onUpdate:modelValue":t[1]||(t[1]=a=>s.rname=a),type:"text",placeholder:"您的姓名"},null,8,["modelValue"])]),_:1})]),e("div",St,[o(m,{prop:"rcompany"},{default:d(()=>[o(l,{modelValue:s.rcompany,"onUpdate:modelValue":t[2]||(t[2]=a=>s.rcompany=a),type:"text",placeholder:"公司名稱"},null,8,["modelValue"])]),_:1})]),e("div",Rt,[o(m,{prop:"rline_id"},{default:d(()=>[o(l,{modelValue:s.rline_id,"onUpdate:modelValue":t[3]||(t[3]=a=>s.rline_id=a),type:"text",placeholder:"您的LINE ID"},null,8,["modelValue"])]),_:1})]),e("div",Bt,[o(m,{prop:"rmobile"},{default:d(()=>[o(l,{modelValue:s.rmobile,"onUpdate:modelValue":t[4]||(t[4]=a=>s.rmobile=a),type:"text",placeholder:"您的手機"},null,8,["modelValue"])]),_:1})]),e("div",Lt,[o(m,{prop:"remail",rules:[{required:!0,message:"您的信箱 未填",type:"email"}]},{default:d(()=>[o(l,{modelValue:s.remail,"onUpdate:modelValue":t[5]||(t[5]=a=>s.remail=a),type:"email",placeholder:"您的信箱"},null,8,["modelValue"])]),_:1})]),e("div",zt,[o(m,{prop:"rpurpose"},{default:d(()=>[o(l,{modelValue:s.rpurpose,"onUpdate:modelValue":t[6]||(t[6]=a=>s.rpurpose=a),type:"text",placeholder:"採購目的"},null,8,["modelValue"])]),_:1})]),e("div",Ft,[o(m,{prop:"requirements"},{default:d(()=>[o(l,{modelValue:s.requirements,"onUpdate:modelValue":t[7]||(t[7]=a=>s.requirements=a),type:"textarea",rows:5,placeholder:"您的需求"},null,8,["modelValue"])]),_:1})]),t[24]||(t[24]=e("div",{class:"col-12 center"},[e("button",{type:"submit",class:"btn alazea-btn mt-15"}," 取得報價 ")],-1))])])])])])]),_:1},8,["model"])])):f("",!0),g(_)==2?(c(),u("div",Tt,t[27]||(t[27]=[e("div",{class:"container"},[e("div",{class:"text-center"},[e("h1",null,"送出成功")])],-1)]))):f("",!0)])])])])])),[[S,g(V).getLoading()]]),e("div",$t,null,512)],64)}}});export{Gt as default};
