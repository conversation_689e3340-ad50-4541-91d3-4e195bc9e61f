<template>
    <div>
        <!-- 麵包屑區域 -->
        <div class="breadcrumb-area">
            <!-- Top Breadcrumb Area -->
            <div
                class="top-breadcrumb-area bg-img bg-overlay d-flex align-items-center justify-content-center"
                style="background-image: url(/img/bg-img/cases_bg.jpg)"
            >
                <h2>精選案例</h2>
            </div>

            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <NuxtLink :to="{ path: '/' }">首頁</NuxtLink>
                                </li>
                                <li class="breadcrumb-item active">精選案例</li>
                            </ol>
                        </nav>
                    </div>
                </div>
            </div>
        </div>

        <!-- 案例展示區域 -->
        <section class="alazea-portfolio-area portfolio-page section-padding-0-100">
            <div class="container" v-loading="http.getLoading()">
                <div class="row alazea-portfolio">
                    <!-- Single Portfolio Area -->
                    <div v-for="(rs, index) in data.data" :key="index" class="col-12 col-sm-6 col-lg-3 single_portfolio_item design">
                        <!-- Portfolio Thumbnail -->
                        <div
                            class="portfolio-thumbnail bg-img"
                            :style="`background-image: url(/images/case/${rs.img.split(',')[0]});`"
                        ></div>
                        <div class="portfolio-hover-overlay">
                            <a
                                v-for="(img, imgIndex) in rs.img.split(',')"
                                :key="imgIndex"
                                :href="`/images/case/${img}`"
                                :class="`portfolio-img0${imgIndex + 1} d-flex align-items-center justify-content-center`"
                                :title="rs.title"
                            >
                                <div v-if="imgIndex === 0" class="port-hover-text">
                                    <h3>{{ rs.title }}</h3>
                                    <h5>{{ rs.body }}</h5>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
                <!-- 分頁功能 -->
                <div class="row">
                    <div class="col-12">
                        <my-pagination :total.number="data.total" v-model:page="inputs.page" @current-change="onPageChange"></my-pagination>
                    </div>
                </div>
            </div>
        </section>
    </div>
</template>

<script setup lang="ts">
import { nextTick, onMounted, reactive, ref, watch } from 'vue'

const http = createHttp() //http套件
const router = useRouter()
const route = useRoute()
const store = useDataStore()
const myDialog = ref('')

//const emits = defineEmits(["close"]); //接受外面送來的觸發的參數
const props = defineProps({}) //接受外面送來的參數

// 使用 reactive 定義對象
const data = reactive({
    data: [] as any[], // 案例資料陣列
    total: 0 // 總筆數
})
const edit = ref('')
// interface Irepo {
//     resultcode: string
//     resultmessage: string
//     data: any
// }

const inputs = reactive({
    search: '',
    page: 1,
    sortname: '',
    sorttype: ''
})

// 初始化燈箱效果和佈局的函數
const initMagnificPopup = () => {
    nextTick(() => {
        // 確保 jQuery 和相關插件已經載入
        const $ = (window as any).$
        if ($ && $.fn.magnificPopup) {
            // 初始化 Isotope 佈局（如果可用）
            if ($.fn.imagesLoaded && $.fn.isotope) {
                $('.alazea-portfolio').imagesLoaded(function () {
                    $('.alazea-portfolio').isotope({
                        itemSelector: '.single_portfolio_item',
                        percentPosition: true,
                        masonry: {
                            columnWidth: '.single_portfolio_item'
                        }
                    })
                })
            }

            // 為每個案例的所有圖片初始化為一個 gallery 組
            data.data.forEach((rs, caseIndex) => {
                // 選擇該案例中的所有 portfolio-img 元素
                $(`.single_portfolio_item:eq(${caseIndex}) [class*="portfolio-img"]`).magnificPopup({
                    gallery: {
                        enabled: true
                    },
                    type: 'image'
                })
            })
            $('.video-icon').magnificPopup({
                type: 'iframe'
            })
        }
    })
}

//分頁事件
function onPageChange(page: number) {
    inputs.page = page
    getData()
}
// API 回應介面定義
interface ApiResponse {
    resultcode: string
    resultmessage: string
    data: {
        data: cases[]
        total: number
    }
}

// 排序事件處理
const onSort = async (column: any): Promise<void> => {
    inputs.sortname = column.prop
    inputs.sorttype = column.order == 'ascending' ? 'desc' : 'asc'
    getData()
}

// 搜尋事件處理
const onSearch = async (searchdatas: any): Promise<void> => {
    inputs.page = 1
    inputs.sortname = ''
    Object.assign(inputs, searchdatas)
    getData()
}

// 下載資料
const getData = async (): Promise<void> => {
    try {
        const rep = (await http.post('api/case', inputs)) as ApiResponse

        if (rep.resultcode == '0') {
            // 將 API 回傳的資料指派給 data 物件
            Object.assign(data, rep.data)
            // 資料載入完成後初始化燈箱效果
            initMagnificPopup()
        } else {
            throw new Error(rep.resultmessage)
        }
    } catch (error) {
        utils.formElError(error)
        console.error(error)
    }
}

// 監聽 data.data 的變化，當數據更新時重新初始化燈箱
watch(
    () => data.data,
    () => {
        initMagnificPopup()
    },
    { deep: true }
)

onMounted(async () => {
    //console.clear();
    //console.clear();
    //console.clear();
    //console.log('lang', lang)
    //console.log('router', router.currentRoute.value.params.lang)
    //console.log('lang', store.lang)

    await getData()
})
</script>
