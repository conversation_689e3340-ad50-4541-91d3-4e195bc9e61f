import{_ as V}from"./jBNyKXjo.js";import{d as x,f as h,u as k,r as c,j as _,k as I,c as B,o as f,l as r,w as F,v as L,e as M,B as C,m as l,b as s,n,t as D,E as j,C as q,D as A,H as N,I as S,F as U,y as u}from"./D_zVjEfm.js";const $={class:"form-group row"},H={class:"col-md-10"},O={class:"form-group row"},P={class:"col-md-10"},R={class:"form-group row"},T={class:"col-md-10"},z={class:"form-group row"},G={class:"col-md-10"},J={align:"center"},Y=x({__name:"edit",props:["edit"],emits:["close-modal"],setup(K,{emit:Q}){const d=h();k();const i=c(null);c([]);const o=_({account:"",name:"",password:"",password_confirmation:"",email:"",role:"",online:"",failcount:""});_({});const g=async()=>{try{let t=await d.post("api/admin/useredit/show",{});if(t.resultcode=="0")Object.assign(o,t.data);else throw new Error(t.resultmessage)}catch(t){u.formElError(t),console.error(t)}finally{}},v=async()=>{if(i.value)try{if(await i.value.validate())try{let e=await d.post("api/admin/useredit/store",o);if(e.resultcode=="0")u.toast(e.resultmessage);else throw new Error(e.resultmessage)}catch(e){u.alert(e.message,"Error","error"),console.error(e)}}catch(t){console.error("Validation error:",t)}};return I(async()=>{g()}),(t,e)=>{const w=V,m=q,p=j,y=A,b=S,E=L;return f(),B(U,null,[r(w,{id:"useredit"}),F((f(),C(b,{ref_key:"formEl",ref:i,model:o,onSubmit:N(v,["prevent"])},{default:l(()=>[s("div",$,[e[4]||(e[4]=s("label",{class:"col-md-2"},[n("帳號"),s("span",{class:"text-danger p-1"})],-1)),s("div",H,D(o.account),1)]),s("div",O,[e[5]||(e[5]=s("label",{class:"col-md-2"},[n("姓名"),s("span",{class:"text-danger p-1"},"*")],-1)),s("div",P,[r(p,{prop:"name",rules:[{required:!0,message:"姓名 未填"}]},{default:l(()=>[r(m,{modelValue:o.name,"onUpdate:modelValue":e[0]||(e[0]=a=>o.name=a),type:"text"},null,8,["modelValue"])]),_:1})])]),s("div",R,[e[6]||(e[6]=s("label",{class:"col-md-2"},[n("密碼"),s("span",{class:"text-danger p-1"})],-1)),s("div",T,[r(p,{prop:"password",rules:[{required:!1,message:"密碼 未填"},{min:8,max:20,message:"長度在 8 到 20 个字符",trigger:"blur"}]},{default:l(()=>[r(y,{placement:"top-start",title:"密碼規格",width:400,trigger:"hover",content:"密碼長度必須為8~20位, 其中必須包含至少一位數字、一位英文，若需有特殊符號僅限於 ! @ # $ % & *"},{reference:l(()=>[r(m,{modelValue:o.password,"onUpdate:modelValue":e[1]||(e[1]=a=>o.password=a),"show-password":"",type:"password",placeholder:"密碼"},null,8,["modelValue"])]),_:1})]),_:1})])]),s("div",z,[e[7]||(e[7]=s("label",{class:"col-md-2"},[n("EMAIL"),s("span",{class:"text-danger p-1"})],-1)),s("div",G,[r(p,{prop:"email",rules:[{required:!1,message:"EMAIL 未填"},{type:"email",message:"EMAIL 格式錯誤"}]},{default:l(()=>[r(m,{modelValue:o.email,"onUpdate:modelValue":e[2]||(e[2]=a=>o.email=a),type:"email",placeholder:"ex <EMAIL>"},null,8,["modelValue"])]),_:1})])]),s("div",J,[e[8]||(e[8]=s("button",{type:"submit",class:"btn btn-primary"},"確定",-1)),e[9]||(e[9]=n("   ")),s("button",{type:"reset",class:"btn btn-secondary",onClick:e[3]||(e[3]=a=>i.value.resetFields())},"取消")])]),_:1},8,["model"])),[[E,M(d).getLoading()]])],64)}}});export{Y as default};
