{"compilerOptions": {"forceConsistentCasingInFileNames": true, "strict": true, "noEmit": true, "target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "allowJs": true, "resolveJsonModule": true, "jsx": "preserve", "allowSyntheticDefaultImports": true, "jsxFactory": "h", "jsxFragmentFactory": "Fragment", "paths": {"#imports": ["./types/nitro-imports"], "~/*": ["../*", "../*"], "@/*": ["../*", "../*"], "~~/*": ["../*", "../*"], "@@/*": ["../*", "../*"], "nitropack/types": ["../../../../../nuxt/node_modules/nitropack/types"], "nitropack": ["../../../../../nuxt/node_modules/nitropack"], "defu": ["../../../../../nuxt/node_modules/defu"], "h3": ["../../../../../nuxt/node_modules/h3"], "consola": ["../../../../../nuxt/node_modules/consola"], "ofetch": ["../../../../../nuxt/node_modules/ofetch"], "@unhead/vue": ["../../../../../nuxt/node_modules/@unhead/vue"], "@nuxt/devtools": ["../../../../../nuxt/node_modules/@nuxt/devtools"], "@vue/runtime-core": ["../../../../../nuxt/node_modules/@vue/runtime-core"], "@vue/compiler-sfc": ["../../../../../nuxt/node_modules/@vue/compiler-sfc"], "unplugin-vue-router/client": ["../../../../../nuxt/node_modules/unplugin-vue-router/client"], "@nuxt/schema": ["../../../../../nuxt/node_modules/@nuxt/schema"], "nuxt": ["../../../../../nuxt/node_modules/nuxt"], "~": ["./.."], "@": ["./.."], "~~": ["./.."], "@@": ["./.."], "#shared": ["../shared"], "assets": ["../assets"], "assets/*": ["../assets/*"], "public": ["../public"], "public/*": ["../public/*"], "#build": ["./"], "#build/*": ["./*"], "#internal/nuxt/paths": ["../../../../../nuxt/node_modules/nuxt/dist/core/runtime/nitro/paths"], "#image": ["../../../../../nuxt/node_modules/@nuxt/image/dist/runtime"], "#image/*": ["../../../../../nuxt/node_modules/@nuxt/image/dist/runtime/*"], "#vue-router": ["./vue-router"]}, "lib": ["esnext", "webworker", "dom.iterable"]}, "include": ["./types/nitro-nuxt.d.ts", "../../../../../nuxt/node_modules/@pinia/nuxt/runtime/server", "../../../../../nuxt/node_modules/@element-plus/nuxt/runtime/server", "../../../../../nuxt/node_modules/@vueuse/nuxt/runtime/server", "../../../../../nuxt/node_modules/@nuxt/image/runtime/server", "../../../../../nuxt/node_modules/@nuxt/telemetry/runtime/server", "./types/nitro.d.ts", "../**/*", "../server/**/*"], "exclude": ["../node_modules", "../../../../../nuxt/node_modules/nuxt/node_modules", "../dist"]}