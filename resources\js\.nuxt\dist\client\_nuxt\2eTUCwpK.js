import{d as S,u as k,a as C,i as _,r as l,Q as u,k as w,aq as E,c as T,o as b,t as B,y as p}from"./D_zVjEfm.js";const N=["id","name"],v=S({__name:"my-ckeditor",props:{modelValue:String},emits:["update:modelValue"],setup(d,{emit:m}){k();const f=C(),o=_(),h=d,s=l("editor_"+Math.floor(Math.random()*50)),r=m;let t=null;l({contents:""});const x=()=>{try{t=window.CKEDITOR.replace(s.value,{toolbar_Full:[["Source","-","Save","NewPage","Preview","-","Templates"],["Cut","Copy","Paste","PasteText","PasteFromWord","-","Print","SpellChecker","<PERSON>ayt"],["Undo","Redo","-","Find","Replace","-","SelectAll","RemoveFormat"],"/",["Bold","Italic","Underline","Strike","-","Subscript","Superscript"],["NumberedList","BulletedList","-","Outdent","Indent","Blockquote"],["JustifyLeft","JustifyCenter","JustifyRight","JustifyBlock"],["Link","Unlink","Anchor"],["Image","Table","HorizontalRule","Smiley","SpecialChar","PageBreak"],"/",["Styles","Format","Font","FontSize"],["TextColor","BGColor"],["Maximize","ShowBlocks","-","About"]],uploadUrl:`${p.getConfig("API_BASE")}api/admin/upload/ckeditor?token=${o.adminuser.api_token}`,filebrowserImageUploadUrl:`${p.getConfig("API_BASE")}api/admin/upload/ckeditor?token=${o.adminuser.api_token}`,autoGrow_minHeight:220,autoGrow_maxHeight:600,width:"100%",language:"zh-tw",toolbar:"Full",height:500,allowedContent:!0,extraAllowedContent:"i;span;ul;li;table;td;style;*[id];*(*);*{*}",ignoreEmptyParagraph:!1,enterMode:window.CKEDITOR.ENTER_BR,shiftEnterMode:window.CKEDITOR.ENTER_P,entities:!1,format_tags:"p;h1;h2;h3;pre",fontSize_sizes:"8/8px;9/9px;10/10px;11/11px;12/12px;13/13px;14/14px;16/16px;18/18px;20/20px;22/22px;24/24px;26/26px;28/28px;36/36px;48/48px;72/72px",entities_processNumerical:!0,font_names:"細明體;微軟正黑體;Arial;Helvetica;sans-serif;Comic Sans MS;cursive;Courier New; Courier;monospace;Georgia;serif;Lucida Sans Unicode; Lucida Grande, sans-serif;Tahoma; Geneva; sans-serif;Times New Roman;Times;serif;Trebuchet MS; Helvetica, sans-serif;Verdana; sans-serif"}),t.on("fileUploadRequest",e=>{const a=e.data.fileLoader.xhr;a.setRequestHeader("Authorization",`Bearer ${o.adminuser.api_token}`),a.setRequestHeader("enctype","multipart/form-data; charset=utf-8")}),t.on("fileUploadResponse",e=>{console.log(["fileUploadResponse"]),e.stop();const a=e.data,y=a.fileLoader.xhr,i=y.responseText.split("|");if(i[1])a.message=i[1],e.cancel();else try{const R=JSON.parse(i[0]).resultData.fileName;a.url="undefined/file/image/download/"+R}catch(c){console.error(c),e.cancel()}}),t.on("fileUploadRequest",function(e){console.log(["fileUploadRequest"]);var a=e.data.fileLoader.xhr;a.setRequestHeader("Cache-Control","no-cache"),a.setRequestHeader("X-File-Name",fileName),a.setRequestHeader("X-File-Size",total),a.send(file),e.stop()}),t.on("instanceReady",()=>{t.on("blur",function(e){r("update:modelValue",t.getData())}),t.on("change",()=>{r("update:modelValue",t.getData())}),t.on("key",()=>{console.log("key"),n.value=!0,r("update:modelValue",t.getData())})})}catch(e){console.error(e)}};let n=l(!1);u(()=>h.modelValue,async e=>{if(t&&(console.log(["newValue",e]),e!=null))try{n.value==!1&&t.setData(e)}catch(a){console.error("设置 editor 数据时出错:",a)}},{deep:!0,immediate:!0}),w(async()=>{setTimeout((function(){x()}).bind(this),1e3)});const g=async()=>{t&&(await t.destroy(),t=null)};return u(()=>f.fullPath,async()=>{n.value=!1}),E(()=>{g()}),(e,a)=>(b(),T("textarea",{cols:"80",id:s.value,name:s.value,rows:"10",class:"form-control"},"        "+B(d.modelValue)+`\r
    `,9,N))}});export{v as _};
