import{_ as U}from"./jBNyKXjo.js";import{d as k,f as I,g as B,h as C,i as D,r as v,j as f,k as F,c as N,o as b,l,w as L,v as j,e as S,B as $,m as d,b as s,n as a,E as H,C as M,H as R,I as O,F as T,y as g}from"./D_zVjEfm.js";const z={class:"form-group row"},A={class:"col-md-10"},G={class:"form-group row"},J={class:"col-md-10"},K={class:"form-group row"},P={class:"col-md-10"},Q={class:"form-group row"},W={class:"col-md-10"},X={class:"form-group row"},Y={class:"col-md-10"},Z={class:"form-group row"},h={class:"col-md-10"},ee={class:"form-group row"},se={class:"col-md-10"},oe={class:"form-group row"},te={class:"col-md-10"},le={class:"form-group row"},re={class:"col-md-10"},ae={align:"center"},me=k({__name:"edit",props:{edit:{default:""}},emits:["closed-dialog"],setup(y,{emit:w}){const u=I();B(),C(),D();const c=w,p=y,i=v();v([]);const o=f({});f({x:""}),f({});const V=async()=>{try{const r=await u.post("api/admin/ordergroup/show",{id:p.edit});if(r.resultcode=="0")Object.assign(o,r.data);else throw new Error(r.resultmessage)}catch(r){g.message(r.message),console.error(r)}},_=async()=>{if(i.value)try{if(!await i.value.validate())return;const e=await u.post("api/admin/ordergroup/store",o);if(e.resultcode=="0")g.toast(e.resultmessage),c("closed-dialog",!0);else throw new Error(e.resultmessage)}catch(r){console.error(r),g.formElError(r)}};return F(async()=>{typeof p.edit<"u"&&p.edit!=""&&await V()}),(r,e)=>{const x=U,n=M,m=H,E=O,q=j;return b(),N(T,null,[l(x,{id:"ordergroup"}),L((b(),$(E,{ref_key:"formEl",ref:i,style:{width:"98%"},model:o,onSubmit:R(_,["prevent"])},{default:d(()=>[s("div",z,[e[11]||(e[11]=s("label",{class:"col-md-2"},[a("訂單編號： "),s("span",{class:"text-danger p-1"}," * ")],-1)),s("div",A,[l(m,{prop:"ordergroupnumber",rules:[{required:!0,message:"訂單編號 未填"}]},{default:d(()=>[l(n,{modelValue:o.ordergroupnumber,"onUpdate:modelValue":e[0]||(e[0]=t=>o.ordergroupnumber=t),type:"text"},null,8,["modelValue"])]),_:1})])]),s("div",G,[e[12]||(e[12]=s("label",{class:"col-md-2"},[a("您的姓名： "),s("span",{class:"text-danger p-1"}," * ")],-1)),s("div",J,[l(m,{prop:"rname",rules:[{required:!0,message:"您的姓名 未填"}]},{default:d(()=>[l(n,{modelValue:o.rname,"onUpdate:modelValue":e[1]||(e[1]=t=>o.rname=t),type:"text"},null,8,["modelValue"])]),_:1})])]),s("div",K,[e[13]||(e[13]=s("label",{class:"col-md-2"},[a("公司名稱： "),s("span",{class:"text-danger p-1"})],-1)),s("div",P,[l(m,{prop:"rcompany",rules:[{required:!1,message:"公司名稱 未填"}]},{default:d(()=>[l(n,{modelValue:o.rcompany,"onUpdate:modelValue":e[2]||(e[2]=t=>o.rcompany=t),type:"text"},null,8,["modelValue"])]),_:1})])]),s("div",Q,[e[14]||(e[14]=s("label",{class:"col-md-2"},[a("您的LINE ID： "),s("span",{class:"text-danger p-1"})],-1)),s("div",W,[l(m,{prop:"rline_id",rules:[{required:!1,message:"您的LINE ID 未填"}]},{default:d(()=>[l(n,{modelValue:o.rline_id,"onUpdate:modelValue":e[3]||(e[3]=t=>o.rline_id=t),type:"text"},null,8,["modelValue"])]),_:1})])]),s("div",X,[e[15]||(e[15]=s("label",{class:"col-md-2"},[a("您的手機： "),s("span",{class:"text-danger p-1"})],-1)),s("div",Y,[l(m,{prop:"rmobile",rules:[{required:!1,message:"您的手機 未填"}]},{default:d(()=>[l(n,{modelValue:o.rmobile,"onUpdate:modelValue":e[4]||(e[4]=t=>o.rmobile=t),type:"text"},null,8,["modelValue"])]),_:1})])]),s("div",Z,[e[16]||(e[16]=s("label",{class:"col-md-2"},[a("您的信箱： "),s("span",{class:"text-danger p-1"}," * ")],-1)),s("div",h,[l(m,{prop:"remail",rules:[{required:!0,message:"您的信箱 未填"},{type:"email",message:"您的信箱 格式錯誤"}]},{default:d(()=>[l(n,{modelValue:o.remail,"onUpdate:modelValue":e[5]||(e[5]=t=>o.remail=t),type:"email",placeholder:"ex <EMAIL>"},null,8,["modelValue"])]),_:1})])]),s("div",ee,[e[17]||(e[17]=s("label",{class:"col-md-2"},[a("採購目的： "),s("span",{class:"text-danger p-1"})],-1)),s("div",se,[l(m,{prop:"rpurpose",rules:[{required:!1,message:"採購目的 未填"}]},{default:d(()=>[l(n,{modelValue:o.rpurpose,"onUpdate:modelValue":e[6]||(e[6]=t=>o.rpurpose=t),type:"text"},null,8,["modelValue"])]),_:1})])]),s("div",oe,[e[18]||(e[18]=s("label",{class:"col-md-2"},[a("您的需求： "),s("span",{class:"text-danger p-1"})],-1)),s("div",te,[l(m,{prop:"requirements",rules:[{required:!1,message:"您的需求 未填"}]},{default:d(()=>[l(n,{modelValue:o.requirements,"onUpdate:modelValue":e[7]||(e[7]=t=>o.requirements=t),style:{width:"98%"},rows:6,"show-word-limit":"",type:"textarea"},null,8,["modelValue"])]),_:1})])]),s("div",le,[e[19]||(e[19]=s("label",{class:"col-md-2"},[a("備註： "),s("span",{class:"text-danger p-1"})],-1)),s("div",re,[l(m,{prop:"rmemo",rules:[{required:!1,message:"備註 未填"}]},{default:d(()=>[l(n,{modelValue:o.rmemo,"onUpdate:modelValue":e[8]||(e[8]=t=>o.rmemo=t),style:{width:"98%"},rows:6,"show-word-limit":"",type:"textarea"},null,8,["modelValue"])]),_:1})])]),s("div",ae,[e[20]||(e[20]=s("button",{type:"submit",class:"btn btn-primary"},"確定",-1)),e[21]||(e[21]=a("   ")),s("button",{type:"reset",class:"btn btn-secondary",onClick:e[9]||(e[9]=t=>i.value.resetFields())},"取消"),e[22]||(e[22]=a("   ")),s("button",{type:"button",class:"btn btn-secondary",onClick:e[10]||(e[10]=t=>c("closed-dialog",!1))}," 返回 ")])]),_:1},8,["model"])),[[q,S(u).getLoading()]])],64)}}});export{me as default};
