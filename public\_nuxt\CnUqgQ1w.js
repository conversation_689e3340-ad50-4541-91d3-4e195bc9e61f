import{d as C,ak as u,f as M,am as d,r as i,a7 as N,k as x,M as b,B as m,o as p,m as f,l as _,s as B,a9 as E,aB as S,e as q,N as O,b as R,n as I,ac as H}from"./D_zVjEfm.js";const L={class:"text-danger"},$=C({__name:"my-apicascader",props:u({api:{type:String,required:!0},modelValue:{default:""}},{name1:{required:!0,type:[String,Number],default:""},name1Modifiers:{},name2:{required:!0,type:[String,Number],default:""},name2Modifiers:{}}),emits:u(["update:modelValue","change"],["update:name1","update:name2"]),setup(t,{emit:T}){const g=M(),o=d(t,"name1"),s=d(t,"name2"),y=t,n=i(!1);let l=i([]);const r=N({get(){return[o.value,s.value]},set(e){o.value=e[0],s.value=e[1]}}),c=async()=>{try{n.value=!1;let e=await g.post(y.api,{},!1);if(e.resultcode=="0")l.value=e.data;else throw new Error(e.resultmessage)}catch(e){n.value=!0,console.error("Error fetching options:",e)}};return x(async()=>{c()}),(e,a)=>{const h=S,v=b("Refresh"),V=O,k=H;return p(),m(k,{"fallback-tag":"span",fallback:"Loading"},{default:f(()=>[_(h,{modelValue:r.value,"onUpdate:modelValue":a[0]||(a[0]=w=>r.value=w),options:q(l),style:{width:"100%"}},null,8,["modelValue","options"]),n.value?(p(),m(V,{key:0,class:"reconnect-icon",onClick:c,style:{display:"inline",width:"100%","text-align":"left"}},{default:f(()=>[R("span",L,[_(v),a[1]||(a[1]=I(" 連線失敗，點擊重新連線"))])]),_:1})):B("",!0),E(e.$slots,"default")]),_:3})}}});export{$ as default};
