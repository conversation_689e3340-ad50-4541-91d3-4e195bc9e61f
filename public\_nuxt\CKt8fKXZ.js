import{d as q,i as T,r as y,j as W,y as u,Q as G,k as O,M as Q,B as X,o as p,S as Z,m as s,b as o,l as d,N as J,a9 as K,t as r,T as Y,n as g,e as V,R as ee,ax as le,c as f,F as oe,q as ae,ay as te,az as se,ar as ie,_ as ne}from"./D_zVjEfm.js";const de={class:"container-fluid p-1"},re={class:"upload-container"},ce={class:"upload-area"},ue={class:"el-upload__tip"},pe={class:"preview-area"},me={class:"el-upload-list el-upload-list--picture-card"},ge={key:0,class:"file-container",style:{"text-align":"left","word-break":"break-all"}},fe={style:{"text-align":"center"}},ve={key:1,class:"file-container"},he={class:"file-name"},_e={class:"el-upload-list__item-actions"},we=["onClick"],Fe=["onClick"],ye=["src"],Ve={class:"dialog-loading"},be=q({__name:"my-upload",props:{modelValue:[String,Object],width:String,height:String,accept:{type:String,required:!1,default:"image/*"},limit:{type:Number,required:!1,default:1},fit:{type:String,default:"cover"},folder:{type:String,required:!0}},emits:["update:modelValue"],setup(n,{emit:b}){const U=T(),c=n,v=y(null),x=["jpg","jpeg","png","gif","bmp","webp"],k=b,a=W({dialogVisible:!1,dialogImageUrl:"",uploadFolder:u.getConfig("API_BASE")+(c.folder.startsWith("/")?"":"/")+`${c.folder}`.replace(/\/\//g,"/"),localUploadFiles:[],headers:{},uploading:!1});G(()=>c.modelValue,async l=>{if(Array.isArray(l))return l;if(!(a.localUploadFiles.length>0)&&l!=null&&l!=""){l.split(",");const e=l.split(",").filter(t=>t!=="undefined").map(t=>({name:t,url:`${a.uploadFolder}/${t}`}));a.localUploadFiles=e}},{deep:!0,immediate:!0});const S=l=>(a.uploading=!0,a.headers={Authorization:`Bearer ${U.adminuser.api_token}`},!0),C=(l,e,t)=>{if(a.uploading=!1,l.resultcode=="0")e.name=l.data.name.trim(),e.url=a.uploadFolder+"/"+e.name,a.localUploadFiles.push(e),m();else throw new Error(l.resultmessage)},E=(l,e,t)=>{a.uploading=!1,u.message(l.message)},z=async l=>{u.onDelRow(a.localUploadFiles,l),m(),h()},m=async()=>{let l=[];a.localUploadFiles.forEach(function(e,t){l.push(e.name)}),l=l.join(","),k("update:modelValue",l)},B=(l,e)=>{u.toast("超出限制数量","error")},h=()=>{v.value&&v.value.clearFiles()},A=(l,e)=>{a.localUploadFiles=a.localUploadFiles.filter(t=>t.uid!==l.uid),m(),h()},I=l=>{l.url?window.open(l.url,"_blank").focus():console.warn("Preview URL not available for this file.")},L=l=>{var e;w(l)?(a.dialogImageUrl=l,a.dialogVisible=!0):(e=window.open(l,"_blank"))==null||e.focus()};let _=y("");O(async()=>{let l=c.folder.replaceAll("images/","");console.log(["state.uploadFolder",a.uploadFolder]),_.value=u.getConfig("API_BASE")+"api/admin/upload?width="+c.width+"&height="+c.height+"&f="+l});const w=l=>{var t;const e=((t=l.split(".").pop())==null?void 0:t.toLowerCase())||"";return x.includes(e)},F=l=>l.split("/").pop(),H=()=>{m()};return(l,e)=>{const t=Y,M=ee,N=te,D=ie,P=Q("Loading"),$=J;return p(),X(Z,null,{default:s(()=>[o("div",null,[o("div",de,[o("div",re,[o("div",ce,[d(M,{ref_key:"uploadRef",ref:v,"file-list":a.localUploadFiles,headers:a.headers,action:V(_),multiple:n.limit>1,limit:n.limit,accept:n.accept,"list-type":"picture-card","on-error":E,"on-success":C,"on-exceed":B,"on-remove":A,"on-preview":I,"show-file-list":!1,"before-upload":S},{trigger:s(()=>[d(t,{type:"primary",loading:a.uploading},{default:s(()=>[g(r(a.uploading?"Uploading...":"Click to upload"),1)]),_:1},8,["loading"])]),tip:s(()=>[o("div",ue,r(n.accept)+" size : "+r(n.width)+"*"+r(n.height)+" / upload Limit :"+r(n.limit),1)]),_:1},8,["file-list","headers","action","multiple","limit","accept"])]),o("div",pe,[o("ul",me,[d(V(se),{modelValue:a.localUploadFiles,"onUpdate:modelValue":e[0]||(e[0]=i=>a.localUploadFiles=i),tag:"div",handle:".el-upload-list__item",group:"warngroup","ghost-class":"ghost",class:"warn-card-box",animation:"300",onEnd:H},{default:s(()=>[d(le,null,{default:s(()=>[(p(!0),f(oe,null,ae(a.localUploadFiles,(i,R)=>(p(),f("li",{key:R,class:"el-upload-list__item is-success animated"},[w(i.url)?(p(),f("div",ge,[d(N,{src:i.url,class:"el-upload-list__item-thumbnail",fit:n.fit},{placeholder:s(()=>e[2]||(e[2]=[o("div",{class:"image-slot"},[g("Loading"),o("span",{class:"dot"},"...")],-1)])),error:s(()=>[o("div",fe,[e[3]||(e[3]=g("   no found")),e[4]||(e[4]=o("br",null,null,-1)),g(r(F(i.url)),1)])]),_:2},1032,["src","fit"])])):(p(),f("div",ve,[e[5]||(e[5]=o("div",{class:"file-icon"},[o("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",class:"file-type-icon"},[o("path",{fill:"currentColor",d:"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"})])],-1)),o("div",he,r(F(i.url)),1)])),e[8]||(e[8]=o("label",{class:"el-upload-list__item-status-label"},[o("i",{class:"el-icon el-icon--upload-success el-icon--check"},[o("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[o("path",{fill:"currentColor",d:"M406.656 706.944 195.84 496.256a32 32 0 1 0-45.248 45.248l256 256 512-512a32 32 0 0 0-45.248-45.248L406.592 706.944z"})])])],-1)),e[9]||(e[9]=o("i",{class:"el-icon-close"},null,-1)),o("span",_e,[o("span",{class:"el-upload-list__item-preview",onClick:j=>L(i.url)},e[6]||(e[6]=[o("i",{class:"el-icon el-icon--zoom-in"},[o("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[o("path",{fill:"currentColor",d:"m795.904 750.72 124.992 124.928a32 32 0 0 1-45.248 45.248L750.656 795.904a416 416 0 1 1 45.248-45.248zM480 832a352 352 0 1 0 0-704 352 352 0 0 0 0 704zm-32-384v-96a32 32 0 0 1 64 0v96h96a32 32 0 0 1 0 64h-96v96a32 32 0 0 1-64 0v-96h-96a32 32 0 0 1 0-64h96z"})])],-1)]),8,we),o("span",{class:"el-upload-list__item-delete",onClick:j=>z(i)},e[7]||(e[7]=[o("i",{class:"el-icon el-icon--zoom-in"},[o("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[o("path",{fill:"currentColor",d:"M160 256H96a32 32 0 0 1 0-64h256V95.936a32 32 0 0 1 32-32h256a32 32 0 0 1 32 32V192h256a32 32 0 1 1 0 64h-64v672a32 32 0 0 1-32 32H192a32 32 0 0 1-32-32V256zm448-64v-64H416v64h192zM224 896h576V256H224v640zm192-128a32 32 0 0 1-32-32V416a32 32 0 0 1 64 0v320a32 32 0 0 1-32 32zm192 0a32 32 0 0 1-32-32V416a32 32 0 0 1 64 0v320a32 32 0 0 1-32 32z"})])],-1)]),8,Fe)])]))),128))]),_:1})]),_:1},8,["modelValue"])])])])]),d(D,{modelValue:a.dialogVisible,"onUpdate:modelValue":e[1]||(e[1]=i=>a.dialogVisible=i),fullscreen:!0},{default:s(()=>[o("img",{"w-full":"",src:a.dialogImageUrl,style:{width:"100%"},alt:"Preview Image"},null,8,ye)]),_:1},8,["modelValue"]),K(l.$slots,"default",{},void 0,!0)])]),fallback:s(()=>[o("div",Ve,[d($,{class:"is-loading"},{default:s(()=>[d(P)]),_:1}),e[10]||(e[10]=o("span",null,"Loading...",-1))])]),_:3})}}}),xe=ne(be,[["__scopeId","data-v-f9ebe974"]]);export{xe as default};
