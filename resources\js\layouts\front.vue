<template>
    <!-- ##### Header Area Start ##### -->
    <header class="header-area">
        <!-- ***** Top Header Area ***** -->
        <div class="top-header-area">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <div class="top-header-content d-flex align-items-center justify-content-between">
                            <!-- Top Header Content -->
                            <div class="top-header-meta">
                                <NuxtLink
                                    :to="'mailto:' + (store.config?.email || '')"
                                    target="_blank"
                                    data-toggle="tooltip"
                                    data-placement="bottom"
                                    :title="store.config?.email || ''"
                                    ><i class="fa fa-envelope-o" aria-hidden="true" style="color: #ff5657; margin-right: 5px"></i
                                    ><span>
                                        E-mail:
                                        {{ store.config?.email || '' }}
                                    </span></NuxtLink
                                >
                                <NuxtLink
                                    :to="{ path: `tel:${store.config?.tel || ''}`, query: {} }"
                                    data-toggle="tooltip"
                                    data-placement="bottom"
                                    :title="store.config?.tel || ''"
                                    ><i class="fa fa-phone" aria-hidden="true" style="color: #ff5657; margin-right: 5px"></i>
                                    <span>Call Us: {{ store.config?.tel || '' }}</span></NuxtLink
                                >
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ***** Navbar Area ***** -->
        <div class="alazea-main-menu">
            <div class="classy-nav-container" :class="navContainerClass">
                <div class="container">
                    <!-- Menu -->
                    <nav class="classy-navbar justify-content-between" id="alazeaNav">
                        <!-- Nav Brand -->
                        <NuxtLink :to="{ path: `/`, query: {} }" class="nav-brand">
                            <img src="/img/core-img/oyatt_logo.png" alt="" />
                        </NuxtLink>

                        <!-- Navbar Toggler -->
                        <div class="classy-navbar-toggler" @click="toggleMobileMenu">
                            <span class="navbarToggler" :class="{ active: isMobileMenuOpen }">
                                <span></span>
                                <span></span>
                                <span></span>
                            </span>
                        </div>

                        <!-- Menu -->
                        <div class="classy-menu" :class="{ active: isMobileMenuOpen }" @click.self="closeMobileMenu">
                            <!-- Close Button -->
                            <div class="classycloseIcon" @click="closeMobileMenu">
                                <div class="cross-wrap"><span class="top"></span><span class="bottom"></span></div>
                            </div>

                            <!-- Navbar Start -->
                            <div class="classynav">
                                <ul>
                                    <li>
                                        <NuxtLink :to="{ path: '/', query: {} }" @click="closeMobileMenu"> 首頁 </NuxtLink>
                                    </li>

                                    <li
                                        class="has-dropdown"
                                        :class="{ active: activeDropdown === 'about' && navContainerClass === 'breakpoint-on' }"
                                    >
                                        <a href="#" @click.prevent="toggleDropdown('about')" title="關於我們" class="dropdown-toggle">
                                            關於我們
                                            <i
                                                class="fa fa-angle-down"
                                                :class="{
                                                    'fa-angle-up': activeDropdown === 'about' && navContainerClass === 'breakpoint-on'
                                                }"
                                            ></i>
                                        </a>
                                        <ul
                                            class="dropdown"
                                            :class="{ show: activeDropdown === 'about' && navContainerClass === 'breakpoint-on' }"
                                        >
                                            <li>
                                                <NuxtLink :to="{ path: `/about`, query: {} }" title="關於我們" @click="closeMobileMenu">
                                                    關於我們
                                                </NuxtLink>
                                            </li>
                                            <li v-for="(rs, index) in aboutpage.data" :key="index">
                                                <NuxtLink
                                                    :to="{ path: `/aboutpage/${rs.id}`, query: {} }"
                                                    :title="rs.title"
                                                    @click="closeMobileMenu"
                                                >
                                                    {{ rs.title }}
                                                </NuxtLink>
                                            </li>
                                        </ul>
                                    </li>
                                    <li>
                                        <NuxtLink :to="{ path: '/news', query: {} }" title="最新消息" @click="closeMobileMenu">
                                            最新消息
                                        </NuxtLink>
                                    </li>
                                    <!-- <li>
                                            <NuxtLink :to="{ path: '/page/service', query: {} }" title="服務項目"> 服務項目 </NuxtLink>
                                        </li> -->
                                    <li
                                        class="has-dropdown"
                                        :class="{ active: activeDropdown === 'product' && navContainerClass === 'breakpoint-on' }"
                                    >
                                        <a href="#" @click.prevent="toggleDropdown('product')" title="熱銷產品" class="dropdown-toggle">
                                            熱銷產品
                                            <i
                                                class="fa fa-angle-down"
                                                :class="{
                                                    'fa-angle-up': activeDropdown === 'product' && navContainerClass === 'breakpoint-on'
                                                }"
                                            ></i>
                                        </a>

                                        <ul
                                            class="dropdown"
                                            :class="{ show: activeDropdown === 'product' && navContainerClass === 'breakpoint-on' }"
                                        >
                                            <li>
                                                <NuxtLink
                                                    :to="{ path: '/product', query: {} }"
                                                    title="產品總覽詢價"
                                                    @click="closeMobileMenu"
                                                >
                                                    產品總覽詢價
                                                </NuxtLink>
                                            </li>
                                            <li v-for="(rs, index) in data.data" :key="index">
                                                <NuxtLink
                                                    :to="{ path: `/product/show/${rs.id}`, query: {} }"
                                                    :title="rs.title"
                                                    @click="closeMobileMenu"
                                                >
                                                    {{ rs.title }}
                                                </NuxtLink>
                                            </li>
                                        </ul>
                                    </li>
                                    <li>
                                        <NuxtLink :to="{ path: '/case', query: {} }" title="精選案例" @click="closeMobileMenu">
                                            精選案例
                                        </NuxtLink>
                                    </li>
                                    <li>
                                        <NuxtLink :to="{ path: '/contact', query: {} }" title="聯繫我們" @click="closeMobileMenu">
                                            聯繫我們
                                        </NuxtLink>
                                    </li>
                                </ul>
                            </div>
                            <!-- Navbar End -->
                        </div>
                    </nav>
                </div>
            </div>
        </div>
    </header>
    <!-- ##### Header Area End ##### -->
    <slot />
    <!-- ##### Hero Area Start ##### -->

    <!-- ##### Footer Area Start ##### -->
    <footer class="footer-area bg-img">
        <!-- Main Footer Area -->
        <div class="main-footer-area">
            <div class="container">
                <div class="row">
                    <!-- Single Footer Widget -->
                    <div class="col-12 col-sm-6 col-lg-4">
                        <div class="single-footer-widget">
                            <div class="footer-logo mb-20">
                                <NuxtLink to="#"><img src="/img/core-img/oyatt_logo_footer.png" alt="" /></NuxtLink>
                            </div>
                            <p class="footer_content">
                                售前規劃至售後維護成為您商場上的好夥伴！ 協助企業採購，是您邁向成功事業的強力推手！
                            </p>
                            <div class="social-info">
                                <NuxtLink :to="{ path: store.config?.line || '#', query: {} }" target="_blank"
                                    ><i class="fab fa-line fa-lg"></i
                                ></NuxtLink>
                                <NuxtLink :to="{ path: store.config?.facebook || '#', query: {} }" target="_blank"
                                    ><i class="fab fa-facebook fa-lg"></i
                                ></NuxtLink>
                                <NuxtLink :to="{ path: store.config?.instagram || '#', query: {} }" target="_blank"
                                    ><i class="fab fa-instagram fa-lg"></i
                                ></NuxtLink>
                                <NuxtLink :to="{ path: store.config?.youtube || '#', query: {} }" target="_blank"
                                    ><i class="fab fa-youtube fa-lg"></i
                                ></NuxtLink>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-sm-6 col-lg-4">
                        <div class="single-footer-widget">
                            <p class="footer_content">
                                <my-epost id="homebottom"></my-epost>
                            </p>
                            <!--<p class="footer_content white_color">LINE加好友線上諮詢： 0900-111-455</p>-->
                        </div>
                    </div>

                    <!-- Single Footer Widget -->
                    <div class="col-12 col-sm-6 col-lg-4">
                        <div class="single-footer-widget">
                            <div class="contact-information">
                                <p><span>Address: </span> {{ store.config?.address || '' }}</p>
                                <p>
                                    <span>TEL: </span
                                    ><NuxtLink :to="`tel:${store.config?.tel || ''}`">
                                        {{ store.config?.tel || '' }}
                                    </NuxtLink>
                                </p>
                                <p><span>FAX: </span>{{ store.config?.fax || '' }}</p>
                                <p>
                                    <span>E-mail:</span>
                                    <NuxtLink :to="`mailto:${store.config?.email || ''}`"> {{ store.config?.email || '' }}</NuxtLink>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Bottom Area -->
        <div class="footer-bottom-area">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <div class="border-line"></div>
                    </div>
                    <!-- Copywrite Text -->
                    <div class="col-12">
                        <div class="copywrite-text">
                            <p>
                                {{ utils.getDateTime('yyyy') }}

                                OYATT DESIGN. All Rights Reserved. | <NuxtLink to="/page/privacy">隱私權政策</NuxtLink>
                            </p>
                        </div>
                    </div>
                    <!-- Footer Nav -->
                </div>
            </div>
        </div>
    </footer>
    <div id="cc-main" v-if="!store.data?.cc">
        <div class="cm" role="dialog" aria-modal="true" aria-hidden="false" aria-describedby="cm__desc" aria-labelledby="cm__title">
            <div class="cm__body">
                <div class="cm__texts">
                    <h2 id="cm__title">我們使用Cookies</h2>
                    <p class="cm__desc">
                        本網站使用cookies以提昇您的使用體驗及統計網路流量相關資料。繼續使用本網站表示您同意我們使用cookies。請閱讀我們的

                        <NuxtLink :to="{ path: `/privacy`, query: {} }" target="new"> 隱私權政策 </NuxtLink>
                    </p>
                </div>
                <div class="cm__btns">
                    <div class="cm__btn-group">
                        <a class="btn7 btn-secondary" @click="store.data.cc = 1">完全接受</a>
                    </div>

                    <div class="cm__btn-group">
                        <a
                            class="btn7 btn-secondary"
                            @click="
                                () => {
                                    store.data.cc = 1
                                    hideCC()
                                }
                            "
                            >完全拒絕</a
                        >
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, reactive, watch } from 'vue'

// 必要的導入
const http = createHttp() //http套件
const router = useRouter()
const store = useDataStore()
const route = useRoute()
// 使用正確的導入方式
import { utils } from '@/utils'

// 類型定義 (如果全域類型定義不可用)
interface Product {
    id: number
    title: string
    [key: string]: any
}

interface Aboutpage {
    id: number
    title: string
    [key: string]: any
}

interface ApiResponse {
    resultcode: string
    resultmessage: string
    data: any
}

useHead({
    link: [
        {
            rel: 'stylesheet',
            href: '/css/cc.css'
        },
        {
            rel: 'stylesheet',
            href: '/css/style.css'
        },
        {
            rel: 'stylesheet',
            href: '/css/carousel.css'
        },
        {
            rel: 'stylesheet',
            href: '/css/animate.min.css' // 引入 Animate.css 庫
        }
    ],
    script: [
        {
            src: '/js/jquery/jquery-2.2.4.min.js'
        },
        {
            src: '/js/bootstrap/popper.min.js'
        },
        {
            src: '/js/bootstrap/bootstrap.min.js'
        },
        {
            src: '/js/plugins/plugins.js'
        },
        {
            src: 'https://use.fontawesome.com/releases/v5.0.9/js/all.js'
        },
        {
            src: 'https://use.fontawesome.com/releases/v5.0.9/js/v4-shims.js'
        },
        {
            src: '/js/slick.js'
        },
        {
            src: '/js/wow.min.js' // 引入 WOW.js 庫
        },
        {
            src: '/js/active.js'
        }
    ]
})

const inputs = reactive({
    //不用加.value
    title: ''
})
const data = reactive({
    data: [] as Product[]
})
const aboutpage = reactive({
    data: [] as Aboutpage[]
})

// 手機版選單狀態管理
const isMobileMenuOpen = ref(false)

// 下拉選單狀態管理
const activeDropdown = ref<string | null>(null)

// 導航欄容器類別管理（響應式breakpoint控制）
const navContainerClass = ref('breakpoint-off')

// 螢幕寬度監聽
const windowWidth = ref(0)

const getData_product = async () => {
    try {
        let rep = (await http.post('api/product', inputs)) as ApiResponse
        if (rep.resultcode == '0') {
            Object.assign(data, rep.data)
        }
    } catch (error) {
        console.error('載入產品資料錯誤:', error)
    }
}

const getData_aboutpage = async () => {
    try {
        let rep = (await http.post('api/aboutpage', inputs)) as ApiResponse
        if (rep.resultcode == '0') {
            Object.assign(aboutpage, rep.data)
        }
    } catch (error) {
        console.error('載入關於頁面資料錯誤:', error)
    }
}

// 隱藏cookie同意框的函數
const hideCC = () => {
    const ccMain = document.getElementById('cc-main')
    if (ccMain) {
        ccMain.style.display = 'none'
    }
}

// 手機版選單控制函數
const toggleMobileMenu = () => {
    isMobileMenuOpen.value = !isMobileMenuOpen.value
}

const closeMobileMenu = () => {
    isMobileMenuOpen.value = false
    activeDropdown.value = null // 關閉選單時也關閉下拉選單
}

// 下拉選單控制函數
const toggleDropdown = (dropdownName: string) => {
    // 在手機版時才使用Vue控制，桌面版使用CSS hover
    if (navContainerClass.value === 'breakpoint-on') {
        if (activeDropdown.value === dropdownName) {
            activeDropdown.value = null
        } else {
            activeDropdown.value = dropdownName
        }
        console.log('手機版下拉選單狀態:', activeDropdown.value)
    }
}

const closeDropdown = () => {
    activeDropdown.value = null
}

// breakpoint檢查函數（與classyNav插件保持一致）
const checkBreakpoint = () => {
    windowWidth.value = window.innerWidth
    if (windowWidth.value <= 991) {
        navContainerClass.value = 'breakpoint-on'
    } else {
        navContainerClass.value = 'breakpoint-off'
        // 桌面版時自動關閉手機版選單
        isMobileMenuOpen.value = false
        activeDropdown.value = null
    }
}

// 載入數據
getData_product()
getData_aboutpage()

// 組件掛載後初始化
onMounted(() => {
    // 初始化breakpoint檢查
    checkBreakpoint()

    // 添加窗口大小監聽器
    window.addEventListener('resize', checkBreakpoint)

    // Vue控制的手機版選單不需要jQuery初始化
    console.log('Vue導航組件已掛載，使用響應式選單控制')
    console.log('產品數據:', data.data)
    console.log('關於頁面數據:', aboutpage.data)
    console.log('當前螢幕寬度:', windowWidth.value)
    console.log('導航欄類別:', navContainerClass.value)
})

// 組件卸載時清理監聽器
onUnmounted(() => {
    window.removeEventListener('resize', checkBreakpoint)
})

watch(
    () => route.fullPath,
    () => {
        // 路由變化時自動關閉手機版選單和下拉選單
        isMobileMenuOpen.value = false
        activeDropdown.value = null
    }
)
</script>

<style scoped>
/* 手機版選單的Vue控制樣式 */
.classy-navbar-toggler {
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 8px;
    border-radius: 6px;
    background: rgba(255, 86, 87, 0.1);
    border: 2px solid transparent;
}

.classy-navbar-toggler:hover {
    background: rgba(255, 86, 87, 0.2);
    border-color: rgba(255, 86, 87, 0.3);
    transform: scale(1.05);
}

.navbarToggler span {
    display: block;
    width: 25px;
    height: 3px;
    background: #ff5657;
    margin: 4px 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 2px;
}

.navbarToggler.active span {
    background: #ff5657;
}

.navbarToggler.active span:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.navbarToggler.active span:nth-child(2) {
    opacity: 0;
    transform: scale(0);
}

.navbarToggler.active span:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* 手機版選單顯示控制 */
@media (max-width: 991px) {
    .classy-menu {
        position: fixed;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100vh;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        z-index: 9999;
        transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
        visibility: hidden;
        opacity: 0;
        overflow-y: auto;
        padding-top: 80px;
        box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
    }

    .classy-menu.active {
        left: 0;
        visibility: visible;
        opacity: 1;
    }

    .classycloseIcon {
        position: absolute;
        top: 25px;
        right: 25px;
        cursor: pointer;
        z-index: 10000;
        width: 35px;
        height: 35px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 86, 87, 0.1);
        border-radius: 50%;
        transition: all 0.3s ease;
    }

    .classycloseIcon:hover {
        background: rgba(255, 86, 87, 0.2);
        transform: scale(1.1);
    }

    /* 手機版主選單項目樣式 */
    .classynav ul {
        padding: 0 20px;
    }

    .classynav ul li {
        border-bottom: 1px solid rgba(238, 238, 238, 0.5);
        margin-bottom: 5px;
        border-radius: 8px;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .classynav ul li:last-child {
        border-bottom: none;
    }

    .classynav ul li:hover {
        background-color: rgba(255, 86, 87, 0.05);
        transform: translateX(5px);
    }

    .classynav ul li > a,
    .classynav ul li > .dropdown-toggle {
        display: block;
        padding: 18px 20px;
        color: #333;
        text-decoration: none;
        font-size: 16px;
        font-weight: 500;
        border: none;
        background: none;
        width: 100%;
        text-align: left;
        transition: all 0.3s ease;
        position: relative;
    }

    .classynav ul li > a:hover,
    .classynav ul li > .dropdown-toggle:hover {
        color: #ff5657;
        padding-left: 25px;
    }

    .classynav ul li > a::before,
    .classynav ul li > .dropdown-toggle::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 0;
        background: #ff5657;
        transition: height 0.3s ease;
    }

    .classynav ul li > a:hover::before,
    .classynav ul li > .dropdown-toggle:hover::before {
        height: 60%;
    }
}

/* 覆蓋層，點擊時關閉選單 */
.classy-menu.active::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: -1;
}

/* 下拉選單樣式 */
.has-dropdown {
    position: relative;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    text-decoration: none;
    color: inherit;
    cursor: pointer;
}

.dropdown-toggle i {
    margin-left: 5px;
    transition: transform 0.3s ease;
}

/* 桌面版下拉選單 - 使用hover效果 */
@media (min-width: 992px) {
    .dropdown {
        display: block;
        position: absolute;
        top: 100%;
        left: 0;
        min-width: 200px;
        background: #fff;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border-radius: 4px;
        z-index: 1000;
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: all 0.3s ease;
    }

    .has-dropdown:hover .dropdown {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }
}

/* 手機版下拉選單 - 使用Vue控制 */
@media (max-width: 991px) {
    .dropdown {
        display: block !important;
        position: static;
        box-shadow: none;
        background: #f8f9fa;
        margin-top: 10px;
        border-radius: 0;
        transform: none !important;
        opacity: 1 !important;
        visibility: visible !important;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
    }

    .dropdown.show {
        max-height: 500px;
    }
}

.dropdown li {
    border-bottom: 1px solid #eee;
}

.dropdown li:last-child {
    border-bottom: none;
}

.dropdown a {
    display: block;
    padding: 12px 20px;
    color: #333;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.dropdown a:hover {
    background-color: #f8f9fa;
    color: #ff5657;
}

/* 下拉選單共用樣式 */
.dropdown li {
    border-bottom: 1px solid #eee;
}

.dropdown li:last-child {
    border-bottom: none;
}

.dropdown a {
    display: block;
    padding: 12px 20px;
    color: #333;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.dropdown a:hover {
    background-color: #f8f9fa;
    color: #ff5657;
}

/* 手機版特殊樣式 */
@media (max-width: 991px) {
    .dropdown {
        background: rgba(248, 249, 250, 0.8);
        border-radius: 8px;
        margin: 10px 0;
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .dropdown a {
        padding: 12px 25px;
        border-left: 3px solid transparent;
        color: #555 !important;
        font-size: 15px;
        position: relative;
        transition: all 0.3s ease;
    }

    .dropdown a:hover {
        border-left-color: #ff5657;
        background-color: rgba(255, 86, 87, 0.1);
        color: #ff5657 !important;
        padding-left: 30px;
    }

    .dropdown a::before {
        content: '▸';
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #ff5657;
        opacity: 0;
        transition: all 0.3s ease;
    }

    .dropdown a:hover::before {
        opacity: 1;
        left: 15px;
    }

    /* 手機版下拉選單箭頭動畫 */
    .has-dropdown .dropdown-toggle i {
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        font-size: 14px;
        color: #ff5657;
        background: rgba(255, 86, 87, 0.1);
        border-radius: 50%;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .has-dropdown.active .dropdown-toggle i {
        transform: rotate(180deg);
        background: rgba(255, 86, 87, 0.2);
    }

    /* 下拉選單展開動畫 */
    .has-dropdown.active .dropdown.show {
        animation: slideDown 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    @keyframes slideDown {
        from {
            max-height: 0;
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            max-height: 500px;
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* 為有下拉選單的項目添加特殊樣式 */
    .has-dropdown > .dropdown-toggle {
        background: linear-gradient(135deg, rgba(255, 86, 87, 0.05) 0%, rgba(255, 86, 87, 0.1) 100%);
        border-radius: 8px;
        margin-bottom: 5px;
    }

    .has-dropdown.active > .dropdown-toggle {
        background: linear-gradient(135deg, rgba(255, 86, 87, 0.1) 0%, rgba(255, 86, 87, 0.15) 100%);
        color: #ff5657;
    }
}
</style>
