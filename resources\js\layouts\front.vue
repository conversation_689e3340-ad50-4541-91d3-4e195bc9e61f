<template>
    <!-- ##### Header Area Start ##### -->
    <header class="header-area">
        <!-- ***** Top Header Area ***** -->
        <div class="top-header-area">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <div class="top-header-content d-flex align-items-center justify-content-between">
                            <!-- Top Header Content -->
                            <div class="top-header-meta">
                                <NuxtLink
                                    :to="'mailto:' + (store.config?.email || '')"
                                    target="_blank"
                                    data-toggle="tooltip"
                                    data-placement="bottom"
                                    :title="store.config?.email || ''"
                                    ><i class="fa fa-envelope-o" aria-hidden="true" style="color: #ff5657; margin-right: 5px"></i
                                    ><span>
                                        E-mail:
                                        {{ store.config?.email || '' }}
                                    </span></NuxtLink
                                >
                                <NuxtLink
                                    :to="{ path: `tel:${store.config?.tel || ''}`, query: {} }"
                                    data-toggle="tooltip"
                                    data-placement="bottom"
                                    :title="store.config?.tel || ''"
                                    ><i class="fa fa-phone" aria-hidden="true" style="color: #ff5657; margin-right: 5px"></i>
                                    <span>Call Us: {{ store.config?.tel || '' }}</span></NuxtLink
                                >
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ***** Navbar Area ***** -->
        <div class="alazea-main-menu">
            <div class="classy-nav-container breakpoint-off">
                <div class="container">
                    <!-- Menu -->
                    <nav class="classy-navbar justify-content-between" id="alazeaNav">
                        <!-- Nav Brand -->
                        <NuxtLink :to="{ path: `/`, query: {} }" class="nav-brand">
                            <img src="/img/core-img/oyatt_logo.png" alt="" />
                        </NuxtLink>

                        <!-- Navbar Toggler -->
                        <div class="classy-navbar-toggler" @click="toggleMobileMenu">
                            <span class="navbarToggler" :class="{ active: isMobileMenuOpen }">
                                <span></span>
                                <span></span>
                                <span></span>
                            </span>
                        </div>

                        <!-- Menu -->
                        <div class="classy-menu" :class="{ active: isMobileMenuOpen }" @click.self="closeMobileMenu">
                            <!-- Close Button -->
                            <div class="classycloseIcon" @click="closeMobileMenu">
                                <div class="cross-wrap"><span class="top"></span><span class="bottom"></span></div>
                            </div>

                            <!-- Navbar Start -->
                            <div class="classynav">
                                <ul>
                                    <li>
                                        <NuxtLink :to="{ path: '/', query: {} }" @click="closeMobileMenu"> 首頁 </NuxtLink>
                                    </li>

                                    <li class="has-dropdown" :class="{ active: activeDropdown === 'about' }">
                                        <a href="#" @click.prevent="toggleDropdown('about')" title="關於我們" class="dropdown-toggle">
                                            關於我們
                                            <i class="fa fa-angle-down" :class="{ 'fa-angle-up': activeDropdown === 'about' }"></i>
                                        </a>
                                        <ul class="dropdown" :class="{ show: activeDropdown === 'about' }">
                                            <li>
                                                <NuxtLink :to="{ path: `/about`, query: {} }" title="關於我們" @click="closeMobileMenu">
                                                    關於我們
                                                </NuxtLink>
                                            </li>
                                            <li v-for="(rs, index) in aboutpage.data" :key="index">
                                                <NuxtLink
                                                    :to="{ path: `/aboutpage/${rs.id}`, query: {} }"
                                                    :title="rs.title"
                                                    @click="closeMobileMenu"
                                                >
                                                    {{ rs.title }}
                                                </NuxtLink>
                                            </li>
                                        </ul>
                                    </li>
                                    <li>
                                        <NuxtLink :to="{ path: '/news', query: {} }" title="最新消息" @click="closeMobileMenu">
                                            最新消息
                                        </NuxtLink>
                                    </li>
                                    <!-- <li>
                                            <NuxtLink :to="{ path: '/page/service', query: {} }" title="服務項目"> 服務項目 </NuxtLink>
                                        </li> -->
                                    <li class="has-dropdown" :class="{ active: activeDropdown === 'product' }">
                                        <a href="#" @click.prevent="toggleDropdown('product')" title="熱銷產品" class="dropdown-toggle">
                                            熱銷產品
                                            <i class="fa fa-angle-down" :class="{ 'fa-angle-up': activeDropdown === 'product' }"></i>
                                        </a>

                                        <ul class="dropdown" :class="{ show: activeDropdown === 'product' }">
                                            <li>
                                                <NuxtLink
                                                    :to="{ path: '/product', query: {} }"
                                                    title="產品總覽詢價"
                                                    @click="closeMobileMenu"
                                                >
                                                    產品總覽詢價
                                                </NuxtLink>
                                            </li>
                                            <li v-for="(rs, index) in data.data" :key="index">
                                                <NuxtLink
                                                    :to="{ path: `/product/show/${rs.id}`, query: {} }"
                                                    :title="rs.title"
                                                    @click="closeMobileMenu"
                                                >
                                                    {{ rs.title }}
                                                </NuxtLink>
                                            </li>
                                        </ul>
                                    </li>
                                    <li>
                                        <NuxtLink :to="{ path: '/case', query: {} }" title="精選案例" @click="closeMobileMenu">
                                            精選案例
                                        </NuxtLink>
                                    </li>
                                    <li>
                                        <NuxtLink :to="{ path: '/contact', query: {} }" title="聯繫我們" @click="closeMobileMenu">
                                            聯繫我們
                                        </NuxtLink>
                                    </li>
                                </ul>
                            </div>
                            <!-- Navbar End -->
                        </div>
                    </nav>
                </div>
            </div>
        </div>
    </header>
    <!-- ##### Header Area End ##### -->
    <slot />
    <!-- ##### Hero Area Start ##### -->

    <!-- ##### Footer Area Start ##### -->
    <footer class="footer-area bg-img">
        <!-- Main Footer Area -->
        <div class="main-footer-area">
            <div class="container">
                <div class="row">
                    <!-- Single Footer Widget -->
                    <div class="col-12 col-sm-6 col-lg-4">
                        <div class="single-footer-widget">
                            <div class="footer-logo mb-20">
                                <NuxtLink to="#"><img src="/img/core-img/oyatt_logo_footer.png" alt="" /></NuxtLink>
                            </div>
                            <p class="footer_content">
                                售前規劃至售後維護成為您商場上的好夥伴！ 協助企業採購，是您邁向成功事業的強力推手！
                            </p>
                            <div class="social-info">
                                <NuxtLink :to="{ path: store.config?.line || '#', query: {} }" target="_blank"
                                    ><i class="fab fa-line fa-lg"></i
                                ></NuxtLink>
                                <NuxtLink :to="{ path: store.config?.facebook || '#', query: {} }" target="_blank"
                                    ><i class="fab fa-facebook fa-lg"></i
                                ></NuxtLink>
                                <NuxtLink :to="{ path: store.config?.instagram || '#', query: {} }" target="_blank"
                                    ><i class="fab fa-instagram fa-lg"></i
                                ></NuxtLink>
                                <NuxtLink :to="{ path: store.config?.youtube || '#', query: {} }" target="_blank"
                                    ><i class="fab fa-youtube fa-lg"></i
                                ></NuxtLink>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-sm-6 col-lg-4">
                        <div class="single-footer-widget">
                            <p class="footer_content">
                                <my-epost id="homebottom"></my-epost>
                            </p>
                            <!--<p class="footer_content white_color">LINE加好友線上諮詢： 0900-111-455</p>-->
                        </div>
                    </div>

                    <!-- Single Footer Widget -->
                    <div class="col-12 col-sm-6 col-lg-4">
                        <div class="single-footer-widget">
                            <div class="contact-information">
                                <p><span>Address: </span> {{ store.config?.address || '' }}</p>
                                <p>
                                    <span>TEL: </span
                                    ><NuxtLink :to="`tel:${store.config?.tel || ''}`">
                                        {{ store.config?.tel || '' }}
                                    </NuxtLink>
                                </p>
                                <p><span>FAX: </span>{{ store.config?.fax || '' }}</p>
                                <p>
                                    <span>E-mail:</span>
                                    <NuxtLink :to="`mailto:${store.config?.email || ''}`"> {{ store.config?.email || '' }}</NuxtLink>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Bottom Area -->
        <div class="footer-bottom-area">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <div class="border-line"></div>
                    </div>
                    <!-- Copywrite Text -->
                    <div class="col-12">
                        <div class="copywrite-text">
                            <p>
                                {{ utils.getDateTime('yyyy') }}

                                OYATT DESIGN. All Rights Reserved. | <NuxtLink to="/page/privacy">隱私權政策</NuxtLink>
                            </p>
                        </div>
                    </div>
                    <!-- Footer Nav -->
                </div>
            </div>
        </div>
    </footer>
    <div id="cc-main" v-if="!store.data?.cc">
        <div class="cm" role="dialog" aria-modal="true" aria-hidden="false" aria-describedby="cm__desc" aria-labelledby="cm__title">
            <div class="cm__body">
                <div class="cm__texts">
                    <h2 id="cm__title">我們使用Cookies</h2>
                    <p class="cm__desc">
                        本網站使用cookies以提昇您的使用體驗及統計網路流量相關資料。繼續使用本網站表示您同意我們使用cookies。請閱讀我們的

                        <NuxtLink :to="{ path: `/privacy`, query: {} }" target="new"> 隱私權政策 </NuxtLink>
                    </p>
                </div>
                <div class="cm__btns">
                    <div class="cm__btn-group">
                        <a class="btn7 btn-secondary" @click="store.data.cc = 1">完全接受</a>
                    </div>

                    <div class="cm__btn-group">
                        <a
                            class="btn7 btn-secondary"
                            @click="
                                () => {
                                    store.data.cc = 1
                                    hideCC()
                                }
                            "
                            >完全拒絕</a
                        >
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, watch } from 'vue'

// 必要的導入
const http = createHttp() //http套件
const router = useRouter()
const store = useDataStore()
const route = useRoute()
// 使用正確的導入方式
import { utils } from '@/utils'

// 類型定義 (如果全域類型定義不可用)
interface Product {
    id: number
    title: string
    [key: string]: any
}

interface Aboutpage {
    id: number
    title: string
    [key: string]: any
}

interface ApiResponse {
    resultcode: string
    resultmessage: string
    data: any
}

useHead({
    link: [
        {
            rel: 'stylesheet',
            href: '/css/cc.css'
        },
        {
            rel: 'stylesheet',
            href: '/css/style.css'
        },
        {
            rel: 'stylesheet',
            href: '/css/carousel.css'
        },
        {
            rel: 'stylesheet',
            href: '/css/animate.min.css' // 引入 Animate.css 庫
        }
    ],
    script: [
        {
            src: '/js/jquery/jquery-2.2.4.min.js'
        },
        {
            src: '/js/bootstrap/popper.min.js'
        },
        {
            src: '/js/bootstrap/bootstrap.min.js'
        },
        {
            src: '/js/plugins/plugins.js'
        },
        {
            src: 'https://use.fontawesome.com/releases/v5.0.9/js/all.js'
        },
        {
            src: 'https://use.fontawesome.com/releases/v5.0.9/js/v4-shims.js'
        },
        {
            src: '/js/slick.js'
        },
        {
            src: '/js/wow.min.js' // 引入 WOW.js 庫
        },
        {
            src: '/js/active.js'
        }
    ]
})

const inputs = reactive({
    //不用加.value
    title: ''
})
const data = reactive({
    data: [] as Product[]
})
const aboutpage = reactive({
    data: [] as Aboutpage[]
})

// 手機版選單狀態管理
const isMobileMenuOpen = ref(false)

// 下拉選單狀態管理
const activeDropdown = ref<string | null>(null)

const getData_product = async () => {
    try {
        let rep = (await http.post('api/product', inputs)) as ApiResponse
        if (rep.resultcode == '0') {
            Object.assign(data, rep.data)
        }
    } catch (error) {
        console.error('載入產品資料錯誤:', error)
    }
}

const getData_aboutpage = async () => {
    try {
        let rep = (await http.post('api/aboutpage', inputs)) as ApiResponse
        if (rep.resultcode == '0') {
            Object.assign(aboutpage, rep.data)
        }
    } catch (error) {
        console.error('載入關於頁面資料錯誤:', error)
    }
}

// 隱藏cookie同意框的函數
const hideCC = () => {
    const ccMain = document.getElementById('cc-main')
    if (ccMain) {
        ccMain.style.display = 'none'
    }
}

// 手機版選單控制函數
const toggleMobileMenu = () => {
    isMobileMenuOpen.value = !isMobileMenuOpen.value
}

const closeMobileMenu = () => {
    isMobileMenuOpen.value = false
    activeDropdown.value = null // 關閉選單時也關閉下拉選單
}

// 下拉選單控制函數
const toggleDropdown = (dropdownName: string) => {
    if (activeDropdown.value === dropdownName) {
        activeDropdown.value = null
    } else {
        activeDropdown.value = dropdownName
    }
    console.log('下拉選單狀態:', activeDropdown.value)
}

const closeDropdown = () => {
    activeDropdown.value = null
}

// 載入數據
getData_product()
getData_aboutpage()

// 組件掛載後初始化
onMounted(() => {
    // Vue控制的手機版選單不需要jQuery初始化
    console.log('Vue導航組件已掛載，使用響應式選單控制')
    console.log('產品數據:', data.data)
    console.log('關於頁面數據:', aboutpage.data)
})

watch(
    () => route.fullPath,
    () => {
        // 路由變化時自動關閉手機版選單和下拉選單
        isMobileMenuOpen.value = false
        activeDropdown.value = null
    }
)
</script>

<style scoped>
/* 手機版選單的Vue控制樣式 */
.classy-navbar-toggler {
    cursor: pointer;
    transition: all 0.3s ease;
}

.navbarToggler.active span {
    transition: all 0.3s ease;
}

.navbarToggler.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.navbarToggler.active span:nth-child(2) {
    opacity: 0;
}

.navbarToggler.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* 手機版選單顯示控制 */
@media (max-width: 991px) {
    .classy-menu {
        position: fixed;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100vh;
        background: #fff;
        z-index: 9999;
        transition: all 0.35s ease;
        visibility: hidden;
        opacity: 0;
        overflow-y: auto; /* 允許滾動 */
        padding-top: 60px; /* 為關閉按鈕留空間 */
    }

    .classy-menu.active {
        left: 0;
        visibility: visible;
        opacity: 1;
    }

    .classycloseIcon {
        position: absolute;
        top: 20px;
        right: 20px;
        cursor: pointer;
        z-index: 10000;
        width: 30px;
        height: 30px;
    }

    /* 手機版主選單項目樣式 */
    .classynav ul li {
        border-bottom: 1px solid #eee;
    }

    .classynav ul li:last-child {
        border-bottom: none;
    }

    .classynav ul li > a,
    .classynav ul li > .dropdown-toggle {
        display: block;
        padding: 15px 20px;
        color: #333;
        text-decoration: none;
        font-size: 16px;
        border: none;
        background: none;
        width: 100%;
        text-align: left;
    }

    .classynav ul li > a:hover,
    .classynav ul li > .dropdown-toggle:hover {
        background-color: #f8f9fa;
        color: #ff5657;
    }
}

/* 覆蓋層，點擊時關閉選單 */
.classy-menu.active::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: -1;
}

/* 下拉選單樣式 */
.has-dropdown {
    position: relative;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    text-decoration: none;
    color: inherit;
    cursor: pointer;
}

.dropdown-toggle i {
    margin-left: 5px;
    transition: transform 0.3s ease;
}

.dropdown {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 200px;
    background: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    z-index: 1000;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.dropdown.show {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

.dropdown li {
    border-bottom: 1px solid #eee;
}

.dropdown li:last-child {
    border-bottom: none;
}

.dropdown a {
    display: block;
    padding: 12px 20px;
    color: #333;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.dropdown a:hover {
    background-color: #f8f9fa;
    color: #ff5657;
}

/* 手機版下拉選單樣式 */
@media (max-width: 991px) {
    .dropdown {
        display: block !important; /* 強制顯示，用max-height控制 */
        position: static;
        box-shadow: none;
        background: #f8f9fa;
        margin-top: 10px;
        border-radius: 0;
        transform: none !important;
        opacity: 1 !important;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
    }

    .dropdown.show {
        max-height: 500px;
    }

    .dropdown a {
        padding: 10px 20px;
        border-left: 3px solid transparent;
        color: #333 !important;
    }

    .dropdown a:hover {
        border-left-color: #ff5657;
        background-color: #e9ecef;
    }

    /* 確保手機版的下拉選單連結樣式 */
    .dropdown li a {
        display: block;
        width: 100%;
        text-decoration: none;
    }

    /* 手機版下拉選單箭頭 */
    .has-dropdown .dropdown-toggle i {
        transition: transform 0.3s ease;
        font-size: 14px;
    }

    .has-dropdown.active .dropdown-toggle i {
        transform: rotate(180deg);
    }

    /* 確保手機版下拉選單內容可見 */
    .has-dropdown.active .dropdown.show {
        animation: slideDown 0.3s ease;
    }

    @keyframes slideDown {
        from {
            max-height: 0;
        }
        to {
            max-height: 500px;
        }
    }
}
</style>
