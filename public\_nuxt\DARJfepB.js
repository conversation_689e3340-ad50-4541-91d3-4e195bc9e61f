import{d as f,f as h,a5 as x,c as r,o as l,b as s,n as i,s as y,F as v,q as k,a3 as C,t as n,e as d,y as c,_ as w}from"./D_zVjEfm.js";const L={class:"container my-4"},N={class:"card"},E={class:"list-group list-group-flush"},B={class:"flex-grow-1"},S={class:"mb-1",style:{"max-height":"100px",overflow:"auto"}},T={class:"text-muted"},V={key:0,class:"list-group-item text-center text-muted"},D=f({__name:"index",setup(F){const m=h(),{$consoleLogs:g}=x(),a=g,u=e=>{const t={log:"bg-primary",info:"bg-success",warn:"bg-warning text-dark",error:"bg-danger",debug:"bg-secondary",trace:"bg-info",table:"bg-dark",default:"bg-light text-dark"};return`${t[e]||t.default} rounded-pill`},p=async()=>{try{let e=await m.post("api/logs/store",{code:c.getDateTime(),messages:JSON.stringify(a.value)});if(e.resultcode=="0")c.toast("上傳成功","success",2e3);else throw new Error(e.resultmessage)}catch(e){c.formElError(e),console.error(e)}},b=()=>{a.value=[]};return(e,t)=>(l(),r("div",L,[s("div",{class:"form-group row m-2"},[t[1]||(t[1]=s("label",{class:"col-md-6 sm-12"},"Today’s Console Logs",-1)),s("div",{class:"col-md-6 sm-12",align:"right"},[s("button",{class:"btn btn-outline-danger btn-sm",onClick:b},"Clear Logs"),t[0]||(t[0]=i("    ")),s("button",{class:"btn btn-primary btn-sm",onClick:p},"Upload to Server")])]),s("div",N,[s("ul",E,[(l(!0),r(v,null,k(d(a),(o,_)=>(l(),r("li",{key:_,class:"list-group-item d-flex align-items-start"},[s("span",{class:C([u(o.method),"badge me-3"])},n(o.method.toUpperCase()),3),s("div",B,[i(n(o.url)+" ",1),s("p",S,n(o.message),1),s("small",T,n(o.timestamp),1)])]))),128)),d(a).length===0?(l(),r("li",V,"No logs for today yet.")):y("",!0)])])]))}}),$=w(D,[["__scopeId","data-v-b0079d17"]]);export{$ as default};
