const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./t9XsTDlZ.js","./jBNyKXjo.js","./D_zVjEfm.js","./entry.BGE7dmog.css","./fnCu7pBG.js","./my-xmlform.DrN1F5vA.css","./CJ4yysAX.js","./sXr78Ku-.js","./G4Mf9Qin.js","./my-search.Din1H_Ix.css","./_cPjsZyH.js","./CQvHppDm.js","./HHVucgb9.js","./my-pagination.C28Xn-qp.css","./BBMl127G.js","./D98v7cVU.js","./BJKtYJtC.js","./ChWBnVGr.js","./Bq1ujDbh.js","./2eTUCwpK.js","./DQgfFnhs.js","./CQF4jwmL.js","./BzDCd5bH.js","./BWS-stcc.js","./ODG5hW3s.js","./Dc9n8SY3.js","./DFv2tWN4.js","./GgDM1tNs.js","./k4dtWBPv.js","./CKhpvNlx.js","./index.B9BDNnw-.css","./CyCvQXuY.js","./y9aR0iNK.js","./CBWOu-An.js","./BBdYVKME.js","./CKt8fKXZ.js","./my-upload.C1aM5ZLD.css","./Cu6CK232.js","./CJyaJRfW.js","./DyBIXSDg.js"])))=>i.map(i=>d[i]);
import{d as C,r as y,ap as k,al as S,j as R,Q as B,aq as U,B as g,o as v,l as E,ar as N,a3 as V,m as l,b as a,x as q,S as K,N as $,e as h,as as j,O as z,P as H,t as Q,at as W,au as F,y as L,L as e}from"./D_zVjEfm.js";const G={class:"dialog-fixed-header"},J=["id"],M={class:"dialog-content"},X={class:"dialog-loading"},te=C({__name:"my-dialog",props:{},emits:["closed-dialog"],setup(Y,{expose:O,emit:A}){const s=y(!1),m=k(null),{width:I}=S(),i=R({width:0,height:0,isCloseReload:!1}),T=A,f=Object.assign({"/pages/admin/adminuser/edit.vue":()=>e(()=>import("./t9XsTDlZ.js"),__vite__mapDeps([0,1,2,3,4,5]),import.meta.url),"/pages/admin/adminuser/index.vue":()=>e(()=>import("./CJ4yysAX.js"),__vite__mapDeps([6,1,2,3,7,8,9,10,11,12,13]),import.meta.url),"/pages/admin/adminuserloginlog/index.vue":()=>e(()=>import("./BBMl127G.js"),__vite__mapDeps([14,1,2,3,7,8,9,12,13]),import.meta.url),"/pages/admin/board/edit.vue":()=>e(()=>import("./D98v7cVU.js"),__vite__mapDeps([15,2,3,1]),import.meta.url),"/pages/admin/board/index.vue":()=>e(()=>import("./BJKtYJtC.js"),__vite__mapDeps([16,2,3,1,7,8,9,10,11,12,13]),import.meta.url),"/pages/admin/config/edit.vue":()=>e(()=>import("./ChWBnVGr.js"),__vite__mapDeps([17,1,2,3]),import.meta.url),"/pages/admin/epost/edit.vue":()=>e(()=>import("./Bq1ujDbh.js"),__vite__mapDeps([18,1,2,3,19]),import.meta.url),"/pages/admin/excelimport/index.vue":()=>e(()=>import("./DQgfFnhs.js"),__vite__mapDeps([20,2,3]),import.meta.url),"/pages/admin/feedback/edit.vue":()=>e(()=>import("./CQF4jwmL.js"),__vite__mapDeps([21,1,2,3]),import.meta.url),"/pages/admin/feedback/index.vue":()=>e(()=>import("./BzDCd5bH.js"),__vite__mapDeps([22,23,2,3,1,7,8,9,10,12,13]),import.meta.url),"/pages/admin/formquerylog/index.vue":()=>e(()=>import("./ODG5hW3s.js"),__vite__mapDeps([24,23,2,3,1,7,8,9,12,13]),import.meta.url),"/pages/admin/index/index.vue":()=>e(()=>import("./Dc9n8SY3.js"),__vite__mapDeps([25,2,3]),import.meta.url),"/pages/admin/kind/edit.vue":()=>e(()=>import("./DFv2tWN4.js"),__vite__mapDeps([26,2,3,1]),import.meta.url),"/pages/admin/kind/index.vue":()=>e(()=>import("./GgDM1tNs.js"),__vite__mapDeps([27,2,3,1,7,8,9,10,11,12,13]),import.meta.url),"/pages/admin/login/index.vue":()=>e(()=>import("./k4dtWBPv.js"),__vite__mapDeps([28,2,3,29,30]),import.meta.url),"/pages/admin/ordergroup/edit.vue":()=>e(()=>import("./CyCvQXuY.js"),__vite__mapDeps([31,1,2,3]),import.meta.url),"/pages/admin/ordergroup/index.vue":()=>e(()=>import("./y9aR0iNK.js"),__vite__mapDeps([32,23,2,3,1,7,8,9,10,12,13]),import.meta.url),"/pages/admin/ordergroup/show.vue":()=>e(()=>import("./CBWOu-An.js"),__vite__mapDeps([33,1,2,3]),import.meta.url),"/pages/admin/product/edit.vue":()=>e(()=>import("./BBdYVKME.js"),__vite__mapDeps([34,1,2,3,35,36,4,5,19,8]),import.meta.url),"/pages/admin/product/index.vue":()=>e(()=>import("./Cu6CK232.js"),__vite__mapDeps([37,23,2,3,1,7,8,9,10,11,12,13]),import.meta.url),"/pages/admin/useredit/edit.vue":()=>e(()=>import("./CJyaJRfW.js"),__vite__mapDeps([38,1,2,3]),import.meta.url),"/pages/admin/viewfile/edit.vue":()=>e(()=>import("./DyBIXSDg.js"),__vite__mapDeps([39,1,2,3,19]),import.meta.url)});let u=R({}),w=y("");const x=async(o,t={},r={width:0,height:0,title:"",isCloseReload:!1})=>{u={...t},w.value=r.title||"",i.width=r.width??0,i.height=r.height??0,i.isCloseReload=r.isCloseReload??!1,I.value<768&&(i.width=0);try{if(o.includes("://")){const _=new URL(o);u={...t,...Object.fromEntries(_.searchParams.entries())},o=_.pathname.replace(/^\/+/,"")}const n=`/pages/${o.split("/").length===2?`${o}/index`:o}.vue`;if(f[n])m.value=await F(f[n]),s.value=!0;else throw new Error(`無法找到檔案: ${n}`)}catch(d){d instanceof Error?L.message(`Dialog Error: ${d.message}`,"error"):L.message("發生未知錯誤","error")}},D=async o=>{m.value=null,s.value=!1,T("closed-dialog",o)};O({open:x});const p=o=>{if(o.key==="PageUp"){o.preventDefault();const t=document.querySelector(".sync-dialog__div");t&&t.scrollBy(0,-window.innerHeight)}else if(o.key==="PageDown"){o.preventDefault();const t=document.querySelector(".sync-dialog__div");t&&t.scrollBy(0,window.innerHeight)}},b=()=>{s.value&&window.addEventListener("keydown",p)},P=()=>{window.removeEventListener("keydown",p)};return B(s,o=>{o?b():P()}),U(()=>{P()}),(o,t)=>{const r=$,d=N;return v(),g(W,{to:"body"},[E(d,{class:V(i.width==0?"full-screen":""),width:i.width,modelValue:s.value,"onUpdate:modelValue":t[0]||(t[0]=c=>s.value=c),"destroy-on-close":"","close-on-click-modal":"",center:"",tabindex:"0",onKeydown:p,onClose:t[1]||(t[1]=()=>D(i.isCloseReload))},{header:l(({close:c,titleId:n,titleClass:_})=>[a("div",G,[a("span",{id:n,class:V(_)},Q(h(w)),11,J),t[2]||(t[2]=a("button",{type:"button",class:"el-dialog__headerbtn","aria-label":"Close"},[a("i",{class:"el-icon el-icon-close"})],-1))])]),default:l(()=>[a("div",{class:"sync-dialog__div",style:q(i.height!=0?"height:"+i.height+"px":"")},[(v(),g(K,null,{default:l(()=>[a("div",M,[(v(),g(z(m.value),H(h(u),{onClosedDialog:D}),null,16))])]),fallback:l(()=>[a("div",X,[E(r,{class:"is-loading"},{default:l(()=>[E(h(j))]),_:1}),t[3]||(t[3]=a("span",null,"Loading...",-1))])]),_:1}))],4)]),_:1},8,["class","width","modelValue"])])}}});export{te as _};
