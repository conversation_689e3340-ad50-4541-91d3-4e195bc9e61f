<?php

namespace App\Mails;

use PF;
use PT;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Swift_SmtpTransport;
use Swift_Mailer;
use Mail;

class baseMail extends Mailable {

    use Queueable;
    use SerializesModels;


    public function __construct() {
    }
    public function setConfig($kindheadid) {

        // $rs = PT::getKindhead($kindheadid);



        // if ($rs->smtp_host != "") {

        //     $transport = new Swift_SmtpTransport($rs->smtp_host, $rs->smtp_port, $rs->smtp_encryption);
        //     $transport->setUsername($rs->smtp_username);
        //     $transport->setPassword($rs->smtp_passowrd);

        //     $mailer = new Swift_Mailer($transport);
        //     Mail::setSwiftMailer($mailer);
        // }
    }
    public function send($mailer) {
        try {

            parent::send($mailer);
        } catch (\Exception $e) {
            //$items=explode("chr(13).chr(10)",$s);
            $items = explode(";", $this->to);
            foreach ($items as $k => $v) {
                $inputs = [];
                $inputs['mobile'] = $v;
                $inputs['msg'] = $e->getMessage();
                $id = \App\Models\smslog::create($inputs)->id;
            };

            //Log::error('郵件發送失敗: ' . $e->getMessage());
            // 可以在这里添加其他错误处理逻辑
        }
    }
}
