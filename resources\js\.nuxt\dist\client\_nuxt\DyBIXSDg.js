import{_ as x}from"./jBNyKXjo.js";import{d as B,f as q,u as C,a as F,r as f,j as I,Q as N,k as j,c as D,o as a,l as d,w as L,v as P,e as R,B as u,m,b as T,E as U,C as $,n as y,T as H,I as M,F as O,y as s}from"./D_zVjEfm.js";import{_ as Q}from"./2eTUCwpK.js";const S={align:"center"},J=B({__name:"edit",props:["edit","type"],setup(_){var p;const l=q();C();const r=F();f([]);const o=I({type:"",body:"",edit:((p=r.query)==null?void 0:p.edit)||""}),c=async()=>{try{if(typeof o.edit>"u"||o.edit=="")return;let t=await l.post("api/admin/viewfile/show",o);t.resultcode=="0"?Object.keys(t.data).forEach(e=>{o[e]=t.data[e]}):s.alert(t.resultmessage)}catch(t){s.alert(t),console.error(t)}finally{}};N(()=>r.fullPath,()=>{var t,e;o.edit=((t=r.query)==null?void 0:t.edit)||"",o.type=(e=r.query)==null?void 0:e.type,c()});const n=f(null),w=async()=>{if(n.value)try{if(await n.value.validate())try{let e=await l.post("api/admin/viewfile/store",o);if(e.resultcode=="0")s.toast(e.resultmessage);else throw new Error(e.resultmessage)}catch(e){s.formElError(e),console.error(e)}}catch(t){console.error("Validation error:",t)}};return j(async()=>{c()}),(t,e)=>{const b=x,v=$,E=Q,g=U,V=H,k=M,h=P;return a(),D(O,null,[d(b,{id:o.edit},null,8,["id"]),L((a(),u(k,{ref_key:"formEl",ref:n,model:o},{default:m(()=>[d(g,{label:"內容",prop:"body",rules:[{required:!0,message:"內容 未填"}]},{default:m(()=>[_.type=="text"?(a(),u(v,{key:0,modelValue:o.body,"onUpdate:modelValue":e[0]||(e[0]=i=>o.body=i),style:{width:"98%"},type:"textarea",rows:30,placeholder:"Please input"},null,8,["modelValue"])):(a(),u(E,{key:1,modelValue:o.body,"onUpdate:modelValue":e[1]||(e[1]=i=>o.body=i)},null,8,["modelValue"]))]),_:1}),T("div",S,[d(V,{type:"primary",onClick:w},{default:m(()=>e[2]||(e[2]=[y(" 確定")])),_:1}),e[3]||(e[3]=y(" "))])]),_:1},8,["model"])),[[h,R(l).getLoading()]])],64)}}});export{J as default};
