import{d as n,f as c,j as p,k as i,c as d,o as l,y as u}from"./D_zVjEfm.js";const m=["innerHTML"],f=n({__name:"my-epost",props:{id:{type:String,default:"",required:!0}},setup(o){const s=c(),r=o,t=p({epostid:0,epostbody:""}),a=async()=>{try{const e=await s.post("api/epost/show",{id:r.id});if(e.resultcode=="0")Object.assign(t,e.data);else throw new Error(e.resultmessage)}catch(e){u.formElError(e),console.error(e)}};return i(async()=>{a()}),(e,_)=>(l(),d("span",{innerHTML:t.epostbody},null,8,m))}});export{f as _};
