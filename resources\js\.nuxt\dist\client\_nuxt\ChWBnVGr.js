import{_ as k}from"./jBNyKXjo.js";import{d as x,f as B,i as C,u as F,r as c,k as D,c as l,o as a,l as d,w as N,v as S,e as I,B as L,m as u,b as r,F as p,q as H,t as _,E as M,s as U,C as $,n as f,H as q,I as R,y as m}from"./D_zVjEfm.js";const T={class:"form-group row"},j={class:"col-md-2"},z={class:"col-md-10"},A={key:0},G={align:"center"},Q=x({__name:"edit",setup(J){const i=B();C(),F();const s=c([]),n=c(null),v=async()=>{try{let t=await i.post("api/admin/config");if(t.resultcode=="0")s.value=t.data;else throw new Error(t.resultmessage)}catch(t){m.formElError(t),console.error(t)}finally{}},y=async()=>{if(n.value)try{if(await n.value.validate())try{let e=await i.post("api/admin/config/store",s.value);if(e.resultcode=="0")m.toast(e.resultmessage);else throw new Error(e.resultmessage)}catch(e){m.formElError(e),console.error(e)}}catch(t){console.error("Validation error:",t)}};return D(async()=>{v()}),(t,e)=>{const g=k,w=$,E=M,b=R,V=S;return a(),l(p,null,[d(g,{id:"config"}),N((a(),L(b,{ref_key:"formEl",ref:n,width:"98%",model:s.value,onSubmit:q(y,["prevent"])},{default:u(()=>[(a(!0),l(p,null,H(s.value,(o,K)=>(a(),l("div",T,[r("div",j,_(o.title),1),r("div",z,[d(E,{prop:"rs.name"},{default:u(()=>[d(w,{modelValue:o.value,"onUpdate:modelValue":h=>o.value=h,type:"text"},null,8,["modelValue","onUpdate:modelValue"]),o.title!=o.memo?(a(),l("span",A,_(o.memo),1)):U("",!0)]),_:2},1024)])]))),256)),r("div",G,[e[1]||(e[1]=r("button",{type:"submit",class:"btn btn-primary"},"確定",-1)),e[2]||(e[2]=f("   ")),r("button",{type:"reset",class:"btn btn-secondary",onClick:e[0]||(e[0]=o=>n.value.resetFields())},"取消"),e[3]||(e[3]=f("   "))])]),_:1},8,["model"])),[[V,I(i).getLoading()]])],64)}}});export{Q as default};
