const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./DEPTJ9I4.js","./D_zVjEfm.js","./entry.BGE7dmog.css","./AII-GxSx.js","./ewbPcaBc.js","./DeQTbgbd.js","./MMxUCDix.js","./DIRD0ar_.js","./DZ_RHEX4.js","./B2ebk17F.js","./DbPQOmG-.js"])))=>i.map(i=>d[i]);
import{d as z,f as G,u as W,a as Y,i as Z,r as j,j as E,Q as ee,k as te,L as d,y as p,w as $,v as ae,e as m,c as l,o as n,l as c,b as r,s as I,J as oe,K as ne,F as f,q as x,t as k,B as re,n as w}from"./D_zVjEfm.js";import{_ as se}from"./jBNyKXjo.js";import le from"./sXr78Ku-.js";import{_ as ie}from"./_cPjsZyH.js";import{_ as de}from"./CQvHppDm.js";import me from"./HHVucgb9.js";import{_ as ce}from"./CF83mHfB.js";import"./G4Mf9Qin.js";const ue={class:"d-flex justify-content-between"},_e={key:0},pe={class:"table-responsive-md"},fe={class:"table table-striped table-hover table-bordered table-fixed"},he={align:"center",width:"90"},ge=["onClick"],ve={valign:"top",title:"Edit",align:"center"},ye=["onClick"],Le=z({__name:"index",props:{kind:{default:""}},setup(L){const v=G();W();const N=Y(),D=Z(),V=j(""),O=E({}),P=E({}),u=L;let h=E({data:[]});const y=j([]);j("");const a=E({search:"",searchname:"",searchdatename:"",page:1,sortname:"",sorttype:"",del:[],kind:u.kind});function S(e){a.page=e,_()}const B=async e=>{a.page=1,a.sortname=e,a.sorttype=a.sorttype=="asc"?"desc":"asc",await _()},M=async e=>{a.page=1,a.sortname="",Object.assign(a,e),await _()},U=(e=!1)=>{e&&_()},q=async e=>{h.value.data.forEach(t=>{t.del=e?t.id:0})},F=async()=>{try{a.del=h.value.data.filter(t=>t.del!==0).map(t=>t.del).filter(t=>t!=null)||[];let e=await v.post("api/admin/adminuser/destroy",a);if(e.resultcode=="0")p.toast(e.resultmessage),_();else throw new Error(e.resultmessage)}catch(e){p.formElError(e),console.error(e)}finally{}},_=async()=>{try{v.setLoading(!0);let e=await v.post("api/admin/board",a);if(e.resultcode=="0")Object.assign(h,e.data);else throw new Error(e.resultmessage)}catch(e){p.formElError(e),console.error(e)}finally{}};ee(()=>u.kind,async e=>{a.kind=u.kind,H(),_()},{deep:!0,immediate:!0}),te(async()=>{console.log(["props.kind",u])});async function H(){try{const e=Object.assign({"/datas/aboutpage.js":()=>d(()=>import("./DEPTJ9I4.js"),__vite__mapDeps([0,1,2]),import.meta.url),"/datas/all.js":()=>d(()=>import("./AII-GxSx.js"),__vite__mapDeps([3,1,2]),import.meta.url),"/datas/banner.js":()=>d(()=>import("./ewbPcaBc.js"),__vite__mapDeps([4,1,2]),import.meta.url),"/datas/case.js":()=>d(()=>import("./DeQTbgbd.js"),__vite__mapDeps([5,1,2]),import.meta.url),"/datas/customer.js":()=>d(()=>import("./MMxUCDix.js"),__vite__mapDeps([6,1,2]),import.meta.url),"/datas/news.js":()=>d(()=>import("./DIRD0ar_.js"),__vite__mapDeps([7,1,2]),import.meta.url),"/datas/productkind.js":()=>d(()=>import("./DZ_RHEX4.js"),__vite__mapDeps([8,1,2]),import.meta.url),"/datas/service.js":()=>d(()=>import("./B2ebk17F.js"),__vite__mapDeps([9,1,2]),import.meta.url),"/datas/serviceitem.js":()=>d(()=>import("./DbPQOmG-.js"),__vite__mapDeps([10,1,2]),import.meta.url)}),t=`/datas/${N.query.kind}.js`;if(e[t])e[t]().then(g=>{y.value=g.fields._rawValue.filter(i=>i.islist==!0),y.value.forEach(i=>{if(i&&i.issearch===!0){let b=i.name;i.kind=="my-dateform"?P[b]=i.label:O[b]=i.label}})}).catch(g=>{throw new Error(g.message)});else throw new Error(`Module ${t} does not exist`)}catch(e){p.formElError(e)}}return(e,t)=>{var A,T;const g=se,i=le,b=ie,J=de,R=oe,K=me,Q=ae;return $((n(),l("div",null,[c(g,{id:L.kind,refs:"breadcrumb"},null,8,["id"]),c(i,{searchNames:O,searchDateNames:P,onOnSearch:M},null,8,["searchNames","searchDateNames"]),r("div",ue,[[999].includes((A=m(D).adminuser)==null?void 0:A.role)?(n(),l("div",_e,[c(b,{onClick:F})])):I("",!0),t[2]||(t[2]=r("div",null,null,-1)),r("div",null,[c(J,{onClick:t[0]||(t[0]=o=>V.value.open("admin/board/edit",{...u},0))})])]),r("div",pe,[r("table",fe,[r("thead",null,[r("tr",null,[r("th",he,[$(c(R,{onChange:q},null,512),[[ne,[999].includes((T=m(D).adminuser)==null?void 0:T.role)]])]),(n(!0),l(f,null,x(y.value,(o,X)=>(n(),l("th",{width:"",onClick:C=>B(o.name),class:"sortable"},k(o.label),9,ge))),256))])]),r("tbody",null,[(n(!0),l(f,null,x(m(h).data,(o,X)=>{var C;return n(),l("tr",null,[r("td",ve,[[999].includes((C=m(D).adminuser)==null?void 0:C.role)?(n(),re(R,{key:0,modelValue:o.del,"onUpdate:modelValue":s=>o.del=s,"true-value":o.id},null,8,["modelValue","onUpdate:modelValue","true-value"])):I("",!0),t[4]||(t[4]=w("   ")),r("button",{type:"button",class:"btn btn-success btn-sm",onClick:s=>V.value.open("admin/board/edit",{...u,edit:o.id},{})},t[3]||(t[3]=[r("i",{class:"fa fa-edit","aria-hidden":"true"},null,-1)]),8,ye)]),(n(!0),l(f,null,x(y.value,(s,be)=>(n(),l("td",null,[s.kind=="my-xmlform"?(n(),l(f,{key:0},[w(k(m(p).getXmlSearch(s.label,o[s.name])),1)],64)):s.kind=="my-dateform"?(n(),l(f,{key:1},[w(k(m(p).formatDate(o[s.name])),1)],64)):(n(),l(f,{key:2},[w(k(typeof s.listname<"u"?o[s.listname]:o[s.name]),1)],64))]))),256))])}),256))])])]),c(K,{total:m(h).total,page:a.page,"onUpdate:page":t[1]||(t[1]=o=>a.page=o),onCurrentChange:S},null,8,["total","page"]),c(ce,{ref_key:"myDialog",ref:V,onClosedDialog:U},null,512)])),[[Q,m(v).getLoading()]])}}});export{Le as default};
