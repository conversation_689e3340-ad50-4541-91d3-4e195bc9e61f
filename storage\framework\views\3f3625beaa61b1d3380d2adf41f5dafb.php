<!doctype html>
<html lang="<?php echo e(app()->getLocale()); ?>">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="content-Language" content="<?php echo e(app()->getLocale()); ?>" />

    <title><?php echo e($title != '' ? $title . ' | ' . PF::getConfig('title') : PF::getConfig('title')); ?></title>
    <meta name="keywords" content="<?php echo e($keyword != '' ? $keyword : PF::getConfig('keyword')); ?>" />
    <meta name="description" content="<?php echo e($description != '' ? $description : PF::getConfig('description')); ?>" />
    <meta name="copyright" content="<?php echo e(PF::getConfig('name')); ?>" />
    <meta name="distribution" content="Taiwan" />
    <meta name="revisit-after" content="1 days" />
    <meta name="robots" content="index,follow" />
    <!--FaceBook-->
    <meta property="og:title"
        content="<?php echo e($title != '' ? $title . ' | ' . PF::getConfig('title') : PF::getConfig('title')); ?>" />
    <meta property="og:image" content="<?php echo e($img); ?>" />
    <meta property="og:url" content="<?php echo e(Request::url()); ?>" />
    <meta property="og:site_name" content="<?php echo e(PF::getConfig('title')); ?>" />
    <meta property="og:description" content="<?php echo e($description != '' ? $description : PF::getConfig('description')); ?>" />
    <meta property="og:type" content="website" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Cache-Control" content="private" />
    <meta http-equiv="Expires" content="0" />
    <link rel='index' title="<?php echo e(PF::getConfig('title')); ?>" href="<?php echo e(str_replace(' /public', '/', url('/'))); ?>" />
    <link rel="canonical" href="<?php echo e(str_replace(' /public/', '/', Request::url())); ?>" />
    <meta content="no" http-equiv="imagetoolbar" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="HandheldFriendly" content="True" />

    <link rel="icon" href="<?php echo e(url('/')); ?>/images/favicon.ico">

    <meta name="viewport"
        content="width=device-width, initial-scale=1.0, maximum-scale=1, minimum-scale=1, user-scalable=no, minimal-ui,shrink-to-fit=no">



    <?php echo $head; ?>


    <?php echo $ld_json; ?>


</head>

<body leftmargin="0" topmargin="0" marginwidth="0" marginheight="0" class="xs">

    <?php if($title != ''): ?>
        <noscript>
            <header>
                <h1><?php echo e($title); ?></h1>
            </header>
            <section>
                <?php echo e($description != '' ? $description : PF::getConfig('description')); ?>

                <?php echo $__env->yieldContent('content'); ?>
            </section>

        </noscript>
    <?php endif; ?>

    <?php echo $body; ?>




    <?php echo $__env->make('layouts.debug', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
</body>

</html>
<?php /**PATH C:\AppServ\laravel\oyatt\resources\views/index.blade.php ENDPATH**/ ?>