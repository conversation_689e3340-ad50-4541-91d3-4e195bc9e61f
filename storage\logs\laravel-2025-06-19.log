[2025-06-19 21:13:43] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 21:13:45] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 21:13:47] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/serviceitem","post":[],"raw":[]} 
[2025-06-19 21:13:47] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 21:13:47] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/news","post":{"pagesize":6},"raw":{"pagesize":6}} 
[2025-06-19 21:13:47] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 21:13:48] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":[],"raw":[]} 
[2025-06-19 21:13:48] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'serviceitem')
  [執行超過10秒:7.69]
  
[2025-06-19 21:13:48] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
  [執行超過10秒:7.41]
  
[2025-06-19 21:13:48] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:7.64]
  
[2025-06-19 21:13:48] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:7.54]
  
[2025-06-19 21:13:48] dev.INFO: 
select id,title,field1 as img,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'serviceitem')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.56]
  
[2025-06-19 21:13:48] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.06]
  
[2025-06-19 21:13:48] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate,body from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
 order by boardsort desc limit 6 offset 0
  [執行超過10秒:1.2]
  
[2025-06-19 21:13:48] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:0.94]
  
[2025-06-19 21:13:48] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:7.25]
  
[2025-06-19 21:13:48] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.22]
  
[2025-06-19 21:13:48] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/banner","post":[],"raw":[]} 
[2025-06-19 21:13:48] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'banner')
  [執行超過10秒:29.04]
  
[2025-06-19 21:13:48] dev.INFO: 
select * from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'banner')
 order by boardsort limit 999 offset 0
  [執行超過10秒:1.82]
  
[2025-06-19 21:13:49] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 21:13:49] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:6.46]
  
[2025-06-19 21:14:17] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 21:14:17] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 21:14:17] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:15.59]
  
[2025-06-19 21:14:17] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1]
  
[2025-06-19 21:14:17] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:23.4]
  
[2025-06-19 21:14:17] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:0.93]
  
[2025-06-19 21:14:17] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 21:14:17] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:19.64]
  
[2025-06-19 21:14:47] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 21:14:49] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 21:14:49] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:13.79]
  
[2025-06-19 21:14:49] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:1.49]
  
[2025-06-19 21:14:49] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.94]
  
[2025-06-19 21:14:49] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.63]
  
[2025-06-19 21:14:50] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:2.17]
  
[2025-06-19 21:14:50] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:1.34]
  
[2025-06-19 21:14:50] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:1.83]
  
[2025-06-19 21:14:50] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:1.33]
  
[2025-06-19 21:14:50] dev.INFO: 
select * from `feedback` limit 1
  [執行超過10秒:1.45]
  
[2025-06-19 21:14:50] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:1.38]
  
[2025-06-19 21:14:50] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:1.46]
  
[2025-06-19 21:14:50] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:1.48]
  
[2025-06-19 21:14:50] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:1.34]
  
[2025-06-19 21:14:50] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.56]
  
[2025-06-19 21:14:50] dev.INFO: 
select * from `orderdetail` limit 1
  [執行超過10秒:1.67]
  
[2025-06-19 21:14:50] dev.INFO: 
select * from `ordergroup` limit 1
  [執行超過10秒:1.65]
  
[2025-06-19 21:14:50] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:1.74]
  
[2025-06-19 21:14:50] dev.INFO: 
select * from `product` limit 1
  [執行超過10秒:1.65]
  
[2025-06-19 21:14:52] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 21:14:52] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:10.04]
  
[2025-06-19 21:14:52] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 21:14:52] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:2.21]
  
[2025-06-19 21:14:52] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 21:14:52] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:12.56]
  
[2025-06-19 21:14:52] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 21:14:52] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:10.18]
  
[2025-06-19 21:14:52] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:1.74]
  
[2025-06-19 21:14:52] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:14.48]
  
[2025-06-19 21:14:52] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.61]
  
[2025-06-19 21:14:52] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 21:14:52] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:10.94]
  
[2025-06-19 21:14:52] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 21:14:52] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:8.28]
  
[2025-06-19 21:14:52] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:2.57]
  
[2025-06-19 21:14:59] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 21:14:59] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 21:14:59] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:12.25]
  
[2025-06-19 21:14:59] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:35.77]
  
[2025-06-19 21:19:48] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 21:19:50] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 21:19:52] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 21:19:52] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:31.72]
  
[2025-06-19 21:19:52] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.23]
  
[2025-06-19 21:19:52] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 21:19:52] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 21:19:52] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:11.05]
  
[2025-06-19 21:19:52] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 21:19:52] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:2.05]
  
[2025-06-19 21:19:52] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:21.97]
  
[2025-06-19 21:19:52] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:2.17]
  
[2025-06-19 21:19:52] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 21:19:52] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:23.1]
  
[2025-06-19 21:19:52] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 21:19:52] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:11.52]
  
[2025-06-19 21:19:52] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.78]
  
[2025-06-19 21:19:52] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:25.09]
  
[2025-06-19 21:21:30] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 21:21:30] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 21:21:30] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:14.95]
  
[2025-06-19 21:21:31] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:26.12]
  
[2025-06-19 21:21:51] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 21:21:53] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 21:21:56] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 21:21:56] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:14.82]
  
[2025-06-19 21:21:56] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:2.5]
  
[2025-06-19 21:21:56] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 21:21:56] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:13.61]
  
[2025-06-19 21:21:56] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:2.86]
  
[2025-06-19 21:21:56] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 21:21:56] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 21:21:57] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:17.5]
  
[2025-06-19 21:21:57] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:2.9]
  
[2025-06-19 21:21:57] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:20.45]
  
[2025-06-19 21:21:57] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:3.66]
  
[2025-06-19 21:21:57] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 21:21:57] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 21:21:57] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:15.17]
  
[2025-06-19 21:21:57] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:20.8]
  
[2025-06-19 21:21:58] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 21:21:58] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:30.72]
  
[2025-06-19 21:21:59] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 21:21:59] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:27.21]
  
[2025-06-19 21:22:02] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 21:22:02] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 21:22:02] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:34.9]
  
[2025-06-19 21:22:02] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:12.23]
  
[2025-06-19 21:22:03] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 21:22:04] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:17.49]
  
[2025-06-19 21:22:04] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 21:22:04] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:13.16]
  
[2025-06-19 21:22:05] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 21:22:05] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:8.25]
  
[2025-06-19 21:22:05] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 21:22:05] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:30.43]
  
[2025-06-19 21:47:31] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 21:47:33] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 21:47:34] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 21:47:36] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 21:47:36] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:4.73]
  
[2025-06-19 21:47:36] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:0.74]
  
[2025-06-19 21:47:36] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 21:47:36] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 21:47:36] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:4.93]
  
[2025-06-19 21:47:36] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:0.77]
  
[2025-06-19 21:47:36] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 21:47:36] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 21:47:36] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:20.71]
  
[2025-06-19 21:47:36] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 21:47:36] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:19.01]
  
[2025-06-19 21:47:36] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:0.9]
  
[2025-06-19 21:47:36] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:27.85]
  
[2025-06-19 21:47:36] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:0.96]
  
[2025-06-19 21:47:36] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:27.08]
  
[2025-06-19 21:49:45] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 21:49:46] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 21:49:56] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/adminlogin","post":{"account":"allen","password":"aaa1234","iskeep":true,"google_recaptcha_token":"03AFcWeA7DpxfkdXvvz3maq45MKgDbNfT3qJktb9ojXsGo2_6ddYOfdZXqiWBU8Y5NXoOdZkgOPTZKr7PiO3UvK51eAXIMoEGqRXVsr0vM1h1TryLs0bIfcIu0pBGbmY87leiDZxh8530G6R2J4DSGq8VfYx3nrGcS8DgP3KSezMSKWQYzVGCIqA6iYKCrp1j836nrk7MV7u5G4i9QfR6JkMUiFEMxO5u38YlJDS1pMaGClpwKrf6wpi4T19fWNSSU4rKULas7Wi6ljiP1G3Tx_IKp0PcIbQoweUveO56RMM_6Xi7RLcLcAycUSj-Wz2JNEO6BBLXZXEwITDeJpXIRQWbSKv0L_D6sHlZWwmcmGsMIt8gLtMEbh4vr-QP3AqvssgH7LUZzXVvny8IOB3CIGdrhm4fM0v0ADRmbt3OiZiHgsHB2seeY4ZQ4v5lSZKMGLVubEFIC9V44UGLzOV3q3IgcrjdbgVwYqGzDxTSaAgjT6He8lHZtN_Dr1OhT3K_Kskiames2YnbfIdWMBZZBk8Xfp_5L-1vfa-kFMdKNCdT6BXWajhu85hOB2pBm8Vh4nluGibThvQ-QMvfet1GxZhWWkVfGzyfNe0KE84lXj5zH5ox2czFpMvZAPSLx8giIGcwEC58SOXn9PIoNRN_yILVY9e1gwA6Vv7u5RYKglVtVmbRng5WLOrXkH0soaW-7DVIdg5XZSRukoYIQAOangGEwxd-ZR93gKcm5ZRo3-Hj-RS4s70Uosj27VFmIsmRuzkc7XAoVEitBN1wMkEGmn5iqDE2Vyc3a4RkgYYazDIzL2e3UC8v2zrkjUccXafU85FByWCGeOIf91CYxb8P7RwYCndNIeZAY2zyKBMJ4gwmI89f-LMgxaidY8_bAdhM_50gFMvbJeeHQKqyRuh57-qm0r0wvzv9GuCSkuRrz2OA67uSEvZZE70Y6Cvv_cnj717gIf2xOYvtgb_8k87anyKDYh2LYg-hv9iZu7oqR5wnI9uMa7sQoJjeX_JLdcM56R5bPN-Z7sz4latttJXXy4Ca7olpLIBHeWxUT-NQiAOLVwwx1hCyrn-XcfBwQo25hyT6ODe2zAeH9TnTRFIbM_Fq-2Fk1PSiyg-1iWFKZLJ2cvH_qiySY97W4wVcHmLAZFRJl4F389wLK9aajXAFXpyglRwexrbWkVi2FlU11-ADCj1aQevTzuxaxKbLvLCu5TwA9XrPZRwuE_IjrGGX9eR_zRaJwYGaqCNUjp2ZQle7bFxHCn1Gi2YXibcb1RdZMMBHD1IT0QC0YWZjNR9eOjfJPmGxNRnqZOVgNsv2wEOFE7xXyt5JGDL2wsexz6BJEns-JS59XKDJOFFQKsHN9LfPcabYe_EiOPWZpSeT6mgMysVPhB2IQK4pD3Y4b3Ne3KNX5Su04rQSWRdOjmUerl6Ls3Tw5VEVSYxIfZ3LNOgbzT80e7DAsnDncXjt5rhpWEYroG1egC3oFkbrE4YsJQJqCh6LziEqRjh4oNh3kkoojwZaekcmkOhCLO2-hmZRlfR-_6U5I1hv89ZdCw6F1M5M6JNJVsk0sATI5FdncI2VfxPa2vo7_sFYYfgFX55m24lpeMFRomViW-FgF0bGQ25cv6_T4_jQOVScBVOM_bscPIh9e8nP_T3tivmOxwd7pQb7EUeaThQ5pf463HMRY5YykYoYOJknO8gvYFDfjkteOMhoyOWSdik-XGuypSCARV2juet1YIl2HK7qRJn7GMgVxd15fogZq1gPe2SmMHFNHNrMbfySdJPWAp9XNqG2rsAAwWWcw87vzforJ8z_rKkMtNB_fCAiy8uR5kmeAa5Gqca8LPHXJfjQ50GfE0V3U-QrohLDURCSY7yB3yPaDr0U1nm1V4lbVtwg0EXI33XKPv5e6ESkIlCXik2JAmhQ4OyFv_Szp_CweTeAbn_-fhL1TxX99BH5RRbV-G94QvHhBJdWEKFlyM02wle408JTmFCuBWChg8RuIe46XrbQKHSIWckuW2LPAKQ0sw_UFDrbLaQ5GpqtYroo3csz4-0w6jm4rQCPndzF8"},"raw":{"account":"allen","password":"aaa1234","iskeep":true,"google_recaptcha_token":"03AFcWeA7DpxfkdXvvz3maq45MKgDbNfT3qJktb9ojXsGo2_6ddYOfdZXqiWBU8Y5NXoOdZkgOPTZKr7PiO3UvK51eAXIMoEGqRXVsr0vM1h1TryLs0bIfcIu0pBGbmY87leiDZxh8530G6R2J4DSGq8VfYx3nrGcS8DgP3KSezMSKWQYzVGCIqA6iYKCrp1j836nrk7MV7u5G4i9QfR6JkMUiFEMxO5u38YlJDS1pMaGClpwKrf6wpi4T19fWNSSU4rKULas7Wi6ljiP1G3Tx_IKp0PcIbQoweUveO56RMM_6Xi7RLcLcAycUSj-Wz2JNEO6BBLXZXEwITDeJpXIRQWbSKv0L_D6sHlZWwmcmGsMIt8gLtMEbh4vr-QP3AqvssgH7LUZzXVvny8IOB3CIGdrhm4fM0v0ADRmbt3OiZiHgsHB2seeY4ZQ4v5lSZKMGLVubEFIC9V44UGLzOV3q3IgcrjdbgVwYqGzDxTSaAgjT6He8lHZtN_Dr1OhT3K_Kskiames2YnbfIdWMBZZBk8Xfp_5L-1vfa-kFMdKNCdT6BXWajhu85hOB2pBm8Vh4nluGibThvQ-QMvfet1GxZhWWkVfGzyfNe0KE84lXj5zH5ox2czFpMvZAPSLx8giIGcwEC58SOXn9PIoNRN_yILVY9e1gwA6Vv7u5RYKglVtVmbRng5WLOrXkH0soaW-7DVIdg5XZSRukoYIQAOangGEwxd-ZR93gKcm5ZRo3-Hj-RS4s70Uosj27VFmIsmRuzkc7XAoVEitBN1wMkEGmn5iqDE2Vyc3a4RkgYYazDIzL2e3UC8v2zrkjUccXafU85FByWCGeOIf91CYxb8P7RwYCndNIeZAY2zyKBMJ4gwmI89f-LMgxaidY8_bAdhM_50gFMvbJeeHQKqyRuh57-qm0r0wvzv9GuCSkuRrz2OA67uSEvZZE70Y6Cvv_cnj717gIf2xOYvtgb_8k87anyKDYh2LYg-hv9iZu7oqR5wnI9uMa7sQoJjeX_JLdcM56R5bPN-Z7sz4latttJXXy4Ca7olpLIBHeWxUT-NQiAOLVwwx1hCyrn-XcfBwQo25hyT6ODe2zAeH9TnTRFIbM_Fq-2Fk1PSiyg-1iWFKZLJ2cvH_qiySY97W4wVcHmLAZFRJl4F389wLK9aajXAFXpyglRwexrbWkVi2FlU11-ADCj1aQevTzuxaxKbLvLCu5TwA9XrPZRwuE_IjrGGX9eR_zRaJwYGaqCNUjp2ZQle7bFxHCn1Gi2YXibcb1RdZMMBHD1IT0QC0YWZjNR9eOjfJPmGxNRnqZOVgNsv2wEOFE7xXyt5JGDL2wsexz6BJEns-JS59XKDJOFFQKsHN9LfPcabYe_EiOPWZpSeT6mgMysVPhB2IQK4pD3Y4b3Ne3KNX5Su04rQSWRdOjmUerl6Ls3Tw5VEVSYxIfZ3LNOgbzT80e7DAsnDncXjt5rhpWEYroG1egC3oFkbrE4YsJQJqCh6LziEqRjh4oNh3kkoojwZaekcmkOhCLO2-hmZRlfR-_6U5I1hv89ZdCw6F1M5M6JNJVsk0sATI5FdncI2VfxPa2vo7_sFYYfgFX55m24lpeMFRomViW-FgF0bGQ25cv6_T4_jQOVScBVOM_bscPIh9e8nP_T3tivmOxwd7pQb7EUeaThQ5pf463HMRY5YykYoYOJknO8gvYFDfjkteOMhoyOWSdik-XGuypSCARV2juet1YIl2HK7qRJn7GMgVxd15fogZq1gPe2SmMHFNHNrMbfySdJPWAp9XNqG2rsAAwWWcw87vzforJ8z_rKkMtNB_fCAiy8uR5kmeAa5Gqca8LPHXJfjQ50GfE0V3U-QrohLDURCSY7yB3yPaDr0U1nm1V4lbVtwg0EXI33XKPv5e6ESkIlCXik2JAmhQ4OyFv_Szp_CweTeAbn_-fhL1TxX99BH5RRbV-G94QvHhBJdWEKFlyM02wle408JTmFCuBWChg8RuIe46XrbQKHSIWckuW2LPAKQ0sw_UFDrbLaQ5GpqtYroo3csz4-0w6jm4rQCPndzF8"}} 
[2025-06-19 21:49:56] dev.INFO: 
select * from `adminuser` 
where
 `account` = 'allen'
 and `online` = '1' limit 1
  [執行超過10秒:20.37]
  
[2025-06-19 21:49:57] dev.INFO: 
insert into `personal_access_tokens` (`name`, `token`, `abilities`, `expires_at`, `tokenable_id`, `tokenable_type`, `updated_at`, `created_at`) values ('admin', '363352ee5a04cff066aee239d309d4ac9f8feae23c5f89d041f100712131a319', '["*"]', '2025-06-20 21:49:57', '2', 'App\Models\adminuser', '2025-06-19 21:49:57', '2025-06-19 21:49:57')
  [執行超過10秒:3.91]
  
[2025-06-19 21:49:57] dev.INFO: 
update `adminuser` set `lastlogin_dt` = '2025-06-19 21:49:57', `adminuser`.`updated_at` = '2025-06-19 21:49:57' 
where
 `id` = '2'
  [執行超過10秒:3.24]
  
[2025-06-19 21:49:57] dev.INFO: 
insert into `adminuserloginlog` (`account`, `clientip`, `created_at`, `loginstatus`) values ('開發工程師', '127.0.0.1', '2025-06-19 21:49:57', '成功')
  [執行超過10秒:2.72]
  
[2025-06-19 21:49:59] dev.INFO: 
select * from `personal_access_tokens` 
where
 `personal_access_tokens`.`id` = '10' limit 1
  [執行超過10秒:19.51]
  
[2025-06-19 21:49:59] dev.INFO: 
select * from `personal_access_tokens` 
where
 `personal_access_tokens`.`id` = '10' limit 1
  [執行超過10秒:25.04]
  
[2025-06-19 21:49:59] dev.INFO: 
select * from `adminuser` 
where
 `adminuser`.`id` = '2' limit 1
  [執行超過10秒:1.66]
  
[2025-06-19 21:49:59] dev.INFO: 
select * from `adminuser` 
where
 `adminuser`.`id` = '2' limit 1
  [執行超過10秒:1.62]
  
[2025-06-19 21:49:59] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer 10|TipZpZicbv3uXeEUicC1TcL3t1jIAm7rquQx7Alt82cc864a","CONTENT_TYPE":"application/json"},"url":"/api/admin/main","post":{"middlewareurl":"https://allennb.com.tw/admin/api/"},"raw":[]} 
[2025-06-19 21:49:59] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer 10|TipZpZicbv3uXeEUicC1TcL3t1jIAm7rquQx7Alt82cc864a","CONTENT_TYPE":"application/json"},"url":"/api/admin/adminuserloginlog","post":{"search":null,"searchname":null,"searchdatename":null,"page":1,"sortname":null,"sorttype":null,"del":[],"middlewareurl":"https://allennb.com.tw/admin/api/"},"raw":{"search":"","searchname":"","searchdatename":"","page":1,"sortname":"","sorttype":"","del":[]}} 
[2025-06-19 21:49:59] dev.INFO: 
select count(*) as aggregate from `adminuserloginlog`
  [執行超過10秒:1.22]
  
[2025-06-19 21:49:59] dev.INFO: 
select adminuserloginlog.* from `adminuserloginlog`
 order by adminuserloginlog.id desc limit 10 offset 0
  [執行超過10秒:0.62]
  
[2025-06-19 21:50:05] dev.INFO: 
select * from `personal_access_tokens` 
where
 `personal_access_tokens`.`id` = '10' limit 1
  [執行超過10秒:14.31]
  
[2025-06-19 21:50:05] dev.INFO: 
select * from `adminuser` 
where
 `adminuser`.`id` = '2' limit 1
  [執行超過10秒:1.39]
  
[2025-06-19 21:50:05] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer 10|TipZpZicbv3uXeEUicC1TcL3t1jIAm7rquQx7Alt82cc864a","CONTENT_TYPE":"application/json"},"url":"/api/admin/epost/show","post":{"edit":"homebottom","middlewareurl":"https://allennb.com.tw/admin/api/"},"raw":{"edit":"homebottom"}} 
[2025-06-19 21:50:05] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:0.64]
  
[2025-06-19 21:50:59] dev.INFO: 
select * from `personal_access_tokens` 
where
 `personal_access_tokens`.`id` = '10' limit 1
  [執行超過10秒:4.24]
  
[2025-06-19 21:50:59] dev.INFO: 
select * from `adminuser` 
where
 `adminuser`.`id` = '2' limit 1
  [執行超過10秒:1.21]
  
[2025-06-19 21:50:59] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer 10|TipZpZicbv3uXeEUicC1TcL3t1jIAm7rquQx7Alt82cc864a","CONTENT_TYPE":"application/json"},"url":"/api/admin/epost/show","post":{"edit":"homebottom","middlewareurl":"https://allennb.com.tw/admin/api/"},"raw":{"edit":"homebottom"}} 
[2025-06-19 21:50:59] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:0.93]
  
[2025-06-19 21:51:28] dev.INFO: 
select * from `personal_access_tokens` 
where
 `personal_access_tokens`.`id` = '10' limit 1
  [執行超過10秒:14]
  
[2025-06-19 21:51:28] dev.INFO: 
select * from `adminuser` 
where
 `adminuser`.`id` = '2' limit 1
  [執行超過10秒:2.98]
  
[2025-06-19 21:51:28] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer 10|TipZpZicbv3uXeEUicC1TcL3t1jIAm7rquQx7Alt82cc864a","CONTENT_TYPE":"application/json"},"url":"/api/admin/epost/show","post":{"edit":"homebottom","middlewareurl":"https://allennb.com.tw/admin/api/"},"raw":{"edit":"homebottom"}} 
[2025-06-19 21:51:28] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:2.15]
  
[2025-06-19 21:51:40] dev.INFO: 
select * from `personal_access_tokens` 
where
 `personal_access_tokens`.`id` = '10' limit 1
  [執行超過10秒:24.63]
  
[2025-06-19 21:51:40] dev.INFO: 
select * from `adminuser` 
where
 `adminuser`.`id` = '2' limit 1
  [執行超過10秒:1.64]
  
[2025-06-19 21:51:40] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer 10|TipZpZicbv3uXeEUicC1TcL3t1jIAm7rquQx7Alt82cc864a","CONTENT_TYPE":"application/json"},"url":"/api/admin/epost/store","post":{"epostbody":"歐悅設計隸屬於歐悅集團，成立於1998年，從事辦公室裝修設計工程，成立當年即競圖拿到台灣營建研究院之裝修工程。2000年受知名超商業者委託完成組合式鋼製櫃檯的設計開發導入。2005年轉型為專業商場道具設計與製造服務的公司，2011年成立昆山歐海悅商場設備有限公司，2013年成立誠龍精密鈑金股份有限公司，2016年成立歐悅室內裝修股份有限公司。","edit":"homebottom","type":"text","id":2,"epostid":0,"eposttitle":null,"alg":null,"adminuser_id":null,"useraccount":null,"created_at":null,"updated_at":null,"middlewareurl":"https://allennb.com.tw/admin/api/"},"raw":{"epostbody":"歐悅設計隸屬於歐悅集團，成立於1998年，從事辦公室裝修設計工程，成立當年即競圖拿到台灣營建研究院之裝修工程。2000年受知名超商業者委託完成組合式鋼製櫃檯的設計開發導入。2005年轉型為專業商場道具設計與製造服務的公司，2011年成立昆山歐海悅商場設備有限公司，2013年成立誠龍精密鈑金股份有限公司，2016年成立歐悅室內裝修股份有限公司。","edit":"homebottom","type":"text","id":2,"epostid":0,"eposttitle":null,"alg":null,"adminuser_id":null,"useraccount":null,"created_at":null,"updated_at":null}} 
[2025-06-19 21:51:40] dev.INFO: 
select * from `epost` 
where
 (`epostid` = 'homebottom') limit 1
  [執行超過10秒:0.89]
  
[2025-06-19 21:52:08] dev.INFO: 
select * from `personal_access_tokens` 
where
 `personal_access_tokens`.`id` = '10' limit 1
  [執行超過10秒:35.65]
  
[2025-06-19 21:52:08] dev.INFO: 
select * from `adminuser` 
where
 `adminuser`.`id` = '2' limit 1
  [執行超過10秒:2.72]
  
[2025-06-19 21:52:08] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer 10|TipZpZicbv3uXeEUicC1TcL3t1jIAm7rquQx7Alt82cc864a","CONTENT_TYPE":"application/json"},"url":"/api/admin/epost/show","post":{"edit":"homebottom","middlewareurl":"https://allennb.com.tw/admin/api/"},"raw":{"edit":"homebottom"}} 
[2025-06-19 21:52:08] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:2.02]
  
[2025-06-19 21:52:42] dev.INFO: 
select * from `personal_access_tokens` 
where
 `personal_access_tokens`.`id` = '10' limit 1
  [執行超過10秒:10.31]
  
[2025-06-19 21:52:42] dev.INFO: 
select * from `adminuser` 
where
 `adminuser`.`id` = '2' limit 1
  [執行超過10秒:4.91]
  
[2025-06-19 21:52:42] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer 10|TipZpZicbv3uXeEUicC1TcL3t1jIAm7rquQx7Alt82cc864a","CONTENT_TYPE":"application/json"},"url":"/api/admin/epost/show","post":{"edit":"homebottom","middlewareurl":"https://allennb.com.tw/admin/api/"},"raw":{"edit":"homebottom"}} 
[2025-06-19 21:52:42] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:2.15]
  
[2025-06-19 21:52:52] dev.INFO: 
select * from `personal_access_tokens` 
where
 `personal_access_tokens`.`id` = '10' limit 1
  [執行超過10秒:4.94]
  
[2025-06-19 21:52:52] dev.INFO: 
select * from `adminuser` 
where
 `adminuser`.`id` = '2' limit 1
  [執行超過10秒:1.35]
  
[2025-06-19 21:52:52] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer 10|TipZpZicbv3uXeEUicC1TcL3t1jIAm7rquQx7Alt82cc864a","CONTENT_TYPE":"application/json"},"url":"/api/admin/epost/store","post":{"edit":"homebottom","epostbody":"歐悅設計隸屬於歐悅集團，成立於1998年，從事辦公室裝修設計工程，成立當年即競圖拿到台灣營建研究院之裝修工程。2000年受知名超商業者委託完成組合式鋼製櫃檯的設計開發導入。2005年轉型為專業商場道具設計與製造服務的公司，2011年成立昆山歐海悅商場設備有限公司，2013年成立誠龍精密鈑金股份有限公司，2016年成立歐悅室內裝修股份有限公司。","middlewareurl":"https://allennb.com.tw/admin/api/"},"raw":{"edit":"homebottom","epostbody":"歐悅設計隸屬於歐悅集團，成立於1998年，從事辦公室裝修設計工程，成立當年即競圖拿到台灣營建研究院之裝修工程。2000年受知名超商業者委託完成組合式鋼製櫃檯的設計開發導入。2005年轉型為專業商場道具設計與製造服務的公司，2011年成立昆山歐海悅商場設備有限公司，2013年成立誠龍精密鈑金股份有限公司，2016年成立歐悅室內裝修股份有限公司。"}} 
[2025-06-19 21:52:52] dev.INFO: 
select * from `epost` 
where
 (`epostid` = 'homebottom') limit 1
  [執行超過10秒:0.93]
  
[2025-06-19 21:52:58] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 21:53:00] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 21:53:02] dev.INFO: 
select * from `personal_access_tokens` 
where
 `personal_access_tokens`.`id` = '10' limit 1
  [執行超過10秒:9.68]
  
[2025-06-19 21:53:02] dev.INFO: 
select * from `personal_access_tokens` 
where
 `personal_access_tokens`.`id` = '10' limit 1
  [執行超過10秒:32.1]
  
[2025-06-19 21:53:02] dev.INFO: 
select * from `adminuser` 
where
 `adminuser`.`id` = '2' limit 1
  [執行超過10秒:1.73]
  
[2025-06-19 21:53:02] dev.INFO: 
select * from `adminuser` 
where
 `adminuser`.`id` = '2' limit 1
  [執行超過10秒:1.35]
  
[2025-06-19 21:53:02] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer 10|TipZpZicbv3uXeEUicC1TcL3t1jIAm7rquQx7Alt82cc864a","CONTENT_TYPE":"application/json"},"url":"/api/admin/main","post":{"middlewareurl":"https://allennb.com.tw/admin/api/"},"raw":[]} 
[2025-06-19 21:53:02] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer 10|TipZpZicbv3uXeEUicC1TcL3t1jIAm7rquQx7Alt82cc864a","CONTENT_TYPE":"application/json"},"url":"/api/admin/epost/show","post":{"edit":"homebottom","middlewareurl":"https://allennb.com.tw/admin/api/"},"raw":{"edit":"homebottom"}} 
[2025-06-19 21:53:02] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:1.63]
  
[2025-06-19 21:54:01] dev.INFO: 
select * from `personal_access_tokens` 
where
 `personal_access_tokens`.`id` = '10' limit 1
  [執行超過10秒:27.47]
  
[2025-06-19 21:54:01] dev.INFO: 
select * from `adminuser` 
where
 `adminuser`.`id` = '2' limit 1
  [執行超過10秒:0.91]
  
[2025-06-19 21:54:01] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer 10|TipZpZicbv3uXeEUicC1TcL3t1jIAm7rquQx7Alt82cc864a","CONTENT_TYPE":"application/json"},"url":"/api/admin/board","post":{"search":null,"searchname":null,"searchdatename":null,"page":1,"sortname":null,"sorttype":null,"del":[],"kind":"news","middlewareurl":"https://allennb.com.tw/admin/api/"},"raw":{"search":"","searchname":"","searchdatename":"","page":1,"sortname":"","sorttype":"","del":[],"kind":"news"}} 
[2025-06-19 21:54:01] dev.INFO: 
select count(*) as aggregate from `board`
 left join `kind` on `kind`.`id` = `board`.`kind_id` 
where
 (board.kind = 'news')
  [執行超過10秒:1.29]
  
[2025-06-19 21:54:01] dev.INFO: 
select board.*,kindtitle from `board`
 left join `kind` on `kind`.`id` = `board`.`kind_id` 
where
 (board.kind = 'news')
 order by board.id desc limit 10 offset 0
  [執行超過10秒:1.21]
  
[2025-06-19 21:54:12] dev.INFO: 
select * from `personal_access_tokens` 
where
 `personal_access_tokens`.`id` = '10' limit 1
  [執行超過10秒:14.04]
  
[2025-06-19 21:54:12] dev.INFO: 
select * from `adminuser` 
where
 `adminuser`.`id` = '2' limit 1
  [執行超過10秒:0.75]
  
[2025-06-19 21:54:12] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer 10|TipZpZicbv3uXeEUicC1TcL3t1jIAm7rquQx7Alt82cc864a","CONTENT_TYPE":"application/json"},"url":"/api/admin/epost/show","post":{"edit":"about","middlewareurl":"https://allennb.com.tw/admin/api/"},"raw":{"edit":"about"}} 
[2025-06-19 21:54:12] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:1.01]
  
[2025-06-19 21:54:13] dev.INFO: 
select * from `personal_access_tokens` 
where
 `personal_access_tokens`.`id` = '10' limit 1
  [執行超過10秒:4.27]
  
[2025-06-19 21:54:13] dev.INFO: 
select * from `adminuser` 
where
 `adminuser`.`id` = '2' limit 1
  [執行超過10秒:1.46]
  
[2025-06-19 21:54:13] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer 10|TipZpZicbv3uXeEUicC1TcL3t1jIAm7rquQx7Alt82cc864a","CONTENT_TYPE":"application/json"},"url":"/api/admin/board","post":{"search":null,"searchname":null,"searchdatename":null,"page":1,"sortname":null,"sorttype":null,"del":[],"kind":"aboutpage","middlewareurl":"https://allennb.com.tw/admin/api/"},"raw":{"search":"","searchname":"","searchdatename":"","page":1,"sortname":"","sorttype":"","del":[],"kind":"aboutpage"}} 
[2025-06-19 21:54:13] dev.INFO: 
select count(*) as aggregate from `board`
 left join `kind` on `kind`.`id` = `board`.`kind_id` 
where
 (board.kind = 'aboutpage')
  [執行超過10秒:0.81]
  
[2025-06-19 21:54:13] dev.INFO: 
select board.*,kindtitle from `board`
 left join `kind` on `kind`.`id` = `board`.`kind_id` 
where
 (board.kind = 'aboutpage')
 order by board.id desc limit 10 offset 0
  [執行超過10秒:0.55]
  
[2025-06-19 21:55:38] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 21:55:40] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 21:55:42] dev.INFO: 
select * from `personal_access_tokens` 
where
 `personal_access_tokens`.`id` = '10' limit 1
  [執行超過10秒:24.91]
  
[2025-06-19 21:55:42] dev.INFO: 
select * from `adminuser` 
where
 `adminuser`.`id` = '2' limit 1
  [執行超過10秒:1.29]
  
[2025-06-19 21:55:42] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer 10|TipZpZicbv3uXeEUicC1TcL3t1jIAm7rquQx7Alt82cc864a","CONTENT_TYPE":"application/json"},"url":"/api/admin/board","post":{"search":null,"searchname":null,"searchdatename":null,"page":1,"sortname":null,"sorttype":null,"del":[],"kind":"aboutpage","middlewareurl":"https://allennb.com.tw/admin/api/"},"raw":{"search":"","searchname":"","searchdatename":"","page":1,"sortname":"","sorttype":"","del":[],"kind":"aboutpage"}} 
[2025-06-19 21:55:42] dev.INFO: 
select count(*) as aggregate from `board`
 left join `kind` on `kind`.`id` = `board`.`kind_id` 
where
 (board.kind = 'aboutpage')
  [執行超過10秒:1.34]
  
[2025-06-19 21:55:42] dev.INFO: 
select board.*,kindtitle from `board`
 left join `kind` on `kind`.`id` = `board`.`kind_id` 
where
 (board.kind = 'aboutpage')
 order by board.id desc limit 10 offset 0
  [執行超過10秒:1.5]
  
[2025-06-19 21:55:42] dev.INFO: 
select * from `personal_access_tokens` 
where
 `personal_access_tokens`.`id` = '10' limit 1
  [執行超過10秒:17.92]
  
[2025-06-19 21:55:42] dev.INFO: 
select * from `adminuser` 
where
 `adminuser`.`id` = '2' limit 1
  [執行超過10秒:1.18]
  
[2025-06-19 21:55:42] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer 10|TipZpZicbv3uXeEUicC1TcL3t1jIAm7rquQx7Alt82cc864a","CONTENT_TYPE":"application/json"},"url":"/api/admin/main","post":{"middlewareurl":"https://allennb.com.tw/admin/api/"},"raw":[]} 
[2025-06-19 21:56:01] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 21:56:02] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 21:56:05] dev.INFO: 
select * from `personal_access_tokens` 
where
 `personal_access_tokens`.`id` = '10' limit 1
  [執行超過10秒:20.16]
  
[2025-06-19 21:56:05] dev.INFO: 
select * from `adminuser` 
where
 `adminuser`.`id` = '2' limit 1
  [執行超過10秒:1.58]
  
[2025-06-19 21:56:05] dev.INFO: 
select * from `personal_access_tokens` 
where
 `personal_access_tokens`.`id` = '10' limit 1
  [執行超過10秒:28.25]
  
[2025-06-19 21:56:05] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer 10|TipZpZicbv3uXeEUicC1TcL3t1jIAm7rquQx7Alt82cc864a","CONTENT_TYPE":"application/json"},"url":"/api/admin/board","post":{"search":null,"searchname":null,"searchdatename":null,"page":1,"sortname":null,"sorttype":null,"del":[],"kind":"aboutpage","middlewareurl":"https://allennb.com.tw/admin/api/"},"raw":{"search":"","searchname":"","searchdatename":"","page":1,"sortname":"","sorttype":"","del":[],"kind":"aboutpage"}} 
[2025-06-19 21:56:05] dev.INFO: 
select * from `adminuser` 
where
 `adminuser`.`id` = '2' limit 1
  [執行超過10秒:1.28]
  
[2025-06-19 21:56:05] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer 10|TipZpZicbv3uXeEUicC1TcL3t1jIAm7rquQx7Alt82cc864a","CONTENT_TYPE":"application/json"},"url":"/api/admin/main","post":{"middlewareurl":"https://allennb.com.tw/admin/api/"},"raw":[]} 
[2025-06-19 21:56:05] dev.INFO: 
select count(*) as aggregate from `board`
 left join `kind` on `kind`.`id` = `board`.`kind_id` 
where
 (board.kind = 'aboutpage')
  [執行超過10秒:2.17]
  
[2025-06-19 21:56:05] dev.INFO: 
select board.*,kindtitle from `board`
 left join `kind` on `kind`.`id` = `board`.`kind_id` 
where
 (board.kind = 'aboutpage')
 order by board.id desc limit 10 offset 0
  [執行超過10秒:1.26]
  
[2025-06-19 21:57:10] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 21:57:11] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 21:57:14] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 21:57:14] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:30.65]
  
[2025-06-19 21:57:14] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.37]
  
[2025-06-19 21:57:14] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 21:57:14] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/serviceitem","post":[],"raw":[]} 
[2025-06-19 21:57:14] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/news","post":{"pagesize":6},"raw":{"pagesize":6}} 
[2025-06-19 21:57:14] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:29.08]
  
[2025-06-19 21:57:14] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:1.52]
  
[2025-06-19 21:57:14] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'serviceitem')
  [執行超過10秒:8.16]
  
[2025-06-19 21:57:14] dev.INFO: 
select id,title,field1 as img,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'serviceitem')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.74]
  
[2025-06-19 21:57:14] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":[],"raw":[]} 
[2025-06-19 21:57:14] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
  [執行超過10秒:9.17]
  
[2025-06-19 21:57:14] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate,body from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
 order by boardsort desc limit 6 offset 0
  [執行超過10秒:1.54]
  
[2025-06-19 21:57:14] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:18.89]
  
[2025-06-19 21:57:14] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:2.24]
  
[2025-06-19 21:57:14] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/banner","post":[],"raw":[]} 
[2025-06-19 21:57:14] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'banner')
  [執行超過10秒:22.27]
  
[2025-06-19 21:57:14] dev.INFO: 
select * from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'banner')
 order by boardsort limit 999 offset 0
  [執行超過10秒:1.62]
  
[2025-06-19 21:57:15] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 21:57:15] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:23.37]
  
[2025-06-19 22:01:45] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 22:01:45] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:35.04]
  
[2025-06-19 22:01:45] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":[],"raw":[]} 
[2025-06-19 22:01:45] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:2.52]
  
[2025-06-19 22:01:45] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/banner","post":[],"raw":[]} 
[2025-06-19 22:01:45] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'banner')
  [執行超過10秒:23.96]
  
[2025-06-19 22:01:45] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:25.07]
  
[2025-06-19 22:01:45] dev.INFO: 
select * from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'banner')
 order by boardsort limit 999 offset 0
  [執行超過10秒:1.98]
  
[2025-06-19 22:01:45] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 22:01:45] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:2.77]
  
[2025-06-19 22:01:45] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/news","post":{"pagesize":6},"raw":{"pagesize":6}} 
[2025-06-19 22:01:45] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:22.57]
  
[2025-06-19 22:01:45] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:1.16]
  
[2025-06-19 22:01:45] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
  [執行超過10秒:23.63]
  
[2025-06-19 22:01:45] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate,body from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
 order by boardsort desc limit 6 offset 0
  [執行超過10秒:2.03]
  
[2025-06-19 22:01:46] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/serviceitem","post":[],"raw":[]} 
[2025-06-19 22:01:46] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'serviceitem')
  [執行超過10秒:7.82]
  
[2025-06-19 22:01:46] dev.INFO: 
select id,title,field1 as img,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'serviceitem')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:0.98]
  
[2025-06-19 22:01:46] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 22:01:46] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:31.21]
  
[2025-06-19 22:01:59] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 22:01:59] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:12.33]
  
[2025-06-19 22:01:59] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.91]
  
[2025-06-19 22:01:59] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":[],"raw":[]} 
[2025-06-19 22:01:59] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:22.91]
  
[2025-06-19 22:01:59] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:2.61]
  
[2025-06-19 22:01:59] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 22:01:59] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/serviceitem","post":[],"raw":[]} 
[2025-06-19 22:01:59] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:17.46]
  
[2025-06-19 22:01:59] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:1.82]
  
[2025-06-19 22:01:59] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/news","post":{"pagesize":6},"raw":{"pagesize":6}} 
[2025-06-19 22:01:59] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'serviceitem')
  [執行超過10秒:26.03]
  
[2025-06-19 22:01:59] dev.INFO: 
select id,title,field1 as img,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'serviceitem')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.72]
  
[2025-06-19 22:02:00] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
  [執行超過10秒:26.93]
  
[2025-06-19 22:02:00] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate,body from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
 order by boardsort desc limit 6 offset 0
  [執行超過10秒:1.46]
  
[2025-06-19 22:02:00] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/banner","post":[],"raw":[]} 
[2025-06-19 22:02:00] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'banner')
  [執行超過10秒:24.85]
  
[2025-06-19 22:02:00] dev.INFO: 
select * from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'banner')
 order by boardsort limit 999 offset 0
  [執行超過10秒:1.56]
  
[2025-06-19 22:02:00] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 22:02:00] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:16.76]
  
[2025-06-19 22:02:11] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/news","post":{"pagesize":6},"raw":{"pagesize":6}} 
[2025-06-19 22:02:11] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 22:02:11] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
  [執行超過10秒:20.65]
  
[2025-06-19 22:02:11] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate,body from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
 order by boardsort desc limit 6 offset 0
  [執行超過10秒:1.2]
  
[2025-06-19 22:02:11] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 22:02:11] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/banner","post":[],"raw":[]} 
[2025-06-19 22:02:11] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:23.01]
  
[2025-06-19 22:02:11] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:0.74]
  
[2025-06-19 22:02:11] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'banner')
  [執行超過10秒:25.98]
  
[2025-06-19 22:02:11] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:23.71]
  
[2025-06-19 22:02:11] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/serviceitem","post":[],"raw":[]} 
[2025-06-19 22:02:11] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:0.71]
  
[2025-06-19 22:02:11] dev.INFO: 
select * from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'banner')
 order by boardsort limit 999 offset 0
  [執行超過10秒:1.27]
  
[2025-06-19 22:02:11] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'serviceitem')
  [執行超過10秒:20.31]
  
[2025-06-19 22:02:11] dev.INFO: 
select id,title,field1 as img,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'serviceitem')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.03]
  
[2025-06-19 22:02:12] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":[],"raw":[]} 
[2025-06-19 22:02:12] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:17.53]
  
[2025-06-19 22:02:12] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.49]
  
[2025-06-19 22:02:12] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 22:02:12] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:26.15]
  
[2025-06-19 22:02:40] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 22:02:40] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 22:02:40] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:30.3]
  
[2025-06-19 22:02:40] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.84]
  
[2025-06-19 22:02:40] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/news","post":{"pagesize":6},"raw":{"pagesize":6}} 
[2025-06-19 22:02:40] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:19.64]
  
[2025-06-19 22:02:40] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:2]
  
[2025-06-19 22:02:41] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/serviceitem","post":[],"raw":[]} 
[2025-06-19 22:02:41] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
  [執行超過10秒:28.58]
  
[2025-06-19 22:02:41] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate,body from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
 order by boardsort desc limit 6 offset 0
  [執行超過10秒:1.79]
  
[2025-06-19 22:02:41] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'serviceitem')
  [執行超過10秒:21.05]
  
[2025-06-19 22:02:41] dev.INFO: 
select id,title,field1 as img,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'serviceitem')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.38]
  
[2025-06-19 22:02:41] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/banner","post":[],"raw":[]} 
[2025-06-19 22:02:41] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'banner')
  [執行超過10秒:20.01]
  
[2025-06-19 22:02:41] dev.INFO: 
select * from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'banner')
 order by boardsort limit 999 offset 0
  [執行超過10秒:1.4]
  
[2025-06-19 22:02:41] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":[],"raw":[]} 
[2025-06-19 22:02:41] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:29.87]
  
[2025-06-19 22:02:41] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.61]
  
[2025-06-19 22:02:41] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 22:02:41] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:5.07]
  
[2025-06-19 22:02:52] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":[],"raw":[]} 
[2025-06-19 22:02:52] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 22:02:52] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:11.55]
  
[2025-06-19 22:02:52] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:30.15]
  
[2025-06-19 22:02:52] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:2.64]
  
[2025-06-19 22:02:52] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:2.78]
  
[2025-06-19 22:02:52] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/news","post":{"pagesize":6},"raw":{"pagesize":6}} 
[2025-06-19 22:02:52] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/serviceitem","post":[],"raw":[]} 
[2025-06-19 22:02:53] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'serviceitem')
  [執行超過10秒:30.75]
  
[2025-06-19 22:02:53] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
  [執行超過10秒:31.71]
  
[2025-06-19 22:02:53] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 22:02:53] dev.INFO: 
select id,title,field1 as img,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'serviceitem')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:2.06]
  
[2025-06-19 22:02:53] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate,body from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
 order by boardsort desc limit 6 offset 0
  [執行超過10秒:1.72]
  
[2025-06-19 22:02:53] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:20.19]
  
[2025-06-19 22:02:53] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:2.15]
  
[2025-06-19 22:02:53] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/banner","post":[],"raw":[]} 
[2025-06-19 22:02:53] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'banner')
  [執行超過10秒:16.42]
  
[2025-06-19 22:02:53] dev.INFO: 
select * from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'banner')
 order by boardsort limit 999 offset 0
  [執行超過10秒:1.61]
  
[2025-06-19 22:02:54] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 22:02:54] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:25.85]
  
[2025-06-19 22:03:01] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 22:03:01] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 22:03:01] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:17.17]
  
[2025-06-19 22:03:01] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.72]
  
[2025-06-19 22:03:01] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:22.65]
  
[2025-06-19 22:03:01] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/news","post":{"pagesize":6},"raw":{"pagesize":6}} 
[2025-06-19 22:03:01] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:0.98]
  
[2025-06-19 22:03:01] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
  [執行超過10秒:7.05]
  
[2025-06-19 22:03:01] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate,body from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
 order by boardsort desc limit 6 offset 0
  [執行超過10秒:1.25]
  
[2025-06-19 22:03:01] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/banner","post":[],"raw":[]} 
[2025-06-19 22:03:01] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":[],"raw":[]} 
[2025-06-19 22:03:01] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'banner')
  [執行超過10秒:27.65]
  
[2025-06-19 22:03:01] dev.INFO: 
select * from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'banner')
 order by boardsort limit 999 offset 0
  [執行超過10秒:1.04]
  
[2025-06-19 22:03:01] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:21.31]
  
[2025-06-19 22:03:01] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.03]
  
[2025-06-19 22:03:02] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/serviceitem","post":[],"raw":[]} 
[2025-06-19 22:03:02] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'serviceitem')
  [執行超過10秒:24.22]
  
[2025-06-19 22:03:02] dev.INFO: 
select id,title,field1 as img,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'serviceitem')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.03]
  
[2025-06-19 22:03:02] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 22:03:02] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:26.82]
  
[2025-06-19 22:03:09] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/banner","post":[],"raw":[]} 
[2025-06-19 22:03:09] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 22:03:09] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 22:03:09] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'banner')
  [執行超過10秒:31.94]
  
[2025-06-19 22:03:09] dev.INFO: 
select * from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'banner')
 order by boardsort limit 999 offset 0
  [執行超過10秒:2.27]
  
[2025-06-19 22:03:09] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:34.93]
  
[2025-06-19 22:03:09] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:1.6]
  
[2025-06-19 22:03:09] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":[],"raw":[]} 
[2025-06-19 22:03:09] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:25.54]
  
[2025-06-19 22:03:09] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.66]
  
[2025-06-19 22:03:09] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/news","post":{"pagesize":6},"raw":{"pagesize":6}} 
[2025-06-19 22:03:09] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:18.14]
  
[2025-06-19 22:03:09] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.69]
  
[2025-06-19 22:03:09] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
  [執行超過10秒:18.69]
  
[2025-06-19 22:03:09] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate,body from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
 order by boardsort desc limit 6 offset 0
  [執行超過10秒:1.29]
  
[2025-06-19 22:03:09] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/serviceitem","post":[],"raw":[]} 
[2025-06-19 22:03:10] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'serviceitem')
  [執行超過10秒:20.97]
  
[2025-06-19 22:03:10] dev.INFO: 
select id,title,field1 as img,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'serviceitem')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.08]
  
[2025-06-19 22:03:10] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 22:03:10] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:24.41]
  
[2025-06-19 22:03:30] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 22:03:32] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 22:03:34] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 22:03:34] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/news","post":{"pagesize":6},"raw":{"pagesize":6}} 
[2025-06-19 22:03:34] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:22.6]
  
[2025-06-19 22:03:34] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:2.07]
  
[2025-06-19 22:03:34] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 22:03:34] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
  [執行超過10秒:22.34]
  
[2025-06-19 22:03:34] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate,body from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
 order by boardsort desc limit 6 offset 0
  [執行超過10秒:1.92]
  
[2025-06-19 22:03:34] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":[],"raw":[]} 
[2025-06-19 22:03:34] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:21.83]
  
[2025-06-19 22:03:34] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:1.72]
  
[2025-06-19 22:03:34] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/serviceitem","post":[],"raw":[]} 
[2025-06-19 22:03:34] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:21.44]
  
[2025-06-19 22:03:34] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.96]
  
[2025-06-19 22:03:34] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'serviceitem')
  [執行超過10秒:11.23]
  
[2025-06-19 22:03:34] dev.INFO: 
select id,title,field1 as img,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'serviceitem')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:3.11]
  
[2025-06-19 22:03:35] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/banner","post":[],"raw":[]} 
[2025-06-19 22:03:35] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'banner')
  [執行超過10秒:8.08]
  
[2025-06-19 22:03:35] dev.INFO: 
select * from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'banner')
 order by boardsort limit 999 offset 0
  [執行超過10秒:2.07]
  
[2025-06-19 22:03:36] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 22:03:36] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:10.99]
  
[2025-06-19 22:03:47] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 22:03:47] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:5.6]
  
[2025-06-19 22:03:47] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.11]
  
[2025-06-19 22:04:00] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 22:04:00] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:13.65]
  
[2025-06-19 22:04:00] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.98]
  
[2025-06-19 22:04:01] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":[],"raw":[]} 
[2025-06-19 22:04:01] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:11.43]
  
[2025-06-19 22:04:01] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:2.48]
  
[2025-06-19 22:04:01] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/banner","post":[],"raw":[]} 
[2025-06-19 22:04:01] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 22:04:01] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'banner')
  [執行超過10秒:12.59]
  
[2025-06-19 22:04:01] dev.INFO: 
select * from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'banner')
 order by boardsort limit 999 offset 0
  [執行超過10秒:2.75]
  
[2025-06-19 22:04:01] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'serviceitem')
  [執行超過10秒:13.29]
  
[2025-06-19 22:04:01] dev.INFO: 
select id,title,field1 as img,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'serviceitem')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:2.78]
  
[2025-06-19 22:04:01] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 22:04:01] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:32.83]
  
[2025-06-19 22:04:02] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:2.57]
  
[2025-06-19 22:04:02] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:10.95]
  
[2025-06-19 22:04:02] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/news","post":{"pagesize":6},"raw":{"pagesize":6}} 
[2025-06-19 22:04:02] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
  [執行超過10秒:19.03]
  
[2025-06-19 22:04:02] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate,body from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
 order by boardsort desc limit 6 offset 0
  [執行超過10秒:1.97]
  
[2025-06-19 22:04:04] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 22:04:05] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 22:04:07] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 22:04:07] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 22:04:07] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:30.85]
  
[2025-06-19 22:04:07] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:1.35]
  
[2025-06-19 22:04:07] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 22:04:07] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:15.92]
  
[2025-06-19 22:04:07] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.61]
  
[2025-06-19 22:04:07] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:8.17]
  
[2025-06-19 22:04:07] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 22:04:07] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.82]
  
[2025-06-19 22:04:07] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:25.5]
  
[2025-06-19 22:04:13] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage/show","post":{"id":"10051"},"raw":{"id":"10051"}} 
[2025-06-19 22:04:13] dev.INFO: 
select board.* from `board` 
where
 `kind` = 'aboutpage'
 and `id` = '10051' limit 1
  [執行超過10秒:30.77]
  
[2025-06-19 22:05:17] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 22:05:17] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 22:05:17] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:11.75]
  
[2025-06-19 22:05:17] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.88]
  
[2025-06-19 22:05:17] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:10.7]
  
[2025-06-19 22:05:17] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.77]
  
[2025-06-19 22:05:17] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 22:05:17] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:29.12]
  
[2025-06-19 22:05:25] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/news","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 22:05:25] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
  [執行超過10秒:21.62]
  
[2025-06-19 22:05:25] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate,body from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:0.85]
  
[2025-06-19 22:05:30] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 22:05:30] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:12.62]
  
[2025-06-19 22:05:30] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 22:05:30] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:2.79]
  
[2025-06-19 22:05:30] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 22:05:30] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:12.95]
  
[2025-06-19 22:05:30] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:2.59]
  
[2025-06-19 22:05:30] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:12.22]
  
[2025-06-19 22:06:37] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 22:06:39] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 22:06:42] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 22:06:42] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 22:06:42] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:14]
  
[2025-06-19 22:06:42] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:2.11]
  
[2025-06-19 22:06:43] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 22:06:43] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 22:06:43] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:15.55]
  
[2025-06-19 22:06:43] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:3.32]
  
[2025-06-19 22:06:43] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 22:06:43] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:13.01]
  
[2025-06-19 22:06:43] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:27.13]
  
[2025-06-19 22:06:43] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:3.32]
  
[2025-06-19 22:06:43] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 22:06:43] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:16.97]
  
[2025-06-19 22:06:43] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:3.09]
  
[2025-06-19 22:06:44] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 22:06:44] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:25.74]
  
[2025-06-19 22:06:45] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 22:06:49] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/serviceitem","post":[],"raw":[]} 
[2025-06-19 22:06:49] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'serviceitem')
  [執行超過10秒:15.07]
  
[2025-06-19 22:06:49] dev.INFO: 
select id,title,field1 as img,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'serviceitem')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:2.49]
  
[2025-06-19 22:06:49] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/banner","post":[],"raw":[]} 
[2025-06-19 22:06:49] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 22:06:49] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'banner')
  [執行超過10秒:15.58]
  
[2025-06-19 22:06:49] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 22:06:49] dev.INFO: 
select * from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'banner')
 order by boardsort limit 999 offset 0
  [執行超過10秒:3.15]
  
[2025-06-19 22:06:49] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:21.27]
  
[2025-06-19 22:06:49] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:17.22]
  
[2025-06-19 22:06:49] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:2.84]
  
[2025-06-19 22:06:49] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":[],"raw":[]} 
[2025-06-19 22:06:49] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:2.55]
  
[2025-06-19 22:06:49] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:12.13]
  
[2025-06-19 22:06:49] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:2.53]
  
[2025-06-19 22:06:50] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/news","post":{"pagesize":6},"raw":{"pagesize":6}} 
[2025-06-19 22:06:50] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
  [執行超過10秒:15.1]
  
[2025-06-19 22:06:50] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate,body from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
 order by boardsort desc limit 6 offset 0
  [執行超過10秒:3.75]
  
[2025-06-19 22:06:51] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 22:06:51] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:14.66]
  
[2025-06-19 22:07:13] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 22:12:59] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 22:13:00] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 22:13:02] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 22:13:02] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:18.97]
  
[2025-06-19 22:13:02] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.31]
  
[2025-06-19 22:13:02] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":[],"raw":[]} 
[2025-06-19 22:13:02] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/news","post":{"pagesize":6},"raw":{"pagesize":6}} 
[2025-06-19 22:13:02] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/serviceitem","post":[],"raw":[]} 
[2025-06-19 22:13:02] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 22:13:02] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:30.7]
  
[2025-06-19 22:13:02] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.42]
  
[2025-06-19 22:13:02] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
  [執行超過10秒:18.62]
  
[2025-06-19 22:13:02] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:7.66]
  
[2025-06-19 22:13:02] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate,body from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
 order by boardsort desc limit 6 offset 0
  [執行超過10秒:1.47]
  
[2025-06-19 22:13:02] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:1.32]
  
[2025-06-19 22:13:02] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'serviceitem')
  [執行超過10秒:27.16]
  
[2025-06-19 22:13:02] dev.INFO: 
select id,title,field1 as img,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'serviceitem')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.78]
  
[2025-06-19 22:13:03] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/banner","post":[],"raw":[]} 
[2025-06-19 22:13:03] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'banner')
  [執行超過10秒:10.54]
  
[2025-06-19 22:13:03] dev.INFO: 
select * from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'banner')
 order by boardsort limit 999 offset 0
  [執行超過10秒:1.82]
  
[2025-06-19 22:13:04] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 22:13:04] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:12.76]
  
[2025-06-19 22:13:07] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 22:13:07] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 22:13:07] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:27.09]
  
[2025-06-19 22:13:07] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.6]
  
[2025-06-19 22:13:07] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:22]
  
[2025-06-19 22:13:09] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 22:13:09] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:25.81]
  
[2025-06-19 22:13:09] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:2.7]
  
[2025-06-19 22:14:48] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 22:14:50] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 22:14:54] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 22:14:54] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 22:14:54] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:16.04]
  
[2025-06-19 22:14:54] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 22:14:54] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 22:14:54] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:14.28]
  
[2025-06-19 22:14:54] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:2.86]
  
[2025-06-19 22:14:54] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:11.18]
  
[2025-06-19 22:14:54] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:15.12]
  
[2025-06-19 22:14:54] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 22:14:54] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:2.59]
  
[2025-06-19 22:14:54] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:2.53]
  
[2025-06-19 22:14:54] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:22.38]
  
[2025-06-19 22:14:57] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 22:14:57] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:16.05]
  
[2025-06-19 22:14:57] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:3.19]
  
[2025-06-19 23:34:13] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 23:34:14] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 23:34:16] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:34:16] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/banner","post":[],"raw":[]} 
[2025-06-19 23:34:16] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:15.42]
  
[2025-06-19 23:34:16] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:0.66]
  
[2025-06-19 23:34:16] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":[],"raw":[]} 
[2025-06-19 23:34:16] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'banner')
  [執行超過10秒:26.71]
  
[2025-06-19 23:34:16] dev.INFO: 
select * from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'banner')
 order by boardsort limit 999 offset 0
  [執行超過10秒:1.15]
  
[2025-06-19 23:34:16] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:34:16] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/news","post":{"pagesize":6},"raw":{"pagesize":6}} 
[2025-06-19 23:34:16] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:18.33]
  
[2025-06-19 23:34:16] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:0.66]
  
[2025-06-19 23:34:16] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:4.26]
  
[2025-06-19 23:34:16] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:0.51]
  
[2025-06-19 23:34:16] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
  [執行超過10秒:25.22]
  
[2025-06-19 23:34:16] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate,body from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'news')
 order by boardsort desc limit 6 offset 0
  [執行超過10秒:0.73]
  
[2025-06-19 23:34:16] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/serviceitem","post":[],"raw":[]} 
[2025-06-19 23:34:16] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'serviceitem')
  [執行超過10秒:13.53]
  
[2025-06-19 23:34:16] dev.INFO: 
select id,title,field1 as img,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'serviceitem')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:0.91]
  
[2025-06-19 23:34:17] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 23:34:17] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:5.07]
  
[2025-06-19 23:34:20] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:34:20] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:4.16]
  
[2025-06-19 23:34:20] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:0.71]
  
[2025-06-19 23:34:20] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 23:34:20] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:20.69]
  
[2025-06-19 23:34:21] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:34:21] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:24.25]
  
[2025-06-19 23:34:21] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:0.33]
  
[2025-06-19 23:35:38] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:35:38] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 23:35:38] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:21.5]
  
[2025-06-19 23:35:38] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:0.91]
  
[2025-06-19 23:35:38] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:21.9]
  
[2025-06-19 23:35:39] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:35:39] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:15.26]
  
[2025-06-19 23:35:39] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:0.76]
  
[2025-06-19 23:36:13] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 23:36:14] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 23:36:16] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:36:16] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:17.03]
  
[2025-06-19 23:36:16] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:1.29]
  
[2025-06-19 23:36:16] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:36:16] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:25.35]
  
[2025-06-19 23:36:16] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.15]
  
[2025-06-19 23:36:16] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 23:36:16] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:36:16] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:23.09]
  
[2025-06-19 23:36:16] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 23:36:16] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:29.27]
  
[2025-06-19 23:36:16] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.15]
  
[2025-06-19 23:36:17] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:21.52]
  
[2025-06-19 23:36:18] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:36:18] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:15.2]
  
[2025-06-19 23:36:18] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.21]
  
[2025-06-19 23:37:43] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:37:43] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:12.32]
  
[2025-06-19 23:37:43] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:2.43]
  
[2025-06-19 23:37:43] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 23:37:43] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:13.26]
  
[2025-06-19 23:37:46] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:37:46] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:6.24]
  
[2025-06-19 23:37:46] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:0.94]
  
[2025-06-19 23:37:54] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:37:55] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:14.67]
  
[2025-06-19 23:37:55] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:2.61]
  
[2025-06-19 23:37:55] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 23:37:55] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:13.12]
  
[2025-06-19 23:37:57] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:37:57] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:27.15]
  
[2025-06-19 23:37:57] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.22]
  
[2025-06-19 23:38:50] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 23:38:51] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 23:38:53] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:38:53] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:38:53] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:7.93]
  
[2025-06-19 23:38:53] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.07]
  
[2025-06-19 23:38:53] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:21.33]
  
[2025-06-19 23:38:53] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:1.54]
  
[2025-06-19 23:38:53] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:38:53] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 23:38:53] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:22.53]
  
[2025-06-19 23:38:53] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.67]
  
[2025-06-19 23:38:53] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 23:38:53] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:33.07]
  
[2025-06-19 23:38:53] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:21.76]
  
[2025-06-19 23:38:55] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:38:55] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:6.26]
  
[2025-06-19 23:38:55] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.52]
  
[2025-06-19 23:39:45] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 23:39:46] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 23:39:49] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:39:49] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:12.02]
  
[2025-06-19 23:39:49] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:2.57]
  
[2025-06-19 23:39:49] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:39:49] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 23:39:49] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:13]
  
[2025-06-19 23:39:49] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:2.18]
  
[2025-06-19 23:39:49] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:27.43]
  
[2025-06-19 23:39:49] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:39:49] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:37.69]
  
[2025-06-19 23:39:49] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:3.24]
  
[2025-06-19 23:39:49] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 23:39:49] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:16.39]
  
[2025-06-19 23:39:54] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 23:39:56] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 23:39:58] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:39:58] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 23:39:58] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:39:58] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:30.62]
  
[2025-06-19 23:39:58] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.29]
  
[2025-06-19 23:39:58] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:19.4]
  
[2025-06-19 23:39:58] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:21.87]
  
[2025-06-19 23:39:58] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:39:58] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:1.93]
  
[2025-06-19 23:39:58] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 23:39:58] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:11.54]
  
[2025-06-19 23:39:58] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.69]
  
[2025-06-19 23:39:58] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:24.09]
  
[2025-06-19 23:40:01] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:40:01] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:33.3]
  
[2025-06-19 23:40:01] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.84]
  
[2025-06-19 23:40:08] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 23:40:09] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 23:40:11] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:40:11] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:22.84]
  
[2025-06-19 23:40:11] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.31]
  
[2025-06-19 23:40:11] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:40:11] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 23:40:11] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:25.86]
  
[2025-06-19 23:40:11] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:1.61]
  
[2025-06-19 23:40:11] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:40:11] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:9.67]
  
[2025-06-19 23:40:11] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 23:40:11] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:32.45]
  
[2025-06-19 23:40:11] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.73]
  
[2025-06-19 23:40:11] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:9.38]
  
[2025-06-19 23:40:14] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:40:14] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:15.13]
  
[2025-06-19 23:40:14] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:2.51]
  
[2025-06-19 23:40:28] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 23:40:29] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 23:40:32] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 23:40:32] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:40:32] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:13.01]
  
[2025-06-19 23:40:32] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:40:32] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:30.74]
  
[2025-06-19 23:40:32] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:2.13]
  
[2025-06-19 23:40:32] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:40:32] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:27.57]
  
[2025-06-19 23:40:32] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:2.49]
  
[2025-06-19 23:40:32] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:10.1]
  
[2025-06-19 23:40:32] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.49]
  
[2025-06-19 23:40:32] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 23:40:32] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:13.24]
  
[2025-06-19 23:40:34] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:40:34] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:20]
  
[2025-06-19 23:40:34] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.93]
  
[2025-06-19 23:41:44] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 23:41:45] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 23:41:47] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:41:47] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:6.61]
  
[2025-06-19 23:41:47] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.28]
  
[2025-06-19 23:41:47] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:41:47] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 23:41:47] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:8.09]
  
[2025-06-19 23:41:47] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:1.39]
  
[2025-06-19 23:41:47] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:23.03]
  
[2025-06-19 23:41:47] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 23:41:47] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:41:47] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:28.96]
  
ATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:19.02]
  
[2025-06-19 23:41:47] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.52]
  
[2025-06-19 23:41:50] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:41:50] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:8.49]
  
[2025-06-19 23:41:50] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.51]
  
[2025-06-19 23:42:11] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 23:42:12] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 23:42:14] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:42:15] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:42:15] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:28.16]
  
[2025-06-19 23:42:15] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.98]
  
[2025-06-19 23:42:15] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:42:15] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:13.45]
  
[2025-06-19 23:42:15] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:2.82]
  
[2025-06-19 23:42:15] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 23:42:15] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:13.4]
  
[2025-06-19 23:42:15] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:2.48]
  
[2025-06-19 23:42:15] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 23:42:15] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:11.04]
  
[2025-06-19 23:42:15] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:12.3]
  
[2025-06-19 23:42:17] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:42:17] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:23.34]
  
[2025-06-19 23:42:17] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.12]
  
[2025-06-19 23:42:49] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 23:42:50] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 23:42:52] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:42:52] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:42:52] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:7.48]
  
[2025-06-19 23:42:52] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.46]
  
[2025-06-19 23:42:52] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:6.19]
  
[2025-06-19 23:42:52] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:1.7]
  
[2025-06-19 23:42:52] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 23:42:52] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 23:42:52] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:9.8]
  
[2025-06-19 23:42:52] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:42:52] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:20.51]
  
[2025-06-19 23:42:52] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:17.3]
  
[2025-06-19 23:42:52] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.49]
  
[2025-06-19 23:42:55] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:42:55] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:15.21]
  
[2025-06-19 23:42:55] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:2.56]
  
[2025-06-19 23:43:12] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 23:43:13] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 23:43:15] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:43:15] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 23:43:15] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:43:15] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:9.32]
  
[2025-06-19 23:43:15] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:2.03]
  
[2025-06-19 23:43:15] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 23:43:15] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:33.46]
  
[2025-06-19 23:43:15] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:35.64]
  
[2025-06-19 23:43:15] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.76]
  
[2025-06-19 23:43:15] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:28.44]
  
[2025-06-19 23:43:16] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:43:16] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:19.06]
  
[2025-06-19 23:43:16] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:0.79]
  
[2025-06-19 23:43:18] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:43:18] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:26.6]
  
[2025-06-19 23:43:18] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.68]
  
[2025-06-19 23:43:24] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 23:43:26] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 23:43:28] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:43:28] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 23:43:28] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:29.14]
  
[2025-06-19 23:43:28] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:2.19]
  
[2025-06-19 23:43:28] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:43:28] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:10.35]
  
[2025-06-19 23:43:28] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 23:43:28] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:43:28] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:33.73]
  
[2025-06-19 23:43:28] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.96]
  
[2025-06-19 23:43:28] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:29.31]
  
[2025-06-19 23:43:28] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:33.68]
  
[2025-06-19 23:43:28] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:2.67]
  
[2025-06-19 23:43:31] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:43:31] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:34.99]
  
[2025-06-19 23:43:31] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:2.39]
  
[2025-06-19 23:43:37] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 23:43:38] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 23:43:41] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:43:41] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:43:41] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:10.98]
  
[2025-06-19 23:43:41] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:2.15]
  
[2025-06-19 23:43:41] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:11.55]
  
[2025-06-19 23:43:41] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:1.73]
  
[2025-06-19 23:43:41] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 23:43:41] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:43:41] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:29.49]
  
[2025-06-19 23:43:41] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 23:43:41] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:34.2]
  
[2025-06-19 23:43:41] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:2.32]
  
[2025-06-19 23:43:41] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:28.58]
  
[2025-06-19 23:43:43] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:43:43] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:16.63]
  
[2025-06-19 23:43:43] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.33]
  
[2025-06-19 23:45:36] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 23:45:38] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 23:45:40] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:45:40] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:33.21]
  
[2025-06-19 23:45:40] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.64]
  
[2025-06-19 23:45:40] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:45:40] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 23:45:40] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:23.3]
  
[2025-06-19 23:45:40] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:1.89]
  
[2025-06-19 23:45:40] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 23:45:40] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:45:40] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:29.74]
  
[2025-06-19 23:45:40] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:11.74]
  
[2025-06-19 23:45:40] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:34.48]
  
[2025-06-19 23:45:40] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.62]
  
[2025-06-19 23:45:43] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:45:43] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:23.14]
  
[2025-06-19 23:45:43] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.83]
  
[2025-06-19 23:47:14] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:47:14] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 23:47:14] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:47:14] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 23:47:14] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:21.13]
  
[2025-06-19 23:47:14] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:2.45]
  
[2025-06-19 23:47:14] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:17.52]
  
[2025-06-19 23:47:14] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:2.67]
  
[2025-06-19 23:47:14] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:17.6]
  
[2025-06-19 23:47:14] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:47:14] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:14.15]
  
[2025-06-19 23:47:14] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:11.52]
  
[2025-06-19 23:47:14] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:3.25]
  
[2025-06-19 23:47:16] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:47:16] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:65.47]
  
[2025-06-19 23:47:16] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:3.08]
  
[2025-06-19 23:47:25] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:47:25] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:47:25] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:32.57]
  
[2025-06-19 23:47:25] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:2.92]
  
[2025-06-19 23:47:25] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 23:47:25] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:13.14]
  
[2025-06-19 23:47:25] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:2.54]
  
[2025-06-19 23:47:25] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:47:25] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:22.18]
  
[2025-06-19 23:47:25] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:12.77]
  
[2025-06-19 23:47:25] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:2.87]
  
[2025-06-19 23:47:25] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 23:47:25] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:11.47]
  
[2025-06-19 23:47:27] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:47:27] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:26.17]
  
[2025-06-19 23:47:27] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:0.85]
  
[2025-06-19 23:48:02] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:48:02] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:20.39]
  
[2025-06-19 23:48:02] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:2.2]
  
[2025-06-19 23:48:02] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 23:48:02] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:48:02] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:24.46]
  
[2025-06-19 23:48:02] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:48:02] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:21.2]
  
[2025-06-19 23:48:02] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.45]
  
[2025-06-19 23:48:02] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 23:48:02] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:22.58]
  
[2025-06-19 23:48:02] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:1.97]
  
[2025-06-19 23:48:02] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:21.54]
  
[2025-06-19 23:48:04] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:48:04] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:29.2]
  
[2025-06-19 23:48:04] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.55]
  
[2025-06-19 23:48:22] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:48:22] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 23:48:22] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:48:22] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:48:22] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 23:48:22] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:13.36]
  
[2025-06-19 23:48:22] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:34.4]
  
[2025-06-19 23:48:22] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:2.26]
  
[2025-06-19 23:48:22] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:2.59]
  
[2025-06-19 23:48:22] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:34.06]
  
[2025-06-19 23:48:22] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:33.75]
  
[2025-06-19 23:48:22] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:2.32]
  
[2025-06-19 23:48:22] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:13.49]
  
[2025-06-19 23:48:24] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:48:24] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:17.28]
  
[2025-06-19 23:48:24] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:0.86]
  
[2025-06-19 23:48:46] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 23:48:47] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 23:48:49] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:48:49] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:8.37]
  
[2025-06-19 23:48:49] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:1.77]
  
[2025-06-19 23:48:49] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:48:49] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:48:49] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:7.94]
  
[2025-06-19 23:48:49] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.58]
  
[2025-06-19 23:48:49] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 23:48:49] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:7.91]
  
[2025-06-19 23:48:49] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.38]
  
[2025-06-19 23:48:49] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 23:48:49] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:10.09]
  
[2025-06-19 23:48:49] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:30.26]
  
[2025-06-19 23:48:51] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:48:51] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:19.17]
  
[2025-06-19 23:48:51] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.31]
  
[2025-06-19 23:49:46] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:49:46] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:49:46] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:11.76]
  
[2025-06-19 23:49:46] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:2.21]
  
[2025-06-19 23:49:46] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 23:49:46] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 23:49:46] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:12.01]
  
[2025-06-19 23:49:46] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:2.55]
  
[2025-06-19 23:49:46] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:11.78]
  
[2025-06-19 23:49:46] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:20.99]
  
[2025-06-19 23:49:46] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:49:46] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:28.74]
  
[2025-06-19 23:49:46] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.35]
  
[2025-06-19 23:49:48] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:49:48] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:20.58]
  
[2025-06-19 23:49:48] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.09]
  
[2025-06-19 23:49:56] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:49:56] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:15.71]
  
[2025-06-19 23:49:56] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:2.95]
  
[2025-06-19 23:49:56] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 23:49:56] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 23:49:56] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:15.1]
  
[2025-06-19 23:49:56] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:49:56] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:11.97]
  
[2025-06-19 23:49:56] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:49:56] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:33.87]
  
[2025-06-19 23:49:56] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.78]
  
[2025-06-19 23:49:56] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:24.29]
  
[2025-06-19 23:49:56] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:2.24]
  
[2025-06-19 23:49:58] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:49:58] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:21.36]
  
[2025-06-19 23:49:58] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:0.97]
  
[2025-06-19 23:50:10] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:50:10] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:29.3]
  
[2025-06-19 23:50:10] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:2.1]
  
[2025-06-19 23:50:10] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 23:50:10] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:50:10] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:34.3]
  
[2025-06-19 23:50:10] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:12.05]
  
[2025-06-19 23:50:10] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:1.82]
  
[2025-06-19 23:50:10] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 23:50:10] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:35.1]
  
[2025-06-19 23:50:10] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:50:10] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:32.73]
  
[2025-06-19 23:50:10] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:2.53]
  
[2025-06-19 23:50:12] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:50:12] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:29.5]
  
[2025-06-19 23:50:12] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:0.88]
  
[2025-06-19 23:52:05] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 23:52:07] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 23:52:09] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:52:09] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:21.29]
  
[2025-06-19 23:52:09] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:1.28]
  
[2025-06-19 23:52:09] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:52:09] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:19.99]
  
[2025-06-19 23:52:09] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.41]
  
[2025-06-19 23:52:09] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 23:52:09] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"about"},"raw":{"id":"about"}} 
[2025-06-19 23:52:09] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:25.11]
  
[2025-06-19 23:52:09] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'about' limit 1
  [執行超過10秒:26.97]
  
[2025-06-19 23:52:09] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/service","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:52:09] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
  [執行超過10秒:22.68]
  
[2025-06-19 23:52:09] dev.INFO: 
select id,title,field2 as youtube,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'service')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.41]
  
[2025-06-19 23:52:11] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/customer","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:52:11] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
  [執行超過10秒:18.33]
  
[2025-06-19 23:52:11] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'customer')
 order by boardsort desc limit 10 offset 0
  [執行超過10秒:1.22]
  
[2025-06-19 23:52:56] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/case","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:52:56] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'case')
  [執行超過10秒:25.62]
  
[2025-06-19 23:52:56] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'case')
 order by boardsort  limit 10 offset 0
  [執行超過10秒:1.47]
  
[2025-06-19 23:53:15] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/case","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:53:15] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'case')
  [執行超過10秒:27]
  
[2025-06-19 23:53:15] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'case')
 order by boardsort  limit 10 offset 0
  [執行超過10秒:1.54]
  
[2025-06-19 23:53:35] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:53:35] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:14.88]
  
[2025-06-19 23:53:35] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:3.65]
  
[2025-06-19 23:53:35] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 23:53:35] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:53:35] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/case","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:53:35] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:38.24]
  
[2025-06-19 23:53:35] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'case')
  [執行超過10秒:18.3]
  
[2025-06-19 23:53:35] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'case')
 order by boardsort  limit 10 offset 0
  [執行超過10秒:2.73]
  
[2025-06-19 23:53:35] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:35.82]
  
[2025-06-19 23:53:35] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:3.34]
  
[2025-06-19 23:53:54] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:53:54] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:53:54] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:34.93]
  
[2025-06-19 23:53:54] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:2.25]
  
[2025-06-19 23:53:54] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:24.29]
  
[2025-06-19 23:53:54] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:2.62]
  
[2025-06-19 23:53:54] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 23:53:54] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:9.78]
  
[2025-06-19 23:53:54] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/case","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:53:54] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'case')
  [執行超過10秒:27.48]
  
[2025-06-19 23:53:54] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'case')
 order by boardsort  limit 10 offset 0
  [執行超過10秒:1.72]
  
[2025-06-19 23:54:10] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 23:54:12] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 23:54:14] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:54:14] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:8.05]
  
[2025-06-19 23:54:14] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.57]
  
[2025-06-19 23:54:14] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/case","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:54:14] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 23:54:14] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'case')
  [執行超過10秒:19.66]
  
[2025-06-19 23:54:14] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'case')
 order by boardsort  limit 10 offset 0
  [執行超過10秒:1.61]
  
[2025-06-19 23:54:14] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:54:14] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:18.82]
  
[2025-06-19 23:54:14] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:16.98]
  
[2025-06-19 23:54:14] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:0.98]
  
[2025-06-19 23:56:08] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:56:08] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:56:08] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:28.45]
  
[2025-06-19 23:56:08] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:2.75]
  
[2025-06-19 23:56:08] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/case","post":{"search":null,"page":1,"sortname":null,"sorttype":null},"raw":{"search":"","page":1,"sortname":"","sorttype":""}} 
[2025-06-19 23:56:08] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:17.82]
  
[2025-06-19 23:56:08] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:3.31]
  
[2025-06-19 23:56:08] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'case')
  [執行超過10秒:14.59]
  
[2025-06-19 23:56:08] dev.INFO: 
select id,title,field1 as img,body,created_at,begindate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'case')
 order by boardsort  limit 10 offset 0
  [執行超過10秒:2.91]
  
[2025-06-19 23:56:08] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 23:56:08] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:28.92]
  
[2025-06-19 23:56:21] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 23:56:21] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:56:21] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:36.39]
  
[2025-06-19 23:56:21] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:56:22] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:12.94]
  
[2025-06-19 23:56:22] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:2.2]
  
[2025-06-19 23:56:22] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:11.81]
  
[2025-06-19 23:56:22] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:2.52]
  
[2025-06-19 23:56:39] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 23:56:40] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 23:56:43] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:56:43] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:10.86]
  
[2025-06-19 23:56:43] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:56:43] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:1.65]
  
[2025-06-19 23:56:43] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:10.63]
  
[2025-06-19 23:56:43] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:1.9]
  
[2025-06-19 23:56:43] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 23:56:43] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:11.2]
  
[2025-06-19 23:57:02] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/config","post":[],"raw":[]} 
[2025-06-19 23:57:04] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/setup","post":[],"raw":[]} 
[2025-06-19 23:57:06] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/product","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:57:06] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/aboutpage","post":{"title":null},"raw":{"title":""}} 
[2025-06-19 23:57:06] dev.INFO: 
select count(*) as aggregate from `product`
  [執行超過10秒:25.72]
  
[2025-06-19 23:57:06] dev.INFO: 
select count(*) as aggregate from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
  [執行超過10秒:27.97]
  
[2025-06-19 23:57:06] dev.INFO: 
select product.* from `product`
 order by product.id desc limit 10 offset 0
  [執行超過10秒:0.95]
  
[2025-06-19 23:57:06] dev.INFO: 
select id,title from `board` 
where
 convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE()
 and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()
 and (kind = 'aboutpage')
 order by boardsort limit 10 offset 0
  [執行超過10秒:0.78]
  
[2025-06-19 23:57:06] dev.INFO: API Request: {"header":{"HTTP_Authorization":"Bearer ","CONTENT_TYPE":"application/json"},"url":"/api/epost/show","post":{"id":"homebottom"},"raw":{"id":"homebottom"}} 
[2025-06-19 23:57:06] dev.INFO: 
select epost.* from `epost` 
where
 `epostid` = 'homebottom' limit 1
  [執行超過10秒:22.73]
  
