import{_ as q}from"./jBNyKXjo.js";import{d as U,f as I,g as D,h as B,i as C,r as b,j as c,k as F,c as N,o as v,l,w as L,v as S,e as j,B as $,m as r,b as s,n as d,E as H,C as M,t as R,H as O,I as T,F as z,y as f}from"./D_zVjEfm.js";const A={class:"form-group row"},G={class:"col-md-10"},J={class:"form-group row"},K={class:"col-md-10"},P={class:"form-group row"},Q={class:"col-md-10"},W={class:"form-group row"},X={class:"col-md-10"},Y={class:"form-group row"},Z={class:"col-md-10"},h={class:"form-group row"},ee={class:"col-md-10"},se={class:"form-group row"},te={class:"col-md-10"},oe={class:"form-group row"},le={class:"col-md-10"},ae={class:"form-group row"},de={class:"col-md-10"},re={align:"center"},me=U({__name:"edit",props:{edit:{default:""}},emits:["closed-dialog"],setup(y,{emit:w}){const u=I();D(),B(),C();const g=w,p=y,m=b();b([]);const t=c({});c({x:""}),c({});const _=async()=>{try{const a=await u.post("api/admin/feedback/show",{id:p.edit});if(a.resultcode=="0")Object.assign(t,a.data);else throw new Error(a.resultmessage)}catch(a){f.message(a.message),console.error(a)}},V=async()=>{if(m.value)try{if(!await m.value.validate())return;const e=await u.post("api/admin/feedback/store",t);if(e.resultcode=="0")f.toast(e.resultmessage),g("closed-dialog",!0);else throw new Error(e.resultmessage)}catch(a){console.error(a),f.formElError(a)}};return F(async()=>{typeof p.edit<"u"&&p.edit!=""&&await _()}),(a,e)=>{const x=q,n=M,i=H,E=T,k=S;return v(),N(z,null,[l(x,{id:"feedback"}),L((v(),$(E,{ref_key:"formEl",ref:m,style:{width:"98%"},model:t,onSubmit:O(V,["prevent"])},{default:r(()=>[s("div",A,[e[10]||(e[10]=s("label",{class:"col-md-2"},[d("姓名： "),s("span",{class:"text-danger p-1"}," * ")],-1)),s("div",G,[l(i,{prop:"name",rules:[{required:!0,message:"姓名 未填"}]},{default:r(()=>[l(n,{modelValue:t.name,"onUpdate:modelValue":e[0]||(e[0]=o=>t.name=o),type:"text"},null,8,["modelValue"])]),_:1})])]),s("div",J,[e[11]||(e[11]=s("label",{class:"col-md-2"},[d("電子信箱： "),s("span",{class:"text-danger p-1"}," * ")],-1)),s("div",K,[l(i,{prop:"email",rules:[{required:!0,message:"電子信箱 未填"},{type:"email",message:"電子信箱 格式錯誤"}]},{default:r(()=>[l(n,{modelValue:t.email,"onUpdate:modelValue":e[1]||(e[1]=o=>t.email=o),type:"email",placeholder:"ex <EMAIL>"},null,8,["modelValue"])]),_:1})])]),s("div",P,[e[12]||(e[12]=s("label",{class:"col-md-2"},[d("LINE ID： "),s("span",{class:"text-danger p-1"})],-1)),s("div",Q,[l(i,{prop:"lineid",rules:[{required:!1,message:"LINE ID 未填"}]},{default:r(()=>[l(n,{modelValue:t.lineid,"onUpdate:modelValue":e[2]||(e[2]=o=>t.lineid=o),type:"text"},null,8,["modelValue"])]),_:1})])]),s("div",W,[e[13]||(e[13]=s("label",{class:"col-md-2"},[d("行動電話： "),s("span",{class:"text-danger p-1"})],-1)),s("div",X,[l(i,{prop:"mobile",rules:[{required:!1,message:"行動電話 未填"},{pattern:/09\d{2}(\d{6}|-\d{3}-\d{3})/,message:"行動電話 格式錯誤"}]},{default:r(()=>[l(n,{modelValue:t.mobile,"onUpdate:modelValue":e[3]||(e[3]=o=>t.mobile=o),type:"tel",placeholder:"ex 09123456789"},null,8,["modelValue"])]),_:1})])]),s("div",Y,[e[14]||(e[14]=s("label",{class:"col-md-2"},[d("標題： "),s("span",{class:"text-danger p-1"}," * ")],-1)),s("div",Z,[l(i,{prop:"title",rules:[{required:!0,message:"標題 未填"}]},{default:r(()=>[l(n,{modelValue:t.title,"onUpdate:modelValue":e[4]||(e[4]=o=>t.title=o),type:"text"},null,8,["modelValue"])]),_:1})])]),s("div",h,[e[15]||(e[15]=s("label",{class:"col-md-2"},[d("留言內容： "),s("span",{class:"text-danger p-1"})],-1)),s("div",ee,[l(i,{prop:"memo",rules:[{required:!1,message:"留言內容 未填"}]},{default:r(()=>[l(n,{modelValue:t.memo,"onUpdate:modelValue":e[5]||(e[5]=o=>t.memo=o),style:{width:"98%"},rows:6,"show-word-limit":"",type:"textarea"},null,8,["modelValue"])]),_:1})])]),s("div",se,[e[16]||(e[16]=s("label",{class:"col-md-2"},[d("回覆標題： "),s("span",{class:"text-danger p-1"})],-1)),s("div",te,[l(i,{prop:"retitle",rules:[{required:!1,message:"回覆標題 未填"}]},{default:r(()=>[l(n,{modelValue:t.retitle,"onUpdate:modelValue":e[6]||(e[6]=o=>t.retitle=o),type:"text"},null,8,["modelValue"])]),_:1})])]),s("div",oe,[e[17]||(e[17]=s("label",{class:"col-md-2"},[d("回覆訊息： "),s("span",{class:"text-danger p-1"})],-1)),s("div",le,[l(i,{prop:"rebody",rules:[{required:!1,message:"回覆訊息 未填"}]},{default:r(()=>[l(n,{modelValue:t.rebody,"onUpdate:modelValue":e[7]||(e[7]=o=>t.rebody=o),style:{width:"98%"},rows:6,"show-word-limit":"",type:"textarea"},null,8,["modelValue"])]),_:1})])]),s("div",ae,[e[18]||(e[18]=s("label",{class:"col-md-2"},[d("回覆日期： "),s("span",{class:"text-danger p-1"})],-1)),s("div",de,R(t.redate),1)]),s("div",re,[e[19]||(e[19]=s("button",{type:"submit",class:"btn btn-primary"},"回覆",-1)),e[20]||(e[20]=d("   ")),s("button",{type:"reset",class:"btn btn-secondary",onClick:e[8]||(e[8]=o=>m.value.resetFields())},"取消"),e[21]||(e[21]=d("   ")),s("button",{type:"button",class:"btn btn-secondary",onClick:e[9]||(e[9]=o=>g("closed-dialog",!1))},"返回")])]),_:1},8,["model"])),[[k,j(u).getLoading()]])],64)}}});export{me as default};
