import{d as z,f as C,g as L,h as P,i as $,r as g,j as u,Q as j,k as E,w as N,v as D,e as S,c as s,o as i,b as t,l as _,m as V,n as B,p as q,F as f,q as h,x as F,a3 as M,s as R,t as v,z as T,y as H}from"./D_zVjEfm.js";import O from"./HHVucgb9.js";const Q={class:"breadcrumb-area"},U={class:"container"},W={class:"row"},A={class:"col-12"},G={"aria-label":"breadcrumb"},J={class:"breadcrumb"},K={class:"breadcrumb-item"},X={class:"alazea-portfolio-area portfolio-page section-padding-0-100"},Y={class:"container"},Z={class:"row alazea-portfolio"},I={class:"portfolio-hover-overlay"},tt=["href","title"],et={key:0,class:"port-hover-text"},ot={class:"row"},at={class:"col-12"},rt=z({__name:"index",props:{},setup(st){const d=C();L(),P(),$(),g("");const n=u({data:[],total:0});g("");const r=u({search:"",page:1,sortname:"",sorttype:""}),p=()=>{T(()=>{const e=window.$;e&&e.fn.magnificPopup&&(e.fn.imagesLoaded&&e.fn.isotope&&e(".alazea-portfolio").imagesLoaded(function(){e(".alazea-portfolio").isotope({itemSelector:".single_portfolio_item",percentPosition:!0,masonry:{columnWidth:".single_portfolio_item"}})}),n.data.forEach((o,l)=>{e(`.single_portfolio_item:eq(${l}) [class*="portfolio-img"]`).magnificPopup({gallery:{enabled:!0},type:"image"})}),e(".video-icon").magnificPopup({type:"iframe"}))})};function b(e){r.page=e,m()}const m=async()=>{try{const e=await d.post("api/case",r);if(e.resultcode=="0")Object.assign(n,e.data),p();else throw new Error(e.resultmessage)}catch(e){H.formElError(e),console.error(e)}};return j(()=>n.data,()=>{p()},{deep:!0}),E(async()=>{await m()}),(e,o)=>{const l=q,y=O,w=D;return N((i(),s("div",null,[t("div",Q,[o[3]||(o[3]=t("div",{class:"top-breadcrumb-area bg-img bg-overlay d-flex align-items-center justify-content-center",style:{"background-image":"url(/img/bg-img/cases_bg.jpg)"}},[t("h2",null,"精選案例")],-1)),t("div",U,[t("div",W,[t("div",A,[t("nav",G,[t("ol",J,[t("li",K,[_(l,{to:{path:"/"}},{default:V(()=>o[1]||(o[1]=[B("首頁")])),_:1})]),o[2]||(o[2]=t("li",{class:"breadcrumb-item active"},"精選案例",-1))])])])])])]),t("section",X,[t("div",Y,[t("div",Z,[(i(!0),s(f,null,h(n.data,(a,x)=>(i(),s("div",{key:x,class:"col-12 col-sm-6 col-lg-3 single_portfolio_item design"},[t("div",{class:"portfolio-thumbnail bg-img",style:F(`background-image: url(/images/case/${a.img.split(",")[0]});`)},null,4),t("div",I,[(i(!0),s(f,null,h(a.img.split(","),(k,c)=>(i(),s("a",{key:c,href:`/images/case/${k}`,class:M(`portfolio-img0${c+1} d-flex align-items-center justify-content-center`),title:a.title},[c===0?(i(),s("div",et,[t("h3",null,v(a.title),1),t("h5",null,v(a.body),1)])):R("",!0)],10,tt))),128))])]))),128))]),t("div",ot,[t("div",at,[_(y,{total:n.total,page:r.page,"onUpdate:page":o[0]||(o[0]=a=>r.page=a),onCurrentChange:b},null,8,["total","page"])])])])])])),[[w,S(d).getLoading()]])}}});export{rt as default};
