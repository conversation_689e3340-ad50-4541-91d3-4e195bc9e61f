<template>
    <my-breadcrumb :id="inputs.edit"></my-breadcrumb>

    <!--// TODO : 前端資料填寫-->
    <el-form ref="formEl" :model="inputs" v-loading="http.getLoading()" @submit.prevent="onSubmit">
        <el-form-item
            label="內容"
            prop="epostbody"
            :rules="[
                {
                    required: false,
                    message: '內容 未填'
                }
            ]"
        >
            <!-- 根據 type 參數顯示不同的編輯器 -->
            <template v-if="inputs.type == 'text'">
                <el-input v-model="inputs.epostbody" style="width: 98%" type="textarea" :rows="30" placeholder="請輸入內容" />
            </template>
            <template v-else>
                <my-ckeditor v-model="inputs.epostbody" />
            </template>
        </el-form-item>

        <div align="center">
            <button class="btn btn-primary">確定</button>

            &nbsp;
        </div>
    </el-form>
</template>

<script setup lang="ts">
import type { FormInstance } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const http = createHttp() //http套件

const router = useRouter()
const route = useRoute()

const props = defineProps({
    edit: {
        type: String,
        default: ''
        // required: true
    },
    type: {
        type: String,
        default: 'html' // 預設為 html 編輯器
    }
})

// 使用 reactive 定義對象
const datas = ref([])
const inputs = reactive({
    epostbody: '',
    edit: (route.query.edit as string) || '',
    type: (route.query.type as string) || props.type
})

//const templetedata = reactive({});
const getData = async (): Promise<void> => {
    try {
        inputs.epostbody = ''
        const rep = (await http.post('api/admin/epost/show', {
            edit: route.query?.edit
        })) as ApiResponse

        if (rep.resultcode == '0') {
            Object.assign(inputs, rep.data)
            // Object.keys(rep.data).forEach(key => {
            //     inputs[key] = rep.data[key]
            // })
            //console.log(["inputs", inputs]);
            //console.log(["inputs", inputs]);
        } else {
            //throw new Error(rep.resultmessage)
            //utils.alert(rep.resultmessage)
        }
    } catch (error: any) {
        utils.alert(error.message || error)
        console.error(error)
    } finally {
    }
}

watch(
    () => route.fullPath,
    () => {
        inputs.edit = (route.query?.edit as string) || ''
        inputs.type = (route.query?.type as string) || props.type
        getData()
    }
)

const formEl = ref<FormInstance>()

const onSubmit = async (): Promise<void> => {
    if (!formEl.value) return
    if (inputs.edit == '') {
        return
    }
    try {
        const valid = await formEl.value.validate()
        if (valid) {
            try {
                // 準備要發送的資料，排除 type 參數（因為它只用於前端控制）
                const submitData = {
                    edit: inputs.edit,
                    epostbody: inputs.epostbody
                }
                const rep = (await http.post('api/admin/epost/store', submitData)) as ApiResponse
                //debugger;
                if (rep.resultcode == '0') {
                    utils.toast(rep.resultmessage)
                } else {
                    throw new Error(rep.resultmessage)
                }
            } catch (error: any) {
                utils.formElError(error)
                console.error(error)
            }
        }
    } catch (error) {
        console.error('Validation error:', error)
    }
}

onMounted(async () => {
    inputs.edit = (route.query.edit as string) || ''
    inputs.type = (route.query.type as string) || props.type
    getData()
})
</script>
