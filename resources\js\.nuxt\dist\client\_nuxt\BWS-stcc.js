import{d as f,i as u,a7 as o,a2 as s,y as i,a8 as m}from"./D_zVjEfm.js";const d=f({__name:"my-seo",props:{defaultTitle:String,title:[Object,String],keyword:[Object,String],description:[Object,String],image:String,url:String,type:String},setup(a){const n=u(),e=a,c=o(()=>{var r;try{let t=e.title;return s(e.title)&&(t=e.title.value),i.isEmpty(t)&&i.isEmpty(e.defaultTitle)?n.config.title:!i.isEmpty(t)&&!i.isEmpty(e.defaultTitle)?t+" | "+e.defaultTitle+" | "+n.config.title:i.isEmpty(t)&&!i.isEmpty(e.defaultTitle)?e.defaultTitle+" | "+n.config.title:i.isEmpty(t)?n.config.title:t+" | "+n.config.title}catch{return(r=n.config)==null?void 0:r.title}}),l=o(()=>{var r;try{let t=e.description;return s(e.description)&&(t=e.description.value),i.isEmpty(t)&&(t=n.config.description),i.isEmpty(t)||(t=i.noHtml(t)),t}catch{return(r=n.config)==null?void 0:r.description}}),p=o(()=>(i.isEmpty(e.keyword),n.config.keyword));return m({title:c,meta:[{name:"keywords",content:p},{name:"description",content:l},{name:"og:title",content:c},{name:"og:description",content:l},{name:"og:image",content:e.image||""},{name:"og:url",content:e.url||""},{name:"og:type",content:e.type||"website"}],link:[{rel:"canonical",href:e.url||""}]}),(r,t)=>null}});export{d as _};
