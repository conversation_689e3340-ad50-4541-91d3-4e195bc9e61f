import{d as U,f as B,i as q,r as I,j as $,a7 as A,k as F,c as m,o as n,l as h,aj as P,m as i,b as f,s as y,a9 as H,F as p,B as b,e as N,C as K,ah as L,ai as M,n as C,q as V,_ as R}from"./D_zVjEfm.js";import{_ as T}from"./G4Mf9Qin.js";const z={class:"scroll-container"},G={class:"col-auto"},J=U({__name:"my-search",props:{searchNames:{type:Object,required:!1},searchDateNames:{type:Object,required:!1}},emits:["onSearch"],setup(c,{expose:D,emit:_}){const x=B(),k=q(),r=c;let u=I([]);const a=$({search:"",searchname:"",searchdatename:"",searchstartdate:"",searchenddate:""}),g=A({get(){return a.search},set(s){a.search=s}}),E=_;function j(){if(a.search!==""&&a.searchname===""){let s=Object.keys(r.searchNames||[]).join("^");s=s.replace(/^\^+|\^+$/g,""),a.searchname=s}E("onSearch",a),a.searchname.indexOf("^")>-1&&(a.searchname="")}const S=async()=>{try{if(!r.searchNames){console.error("searchNames is undefined");return}const s=r.searchNames[a.searchname];let e=[];if(typeof k.setup[s]<"u"&&(e=k.setup[s].KIND||[]),a.search="",u.value=[],e.length>0)Array.isArray(e)?(e.forEach(l=>{l.傳回值===""?l.value=l.資料:l.value=l.傳回值,l.label=l.資料}),u.value=e):console.error("labels is not an array");else{const l=await x.post("api/admin/dependentdropdown",{label:s},!1);if(l.resultcode==0)l.data.forEach(o=>{u.value.push({value:String(Object.values(o)[0]),label:String(Object.values(o)[1]||Object.values(o)[0])})});else throw new Error(l.resultmessage)}}catch(s){console.error(s)}};return F(()=>{r.searchDateNames&&(a.searchdatename=Object.keys(r.searchDateNames).length>0?Object.keys(r.searchDateNames)[0]:""),r.searchNames&&(a.searchname=Object.keys(r.searchNames).length>0?Object.keys(r.searchNames)[0]:"")}),D({inputs:a}),(s,e)=>{const l=K,o=M,v=L,O=T,w=P;return n(),m("div",z,[h(w,{class:"scroll-content"},{default:i(()=>[f("div",G,[c.searchNames&&Object.keys(c.searchNames).length>0?(n(),m(p,{key:0},[N(u).length==0?(n(),b(l,{key:0,placeholder:"Search..",modelValue:a.search,"onUpdate:modelValue":e[0]||(e[0]=t=>a.search=t)},null,8,["modelValue"])):y("",!0),N(u).length>0?(n(),b(v,{key:1,placeholder:"Select",modelValue:g.value,"onUpdate:modelValue":e[1]||(e[1]=t=>g.value=t)},{default:i(()=>[h(o,{value:""},{default:i(()=>e[6]||(e[6]=[C("Select")])),_:1}),(n(!0),m(p,null,V(N(u),(t,d)=>(n(),b(o,{key:t.id,value:t.value,label:t.label},null,8,["value","label"]))),128))]),_:1},8,["modelValue"])):y("",!0),h(v,{placeholder:"Select",modelValue:a.searchname,"onUpdate:modelValue":e[2]||(e[2]=t=>a.searchname=t),onChange:S},{default:i(()=>[(n(!0),m(p,null,V(c.searchNames,(t,d)=>(n(),b(o,{key:d,value:d,label:t},null,8,["value","label"]))),128))]),_:1},8,["modelValue"]),f("button",{type:"button",class:"btn btn-primary btn-sm",onClick:j},e[7]||(e[7]=[f("i",{class:"fa fa-search"},null,-1)]))],64)):y("",!0),c.searchDateNames&&Object.keys(c.searchDateNames).length>0?(n(),m(p,{key:1},[h(v,{modelValue:a.searchdatename,"onUpdate:modelValue":e[3]||(e[3]=t=>a.searchdatename=t),onChange:S},{default:i(()=>[(n(!0),m(p,null,V(c.searchDateNames,(t,d)=>(n(),b(o,{key:d,value:d,label:t},null,8,["value","label"]))),128))]),_:1},8,["modelValue"]),h(O,{modelValue:a.searchstartdate,"onUpdate:modelValue":e[4]||(e[4]=t=>a.searchstartdate=t),placeholder:"Pick a day",type:"date"},null,8,["modelValue"]),e[9]||(e[9]=C(" ~ ")),h(O,{modelValue:a.searchenddate,"onUpdate:modelValue":e[5]||(e[5]=t=>a.searchenddate=t),placeholder:"Pick a day",type:"date"},null,8,["modelValue"]),f("button",{type:"button",class:"btn btn-primary btn-sm",onClick:j},e[8]||(e[8]=[f("i",{class:"fa fa-search"},null,-1)]))],64)):y("",!0),H(s.$slots,"default",{},void 0,!0)])]),_:3})])}}}),X=R(J,[["__scopeId","data-v-1fdf73fe"]]);export{X as default};
