---
description:
globs:
alwaysApply: false
---
# Laravel PHPUnit 測試規範指南

## 適用範圍
- 適用於所有 Laravel 專案的 PHPUnit 測試生成
- 當需求涉及 Laravel 元素（模型、控制器、遷移檔等）時，必須遵循此規範

## 任務目標
- 確保所有控制器都有對應的測試檔案，遵循專案的測試規範

## 執行步驟
1. 讀取並理解 `.cursor\rules\phptest.mdc` 中定義的測試規範
2. 掃描 `app\Http\Controllers\` 目錄及其所有子目錄中的控制器檔案
3. 對每個控制器檔案，檢查 `tests\Feature\` 目錄中是否存在對應的測試檔案
   - 控制器路徑對應關係：
     - `app/Http/Controllers/api` → `tests/Feature/api`
     - `app/Http/Controllers/api/admin` → `tests/Feature/api/admin`
4. 若控制器沒有對應的測試檔案，則根據 `phptest.mdc` 中的規則產生新的測試檔案：
   - 遵循命名規範（小駝峰式，如：`userTest`）
   - 繼承正確的 `baseTest` 類別
   - 為每個控制器方法建立對應的測試方法
   - 包含必要的測試檢查（JSON/HTML 檢查、狀態碼檢查、資料檢查等）

## 輸出要求
- 列出所有缺少測試檔案的控制器
- 為每個缺少測試的控制器產生符合規範的測試檔案

- 提供測試檔案的完整路徑和內容