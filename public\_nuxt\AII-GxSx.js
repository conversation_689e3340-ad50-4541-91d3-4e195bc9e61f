import{r as e}from"./D_zVjEfm.js";const r=e([{label:"訊息種類",name:"kindid",kind:"my-xmlform",props:{type:"select",xpath:"訊息種類"},rules:[{required:!0,message:"種類 未填"}],value:"",isedit:!0,islist:!0,listname:"kindtitle",issearch:!0},{label:"標題",name:"title",kind:"el-input",rules:[{required:!0,message:"標題 未填"}],value:"",isedit:!0,islist:!0,issearch:!0},{label:"圖",name:"field1",kind:"my-upload",props:{width:"380",height:"280",folder:"images/news"},rules:[{required:!1,message:"圖 未上傳"}],value:"",isedit:!0,islist:!1},{label:"本文",name:"body",kind:"my-ckeditor",props:{},rules:[{required:!0,message:"本文 未填"}],value:"",isedit:!0,islist:!1,issearch:!0},{label:"本文",name:"body",kind:"el-input",props:{type:"textarea",rows:10},rules:[{required:!0,message:"本文 未填"}],value:"",isedit:!0,islist:!1,issearch:!0},{label:"連結",name:"memo",kind:"el-input",props:{kind:"url",placeholder:"https://www.yahoo.com.tw"},rules:[{required:!1,message:"連結 未填"}],value:"",isedit:!0,islist:!1},{label:"開始日期",name:"begindate",kind:"my-dateform",props:{type:"date"},rules:[{required:!1,message:"建立日期 未填"}],value:"",isedit:!0,islist:!0,issearch:!0},{label:"點閱數",name:"hits",kind:"el-input",props:{},rules:[{kind:"number"}],value:"",isedit:!1,islist:!0,issearch:!1},{label:"結束日期",name:"closedate",kind:"my-dateform",props:{type:"date"},rules:[{required:!1,message:"建立日期 未填"}],value:"",isedit:!0,islist:!0,issearch:!0},{label:"順序",name:"boardsort",kind:"el-input",props:{type:"number"},rules:[{required:!1,message:"順序 未填"}],value:"",isedit:!0,islist:!0,issearch:!1,memo:"數字小到大"},{label:"建立日期",name:"created_at",kind:"my-dateform",props:{type:"date"},rules:[{required:!1,message:"建立日期 未填"}],value:"",islist:!0,issearch:!0}]);export{r as fields};
